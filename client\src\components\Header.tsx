import { useState } from 'react';
import { Menu, X, ArrowRight } from 'lucide-react';
import PdfAlertBanner from "./PdfAlertBanner";
import SectionLink from './SectionLink';

const Header = () => {
  const [menuOpen, setMenuOpen] = useState(false);

  const toggleMenu = () => {
    setMenuOpen(!menuOpen);
  };

  return (
    <header className="bg-white shadow-sm fixed w-full z-50 h-auto">
      <PdfAlertBanner />
      <div className="container py-2 md:py-3 flex justify-between items-center">
        <div className="flex items-center pointer-events-none">
          <div className="h-16 sm:h-24 md:h-40 w-32 sm:w-48 md:w-80 select-none logo-container" style={{ border: 'none', outline: 'none', boxShadow: 'none' }}>
            <img
              src="/logo.png"
              alt="Free Energy"
              className="h-full w-full object-contain outline-none border-none pointer-events-none select-none"
              style={{
                userSelect: 'none',
                WebkitUserDrag: 'none',
                pointerEvents: 'none',
                border: 'none',
                outline: 'none',
                boxShadow: 'none'
              }}
              draggable="false"
            />
          </div>
        </div>
        <nav className="hidden md:flex items-center space-x-6">
          <SectionLink targetId="como-funciona" className="text-neutral-900 hover:text-[#2ECC71] transition-colors font-medium">
            Como Funciona
          </SectionLink>
          <SectionLink targetId="solucoes" className="text-neutral-900 hover:text-[#2ECC71] transition-colors font-medium">
            Soluções
          </SectionLink>
          <SectionLink targetId="simulador" className="text-neutral-900 hover:text-[#2ECC71] transition-colors font-medium">
            Simulador
          </SectionLink>
          <SectionLink targetId="cases" className="text-neutral-900 hover:text-[#2ECC71] transition-colors font-medium">
            Cases
          </SectionLink>
          <SectionLink targetId="sobre-nos" className="text-neutral-900 hover:text-[#2ECC71] transition-colors font-medium">
            Sobre Nós
          </SectionLink>
          <SectionLink targetId="/blog" className="text-neutral-900 hover:text-[#2ECC71] transition-colors font-medium" isExternalLink={true}>
            Blog
          </SectionLink>
        </nav>
        <SectionLink
          targetId="/solutions"
          className="hidden md:flex items-center gap-2 bg-[#FFC107] text-yellow-800 px-4 py-2 rounded-lg font-semibold hover:bg-[#F9A825] shadow-md"
          isExternalLink={true}
          style={{ border: 'none', outline: 'none' }}
        >
          Contato <ArrowRight className="h-4 w-4" />
        </SectionLink>
        <button
          className="md:hidden text-neutral-900"
          onClick={toggleMenu}
        >
          {menuOpen ? <X className="h-6 w-6" /> : <Menu className="h-6 w-6" />}
        </button>
      </div>

      {/* Mobile menu */}
      {menuOpen && (
        <div className="md:hidden bg-white w-full absolute shadow-lg">
          <div className="container py-3 flex flex-col space-y-4">
            <SectionLink
              targetId="como-funciona"
              className="text-neutral-900 hover:text-[#2ECC71] transition-colors font-medium py-2"
              onClick={() => setMenuOpen(false)}
            >
              Como Funciona
            </SectionLink>
            <SectionLink
              targetId="solucoes"
              className="text-neutral-900 hover:text-[#2ECC71] transition-colors font-medium py-2"
              onClick={() => setMenuOpen(false)}
            >
              Soluções
            </SectionLink>
            <SectionLink
              targetId="simulador"
              className="text-neutral-900 hover:text-[#2ECC71] transition-colors font-medium py-2"
              onClick={() => setMenuOpen(false)}
            >
              Simulador
            </SectionLink>
            <SectionLink
              targetId="cases"
              className="text-neutral-900 hover:text-[#2ECC71] transition-colors font-medium py-2"
              onClick={() => setMenuOpen(false)}
            >
              Cases
            </SectionLink>
            <SectionLink
              targetId="sobre-nos"
              className="text-neutral-900 hover:text-[#2ECC71] transition-colors font-medium py-2"
              onClick={() => setMenuOpen(false)}
            >
              Sobre Nós
            </SectionLink>
            <SectionLink
              targetId="/blog"
              className="text-neutral-900 hover:text-[#2ECC71] transition-colors font-medium py-2"
              onClick={() => setMenuOpen(false)}
              isExternalLink={true}
            >
              Blog
            </SectionLink>
            <SectionLink
              targetId="/solutions"
              className="flex items-center justify-center gap-2 bg-[#FFC107] text-yellow-800 px-4 py-2 rounded-lg font-semibold hover:bg-[#F9A825] shadow-md w-full"
              onClick={() => setMenuOpen(false)}
              isExternalLink={true}
            >
              Contato <ArrowRight className="h-4 w-4" />
            </SectionLink>
          </div>
        </div>
      )}
    </header>
  );
};

export default Header;
