import { useState } from 'react';
import { Menu, X, ArrowRight } from 'lucide-react';
import PdfAlertBanner from "./PdfAlertBanner";
import SectionLink from './SectionLink';

const Header = () => {
  const [menuOpen, setMenuOpen] = useState(false);

  const toggleMenu = () => {
    setMenuOpen(!menuOpen);
  };

  return (
    <header
      className="bg-white fixed w-full"
      style={{
        zIndex: 'var(--z-fixed)',
        boxShadow: 'var(--shadow-sm)',
        borderBottom: '1px solid var(--border)'
      }}
    >
      <PdfAlertBanner />
      <div className="container flex justify-between items-center" style={{ padding: 'var(--space-md) var(--space-md)' }}>
        <div className="flex items-center">
          <div
            style={{
              height: '3rem',
              width: 'auto',
              border: 'none',
              outline: 'none',
              boxShadow: 'none'
            }}
          >
            <img
              src="/logo.png"
              alt="Free Energy"
              style={{
                height: '100%',
                width: 'auto',
                objectFit: 'contain',
                userSelect: 'none',
                WebkitUserDrag: 'none',
                pointerEvents: 'none',
                border: 'none',
                outline: 'none',
                boxShadow: 'none'
              }}
              draggable="false"
            />
          </div>
        </div>
        <nav className="hidden md:flex items-center gap-xl">
          <SectionLink
            targetId="como-funciona"
            className="font-medium"
            style={{
              color: 'var(--text-secondary)',
              fontSize: 'var(--font-size-base)',
              textDecoration: 'none',
              transition: 'var(--transition-fast)',
              padding: 'var(--space-xs) 0'
            }}
          >
            Como Funciona
          </SectionLink>
          <SectionLink
            targetId="solucoes"
            className="font-medium"
            style={{
              color: 'var(--text-secondary)',
              fontSize: 'var(--font-size-base)',
              textDecoration: 'none',
              transition: 'var(--transition-fast)',
              padding: 'var(--space-xs) 0'
            }}
          >
            Soluções
          </SectionLink>
          <SectionLink
            targetId="simulador"
            className="font-medium"
            style={{
              color: 'var(--text-secondary)',
              fontSize: 'var(--font-size-base)',
              textDecoration: 'none',
              transition: 'var(--transition-fast)',
              padding: 'var(--space-xs) 0'
            }}
          >
            Simulador
          </SectionLink>
          <SectionLink
            targetId="cases"
            className="font-medium"
            style={{
              color: 'var(--text-secondary)',
              fontSize: 'var(--font-size-base)',
              textDecoration: 'none',
              transition: 'var(--transition-fast)',
              padding: 'var(--space-xs) 0'
            }}
          >
            Cases
          </SectionLink>
          <SectionLink
            targetId="sobre-nos"
            className="font-medium"
            style={{
              color: 'var(--text-secondary)',
              fontSize: 'var(--font-size-base)',
              textDecoration: 'none',
              transition: 'var(--transition-fast)',
              padding: 'var(--space-xs) 0'
            }}
          >
            Sobre Nós
          </SectionLink>
          <SectionLink
            targetId="/blog"
            className="font-medium"
            isExternalLink={true}
            style={{
              color: 'var(--text-secondary)',
              fontSize: 'var(--font-size-base)',
              textDecoration: 'none',
              transition: 'var(--transition-fast)',
              padding: 'var(--space-xs) 0'
            }}
          >
            Blog
          </SectionLink>
        </nav>
        <SectionLink
          targetId="/solutions"
          className="hidden md:flex items-center btn btn-secondary"
          isExternalLink={true}
          style={{
            textDecoration: 'none',
            gap: 'var(--space-sm)'
          }}
        >
          Contato <ArrowRight className="h-4 w-4" />
        </SectionLink>
        <button
          className="md:hidden flex items-center justify-center"
          onClick={toggleMenu}
          style={{
            padding: 'var(--space-sm)',
            borderRadius: 'var(--radius-md)',
            background: 'transparent',
            border: 'none',
            color: 'var(--text-secondary)',
            cursor: 'pointer',
            transition: 'var(--transition-fast)'
          }}
          onMouseEnter={(e) => {
            e.currentTarget.style.background = 'var(--surface)';
            e.currentTarget.style.color = 'var(--primary)';
          }}
          onMouseLeave={(e) => {
            e.currentTarget.style.background = 'transparent';
            e.currentTarget.style.color = 'var(--text-secondary)';
          }}
        >
          {menuOpen ? <X className="h-6 w-6" /> : <Menu className="h-6 w-6" />}
        </button>
      </div>

      {/* Mobile menu */}
      {menuOpen && (
        <div
          className="md:hidden bg-white w-full absolute"
          style={{
            boxShadow: 'var(--shadow-lg)',
            borderTop: '1px solid var(--border)'
          }}
        >
          <div
            className="container flex flex-col gap-lg"
            style={{
              padding: 'var(--space-lg) var(--space-md)'
            }}
          >
            <SectionLink
              targetId="como-funciona"
              className="font-medium"
              onClick={() => setMenuOpen(false)}
              style={{
                color: 'var(--text-secondary)',
                fontSize: 'var(--font-size-lg)',
                textDecoration: 'none',
                transition: 'var(--transition-fast)',
                padding: 'var(--space-sm) 0'
              }}
            >
              Como Funciona
            </SectionLink>
            <SectionLink
              targetId="solucoes"
              className="font-medium"
              onClick={() => setMenuOpen(false)}
              style={{
                color: 'var(--text-secondary)',
                fontSize: 'var(--font-size-lg)',
                textDecoration: 'none',
                transition: 'var(--transition-fast)',
                padding: 'var(--space-sm) 0'
              }}
            >
              Soluções
            </SectionLink>
            <SectionLink
              targetId="simulador"
              className="font-medium"
              onClick={() => setMenuOpen(false)}
              style={{
                color: 'var(--text-secondary)',
                fontSize: 'var(--font-size-lg)',
                textDecoration: 'none',
                transition: 'var(--transition-fast)',
                padding: 'var(--space-sm) 0'
              }}
            >
              Simulador
            </SectionLink>
            <SectionLink
              targetId="cases"
              className="font-medium"
              onClick={() => setMenuOpen(false)}
              style={{
                color: 'var(--text-secondary)',
                fontSize: 'var(--font-size-lg)',
                textDecoration: 'none',
                transition: 'var(--transition-fast)',
                padding: 'var(--space-sm) 0'
              }}
            >
              Cases
            </SectionLink>
            <SectionLink
              targetId="sobre-nos"
              className="font-medium"
              onClick={() => setMenuOpen(false)}
              style={{
                color: 'var(--text-secondary)',
                fontSize: 'var(--font-size-lg)',
                textDecoration: 'none',
                transition: 'var(--transition-fast)',
                padding: 'var(--space-sm) 0'
              }}
            >
              Sobre Nós
            </SectionLink>
            <SectionLink
              targetId="/blog"
              className="font-medium"
              onClick={() => setMenuOpen(false)}
              isExternalLink={true}
              style={{
                color: 'var(--text-secondary)',
                fontSize: 'var(--font-size-lg)',
                textDecoration: 'none',
                transition: 'var(--transition-fast)',
                padding: 'var(--space-sm) 0'
              }}
            >
              Blog
            </SectionLink>
            <SectionLink
              targetId="/solutions"
              className="btn btn-secondary text-center"
              onClick={() => setMenuOpen(false)}
              isExternalLink={true}
              style={{
                textDecoration: 'none',
                gap: 'var(--space-sm)',
                marginTop: 'var(--space-md)',
                width: '100%',
                justifyContent: 'center'
              }}
            >
              Contato <ArrowRight className="h-4 w-4" />
            </SectionLink>
          </div>
        </div>
      )}
    </header>
  );
};

export default Header;
