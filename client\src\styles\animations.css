/**
 * Arquivo de animações otimizadas para todo o site
 * Este arquivo garante que todas as animações e transições sejam fluidas e consistentes
 * e que não haja efeitos de zoom indesejados nas imagens
 */

/* Otimização de performance para animações */
* {
  will-change: auto;
  backface-visibility: hidden;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Transições suaves para todos os elementos interativos */
a, button, .btn, .card, .case-card, .solution-card, .blog-card,
.shadow-lg, .shadow-xl, .rounded-xl, .rounded-lg, .rounded-md {
  transition: transform 0.25s cubic-bezier(0.2, 0, 0.2, 1),
              box-shadow 0.25s cubic-bezier(0.2, 0, 0.2, 1),
              background-color 0.25s ease,
              color 0.25s ease;
  will-change: transform, box-shadow;
}

/* Efeito de hover para elementos interativos - mais suave */
a:hover, button:hover, .btn:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 20px -5px rgba(0, 0, 0, 0.1);
}

.card:hover, .case-card:hover, .solution-card:hover, .blog-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1);
}

/* Regras específicas para imagens - sem zoom, com transições suaves */
img {
  transition: opacity 0.25s ease;
  transform: none !important;
  will-change: opacity;
}

/* Garantir que imagens dentro de containers não sofram zoom */
*:hover > img {
  transform: none !important;
}

/* Regras específicas para containers com overflow hidden */
.overflow-hidden {
  overflow: hidden;
}

.overflow-hidden img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transform: none !important;
}

.overflow-hidden:hover img {
  transform: none !important;
}

/* Animações para cards específicos - mais fluidas */
.case-card, .solution-card, .blog-card {
  overflow: hidden;
  transition: transform 0.25s cubic-bezier(0.2, 0, 0.2, 1),
              box-shadow 0.25s cubic-bezier(0.2, 0, 0.2, 1);
  will-change: transform, box-shadow;
}

.case-card:hover, .solution-card:hover, .blog-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 30px rgba(46, 204, 113, 0.15);
}

/* Garantir que não haja zoom em imagens dentro de cards */
.case-card img, .solution-card img, .blog-card img {
  transition: opacity 0.25s ease;
  transform: none !important;
}

.case-card:hover img, .solution-card:hover img, .blog-card:hover img {
  transform: none !important;
}

/* Animações para ícones - mais suaves */
.icon, .case-icon, [class*='icon'] {
  transition: transform 0.25s cubic-bezier(0.2, 0, 0.2, 1);
  will-change: transform;
}

.card:hover .icon, .case-card:hover .case-icon, *:hover > [class*='icon'] {
  transform: translateY(-2px);
}

/* Otimização para vídeos */
video, iframe, .video-container, .video-container-wrapper {
  will-change: transform;
  transform: translateZ(0);
  border: none !important;
  outline: none !important;
}

/* Estilos específicos para iframes do YouTube */
iframe[src*="youtube.com"] {
  border: none !important;
  outline: none !important;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1) !important;
  background-color: #000 !important;
  display: block !important;
  width: 100% !important;
  height: 100% !important;
}

/* Contêiner específico para vídeos do YouTube */
.video-container {
  position: relative;
  overflow: hidden;
  border-radius: 0.75rem;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1) !important;
  background-color: #000;
  transform: translateZ(0);
  will-change: transform;
}

/* Garantir que o iframe do YouTube ocupe todo o espaço */
.video-container iframe {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border: none !important;
  outline: none !important;
}

/* Animações para elementos com motion */
.motion-div {
  will-change: opacity, transform;
}

/* Melhorar performance de scroll */
html {
  scroll-behavior: smooth;
}

/* ===== MELHORIAS CONSERVADORAS DE UX ===== */

/* Loading states suaves */
.loading {
  opacity: 0.7;
  pointer-events: none;
  transition: opacity 0.3s ease;
  position: relative;
}

.loading::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 20px;
  height: 20px;
  margin: -10px 0 0 -10px;
  border: 2px solid #f3f3f3;
  border-top: 2px solid #2ECC71;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  z-index: 10;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Melhorias para focus (acessibilidade) */
*:focus {
  outline: 2px solid #2ECC71;
  outline-offset: 2px;
  transition: outline 0.2s ease;
}

button:focus, .btn:focus, a:focus {
  outline: 2px solid #2ECC71;
  outline-offset: 2px;
  box-shadow: 0 0 0 4px rgba(46, 204, 113, 0.1);
}

/* Animações de entrada para elementos */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fadeInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Classes utilitárias para animações */
.animate-fade-in-up {
  animation: fadeInUp 0.6s ease-out;
}

.animate-fade-in-left {
  animation: fadeInLeft 0.6s ease-out;
}

.animate-fade-in-right {
  animation: fadeInRight 0.6s ease-out;
}

/* Melhorias para touch devices */
@media (hover: none) and (pointer: coarse) {
  /* Remove hover effects em touch devices */
  *:hover {
    transform: none !important;
  }

  /* Mantém apenas efeitos de tap */
  button:active, .btn:active, a:active {
    transform: scale(0.98);
    transition: transform 0.1s ease;
  }
}

/* Otimizações para reduced motion */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }

  html {
    scroll-behavior: auto;
  }
}
