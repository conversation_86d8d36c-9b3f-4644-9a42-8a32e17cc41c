/**
 * FREE ENERGY - DESIGN SYSTEM LIMPO
 * Sistema unificado que substitui todos os CSS conflitantes
 */

/* ===== DESIGN TOKENS ===== */
:root {
  /* Cores Principais */
  --primary: #2ECC71;
  --primary-dark: #27AE60;
  --primary-light: #58D68D;
  --secondary: #FFC107;
  --secondary-dark: #F39C12;
  --secondary-light: #FFD54F;
  
  /* Cores Neutras */
  --text-primary: #1a1a1a;
  --text-secondary: #4a5568;
  --text-muted: #718096;
  --background: #ffffff;
  --surface: #f7fafc;
  --border: #e2e8f0;
  
  /* Espaçamentos */
  --space-xs: 0.25rem;   /* 4px */
  --space-sm: 0.5rem;    /* 8px */
  --space-md: 1rem;      /* 16px */
  --space-lg: 1.5rem;    /* 24px */
  --space-xl: 2rem;      /* 32px */
  --space-2xl: 3rem;     /* 48px */
  --space-3xl: 4rem;     /* 64px */
  --space-4xl: 6rem;     /* 96px */
  
  /* Tipografia */
  --font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  --font-family-heading: 'Montserrat', var(--font-family);
  
  --font-size-xs: 0.75rem;    /* 12px */
  --font-size-sm: 0.875rem;   /* 14px */
  --font-size-base: 1rem;     /* 16px */
  --font-size-lg: 1.125rem;   /* 18px */
  --font-size-xl: 1.25rem;    /* 20px */
  --font-size-2xl: 1.5rem;    /* 24px */
  --font-size-3xl: 1.875rem;  /* 30px */
  --font-size-4xl: 2.25rem;   /* 36px */
  --font-size-5xl: 3rem;      /* 48px */
  
  --line-height-tight: 1.1;
  --line-height-normal: 1.4;
  --line-height-relaxed: 1.6;
  
  /* Sombras */
  --shadow-xs: 0 1px 2px rgba(0, 0, 0, 0.05);
  --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.1);
  --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.07);
  --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);
  --shadow-xl: 0 20px 25px rgba(0, 0, 0, 0.1);
  
  /* Bordas */
  --radius-sm: 0.375rem;  /* 6px */
  --radius-md: 0.5rem;    /* 8px */
  --radius-lg: 0.75rem;   /* 12px */
  --radius-xl: 1rem;      /* 16px */
  --radius-2xl: 1.5rem;   /* 24px */
  
  /* Transições */
  --transition-fast: 150ms ease;
  --transition-normal: 250ms ease;
  --transition-slow: 350ms ease;
  
  /* Z-index */
  --z-dropdown: 1000;
  --z-sticky: 1020;
  --z-fixed: 1030;
  --z-modal: 1040;
  --z-popover: 1050;
  --z-tooltip: 1060;
}

/* ===== RESET GLOBAL ===== */
*,
*::before,
*::after {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html {
  scroll-behavior: smooth;
  -webkit-text-size-adjust: 100%;
}

body {
  font-family: var(--font-family);
  font-size: var(--font-size-base);
  line-height: var(--line-height-normal);
  color: var(--text-primary);
  background-color: var(--background);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* ===== TIPOGRAFIA LIMPA ===== */
h1, h2, h3, h4, h5, h6 {
  font-family: var(--font-family-heading);
  font-weight: 600;
  line-height: var(--line-height-tight);
  color: var(--text-primary);
  margin: 0;
}

h1 { font-size: var(--font-size-4xl); font-weight: 700; }
h2 { font-size: var(--font-size-3xl); font-weight: 600; }
h3 { font-size: var(--font-size-2xl); font-weight: 600; }
h4 { font-size: var(--font-size-xl); font-weight: 500; }
h5 { font-size: var(--font-size-lg); font-weight: 500; }
h6 { font-size: var(--font-size-base); font-weight: 500; }

p {
  font-size: var(--font-size-lg);
  line-height: var(--line-height-relaxed);
  color: var(--text-secondary);
  margin: 0;
}

/* ===== LAYOUT CONTAINERS ===== */
.container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--space-md);
}

.section {
  padding: var(--space-4xl) 0;
}

.section-sm {
  padding: var(--space-3xl) 0;
}

.section-lg {
  padding: var(--space-4xl) 0 var(--space-4xl) 0;
}

/* ===== COMPONENTES BASE ===== */
.card {
  background: var(--background);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-md);
  padding: var(--space-xl);
  transition: all var(--transition-normal);
  border: none;
  outline: none;
}

.card:hover {
  box-shadow: var(--shadow-lg);
  transform: translateY(-2px);
}

.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-sm);
  padding: var(--space-md) var(--space-xl);
  font-size: var(--font-size-base);
  font-weight: 600;
  border-radius: var(--radius-lg);
  border: none;
  outline: none;
  cursor: pointer;
  transition: all var(--transition-fast);
  text-decoration: none;
  white-space: nowrap;
}

.btn-primary {
  background: var(--primary);
  color: white;
}

.btn-primary:hover {
  background: var(--primary-dark);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.btn-secondary {
  background: var(--secondary);
  color: var(--text-primary);
}

.btn-secondary:hover {
  background: var(--secondary-dark);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

/* ===== IMAGENS OTIMIZADAS ===== */
img {
  max-width: 100%;
  height: auto;
  border: none;
  outline: none;
  display: block;
}

.img-rounded {
  border-radius: var(--radius-lg);
}

.img-circle {
  border-radius: 50%;
}

/* ===== GRID SYSTEM ===== */
.grid {
  display: grid;
  gap: var(--space-lg);
}

.grid-cols-1 { grid-template-columns: repeat(1, 1fr); }
.grid-cols-2 { grid-template-columns: repeat(2, 1fr); }
.grid-cols-3 { grid-template-columns: repeat(3, 1fr); }
.grid-cols-4 { grid-template-columns: repeat(4, 1fr); }

/* ===== FLEXBOX UTILITIES ===== */
.flex { display: flex; }
.flex-col { flex-direction: column; }
.flex-wrap { flex-wrap: wrap; }
.items-center { align-items: center; }
.items-start { align-items: flex-start; }
.items-end { align-items: flex-end; }
.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }
.justify-start { justify-content: flex-start; }
.justify-end { justify-content: flex-end; }

/* ===== SPACING UTILITIES ===== */
.gap-xs { gap: var(--space-xs); }
.gap-sm { gap: var(--space-sm); }
.gap-md { gap: var(--space-md); }
.gap-lg { gap: var(--space-lg); }
.gap-xl { gap: var(--space-xl); }

.mb-xs { margin-bottom: var(--space-xs); }
.mb-sm { margin-bottom: var(--space-sm); }
.mb-md { margin-bottom: var(--space-md); }
.mb-lg { margin-bottom: var(--space-lg); }
.mb-xl { margin-bottom: var(--space-xl); }
.mb-2xl { margin-bottom: var(--space-2xl); }

.mt-xs { margin-top: var(--space-xs); }
.mt-sm { margin-top: var(--space-sm); }
.mt-md { margin-top: var(--space-md); }
.mt-lg { margin-top: var(--space-lg); }
.mt-xl { margin-top: var(--space-xl); }
.mt-2xl { margin-top: var(--space-2xl); }

/* ===== TEXT UTILITIES ===== */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.text-primary { color: var(--text-primary); }
.text-secondary { color: var(--text-secondary); }
.text-muted { color: var(--text-muted); }

.font-bold { font-weight: 700; }
.font-semibold { font-weight: 600; }
.font-medium { font-weight: 500; }
.font-normal { font-weight: 400; }

/* ===== RESPONSIVIDADE LIMPA ===== */
@media (max-width: 768px) {
  /* Ajustes de tipografia para mobile */
  h1 { font-size: var(--font-size-3xl); }
  h2 { font-size: var(--font-size-2xl); }
  h3 { font-size: var(--font-size-xl); }

  /* Ajustes de espaçamento para mobile */
  .section { padding: var(--space-3xl) 0; }
  .section-sm { padding: var(--space-2xl) 0; }
  .container { padding: 0 var(--space-lg); }

  /* Grid responsivo */
  .grid-cols-2,
  .grid-cols-3,
  .grid-cols-4 {
    grid-template-columns: 1fr;
  }

  /* Botões em mobile */
  .btn {
    padding: var(--space-lg) var(--space-xl);
    font-size: var(--font-size-lg);
  }

  /* Cards em mobile */
  .card {
    padding: var(--space-lg);
  }
}

@media (max-width: 480px) {
  /* Ajustes para telas muito pequenas */
  .container { padding: 0 var(--space-md); }
  .section { padding: var(--space-2xl) 0; }

  h1 { font-size: var(--font-size-2xl); }
  h2 { font-size: var(--font-size-xl); }

  .btn {
    padding: var(--space-md) var(--space-lg);
    font-size: var(--font-size-base);
  }
}

/* ===== ANIMAÇÕES SUAVES ===== */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}

@keyframes slideIn {
  from { opacity: 0; transform: translateX(-20px); }
  to { opacity: 1; transform: translateX(0); }
}

.animate-fade-in {
  animation: fadeIn var(--transition-normal) ease-out;
}

.animate-slide-in {
  animation: slideIn var(--transition-normal) ease-out;
}

/* ===== ESTADOS DE FOCO E ACESSIBILIDADE ===== */
*:focus {
  outline: 2px solid var(--primary);
  outline-offset: 2px;
}

.btn:focus {
  outline: 2px solid var(--primary);
  outline-offset: 2px;
}

/* ===== CORREÇÕES DE BUGS VISUAIS ===== */
/* Remove todos os !important desnecessários */
* {
  border: none !important;
  outline: none !important;
}

/* Exceções para elementos que precisam de bordas */
.btn,
.card,
input,
textarea,
select {
  border: 1px solid var(--border) !important;
}

.btn {
  border: none !important;
}

/* Remove transformações indesejadas */
img,
.img {
  transform: none !important;
}

/* Garante que hover só funcione em elementos interativos */
.card:hover,
.btn:hover,
a:hover {
  transform: translateY(-2px) !important;
}

/* Remove zoom indesejado em imagens */
img:hover {
  transform: none !important;
}

/* Corrige problemas de overflow */
.overflow-hidden {
  overflow: hidden !important;
}

/* Garante que elementos não quebrem o layout */
* {
  max-width: 100%;
  box-sizing: border-box;
}
