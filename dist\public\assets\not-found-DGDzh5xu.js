import{c as o,r as t,j as e,a as d}from"./index-DaZ2okPO.js";/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const n=o("CircleAlert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]]),l=t.forwardRef(({className:a,...s},r)=>e.jsx("div",{ref:r,className:d("rounded-lg border bg-card text-card-foreground shadow-sm",a),...s}));l.displayName="Card";const i=t.forwardRef(({className:a,...s},r)=>e.jsx("div",{ref:r,className:d("flex flex-col space-y-1.5 p-6",a),...s}));i.displayName="CardHeader";const x=t.forwardRef(({className:a,...s},r)=>e.jsx("h3",{ref:r,className:d("text-2xl font-semibold leading-none tracking-tight",a),...s}));x.displayName="CardTitle";const m=t.forwardRef(({className:a,...s},r)=>e.jsx("p",{ref:r,className:d("text-sm text-muted-foreground",a),...s}));m.displayName="CardDescription";const c=t.forwardRef(({className:a,...s},r)=>e.jsx("div",{ref:r,className:d("p-6 pt-0",a),...s}));c.displayName="CardContent";const f=t.forwardRef(({className:a,...s},r)=>e.jsx("div",{ref:r,className:d("flex items-center p-6 pt-0",a),...s}));f.displayName="CardFooter";function y(){return e.jsx("div",{className:"min-h-screen w-full flex items-center justify-center bg-gray-50",children:e.jsx(l,{className:"w-full max-w-md mx-4",children:e.jsxs(c,{className:"pt-6",children:[e.jsxs("div",{className:"flex mb-4 gap-2",children:[e.jsx(n,{className:"h-8 w-8 text-red-500"}),e.jsx("h1",{className:"text-2xl font-bold text-gray-900",children:"404 Page Not Found"})]}),e.jsx("p",{className:"mt-4 text-sm text-gray-600",children:"Did you forget to add the page to the router?"})]})})})}export{y as default};
