import{c as vt,j as f,r as b,X as Qi}from"./index-DaZ2okPO.js";/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ge=vt("ArrowRight",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const te=vt("Facebook",[["path",{d:"M18 2h-3a5 5 0 0 0-5 5v3H7v4h3v8h4v-8h3l1-4h-4V7a1 1 0 0 1 1-1h3z",key:"1jg4f8"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ee=vt("Instagram",[["rect",{width:"20",height:"20",x:"2",y:"2",rx:"5",ry:"5",key:"2e1cvw"}],["path",{d:"M16 11.37A4 4 0 1 1 12.63 8 4 4 0 0 1 16 11.37z",key:"9exkf1"}],["line",{x1:"17.5",x2:"17.51",y1:"6.5",y2:"6.5",key:"r4j83e"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ne=vt("Linkedin",[["path",{d:"M16 8a6 6 0 0 1 6 6v7h-4v-7a2 2 0 0 0-2-2 2 2 0 0 0-2 2v7h-4v-7a6 6 0 0 1 6-6z",key:"c2jq9f"}],["rect",{width:"4",height:"12",x:"2",y:"9",key:"mk3on5"}],["circle",{cx:"4",cy:"4",r:"2",key:"bt5ra8"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const tr=vt("Menu",[["line",{x1:"4",x2:"20",y1:"12",y2:"12",key:"1e0a9i"}],["line",{x1:"4",x2:"20",y1:"6",y2:"6",key:"1owob3"}],["line",{x1:"4",x2:"20",y1:"18",y2:"18",key:"yk5zj1"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const er=vt("TriangleAlert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]]),nr=()=>f.jsx("div",{className:"w-full bg-yellow-50 border-b-2 border-yellow-400 py-1 md:py-2 flex items-center justify-center gap-2 md:gap-3 text-yellow-800 text-xs md:text-sm font-semibold z-50",children:f.jsxs("div",{className:"container flex items-center justify-center gap-2 md:gap-3",children:[f.jsx(er,{className:"h-4 w-4 md:h-5 md:w-5 text-yellow-600 flex-shrink-0"}),f.jsx("span",{className:"line-clamp-2 md:line-clamp-none",children:"Atenção: Para receber sua proposta sem erros, tenha sua conta de luz em PDF em mãos."})]})}),I=({targetId:t,children:e,className:n="",onClick:s,isExternalLink:i=!1})=>{const o=a=>{if(a.preventDefault(),i||t.startsWith("/")){window.location.href=t,s&&s();return}const l=t.startsWith("#")?t.substring(1):t;if(window.location.pathname!=="/"){window.location.href=`/#${l}`,s&&s();return}const u=document.getElementById(l);if(u){const m=u.getBoundingClientRect().top+window.pageYOffset-120;window.scrollTo({top:m,behavior:"smooth"}),window.history.pushState(null,"",`#${l}`)}s&&s()},r=()=>{if(i||t.startsWith("/"))return t;const a=t.startsWith("#")?t:`#${t}`;return typeof window<"u"&&window.location.pathname!=="/"?`/${a}`:a};return f.jsx("a",{href:r(),className:n,onClick:o,style:{border:"none",outline:"none"},children:e})},Cc=()=>{const[t,e]=b.useState(!1),n=()=>{e(!t)};return f.jsxs("header",{className:"bg-white fixed w-full",style:{zIndex:"var(--z-fixed)",boxShadow:"var(--shadow-sm)",borderBottom:"1px solid var(--border)"},children:[f.jsx(nr,{}),f.jsxs("div",{className:"container flex justify-between items-center",style:{padding:"var(--space-md) var(--space-md)"},children:[f.jsx("div",{className:"flex items-center",children:f.jsx("div",{style:{height:"3rem",width:"auto",border:"none",outline:"none",boxShadow:"none"},children:f.jsx("img",{src:"/logo.png",alt:"Free Energy",style:{height:"100%",width:"auto",objectFit:"contain",userSelect:"none",WebkitUserDrag:"none",pointerEvents:"none",border:"none",outline:"none",boxShadow:"none"},draggable:"false"})})}),f.jsxs("nav",{className:"hidden md:flex items-center gap-xl",children:[f.jsx(I,{targetId:"como-funciona",className:"font-medium",style:{color:"var(--text-secondary)",fontSize:"var(--font-size-base)",textDecoration:"none",transition:"var(--transition-fast)",padding:"var(--space-xs) 0"},children:"Como Funciona"}),f.jsx(I,{targetId:"solucoes",className:"font-medium",style:{color:"var(--text-secondary)",fontSize:"var(--font-size-base)",textDecoration:"none",transition:"var(--transition-fast)",padding:"var(--space-xs) 0"},children:"Soluções"}),f.jsx(I,{targetId:"simulador",className:"font-medium",style:{color:"var(--text-secondary)",fontSize:"var(--font-size-base)",textDecoration:"none",transition:"var(--transition-fast)",padding:"var(--space-xs) 0"},children:"Simulador"}),f.jsx(I,{targetId:"cases",className:"font-medium",style:{color:"var(--text-secondary)",fontSize:"var(--font-size-base)",textDecoration:"none",transition:"var(--transition-fast)",padding:"var(--space-xs) 0"},children:"Cases"}),f.jsx(I,{targetId:"sobre-nos",className:"font-medium",style:{color:"var(--text-secondary)",fontSize:"var(--font-size-base)",textDecoration:"none",transition:"var(--transition-fast)",padding:"var(--space-xs) 0"},children:"Sobre Nós"}),f.jsx(I,{targetId:"/blog",className:"font-medium",isExternalLink:!0,style:{color:"var(--text-secondary)",fontSize:"var(--font-size-base)",textDecoration:"none",transition:"var(--transition-fast)",padding:"var(--space-xs) 0"},children:"Blog"})]}),f.jsxs(I,{targetId:"/solutions",className:"hidden md:flex items-center btn btn-secondary",isExternalLink:!0,style:{textDecoration:"none",gap:"var(--space-sm)"},children:["Contato ",f.jsx(ge,{className:"h-4 w-4"})]}),f.jsx("button",{className:"md:hidden flex items-center justify-center",onClick:n,style:{padding:"var(--space-sm)",borderRadius:"var(--radius-md)",background:"transparent",border:"none",color:"var(--text-secondary)",cursor:"pointer",transition:"var(--transition-fast)"},onMouseEnter:s=>{s.currentTarget.style.background="var(--surface)",s.currentTarget.style.color="var(--primary)"},onMouseLeave:s=>{s.currentTarget.style.background="transparent",s.currentTarget.style.color="var(--text-secondary)"},children:t?f.jsx(Qi,{className:"h-6 w-6"}):f.jsx(tr,{className:"h-6 w-6"})})]}),t&&f.jsx("div",{className:"md:hidden bg-white w-full absolute",style:{boxShadow:"var(--shadow-lg)",borderTop:"1px solid var(--border)"},children:f.jsxs("div",{className:"container flex flex-col gap-lg",style:{padding:"var(--space-lg) var(--space-md)"},children:[f.jsx(I,{targetId:"como-funciona",className:"font-medium",onClick:()=>e(!1),style:{color:"var(--text-secondary)",fontSize:"var(--font-size-lg)",textDecoration:"none",transition:"var(--transition-fast)",padding:"var(--space-sm) 0"},children:"Como Funciona"}),f.jsx(I,{targetId:"solucoes",className:"font-medium",onClick:()=>e(!1),style:{color:"var(--text-secondary)",fontSize:"var(--font-size-lg)",textDecoration:"none",transition:"var(--transition-fast)",padding:"var(--space-sm) 0"},children:"Soluções"}),f.jsx(I,{targetId:"simulador",className:"font-medium",onClick:()=>e(!1),style:{color:"var(--text-secondary)",fontSize:"var(--font-size-lg)",textDecoration:"none",transition:"var(--transition-fast)",padding:"var(--space-sm) 0"},children:"Simulador"}),f.jsx(I,{targetId:"cases",className:"font-medium",onClick:()=>e(!1),style:{color:"var(--text-secondary)",fontSize:"var(--font-size-lg)",textDecoration:"none",transition:"var(--transition-fast)",padding:"var(--space-sm) 0"},children:"Cases"}),f.jsx(I,{targetId:"sobre-nos",className:"font-medium",onClick:()=>e(!1),style:{color:"var(--text-secondary)",fontSize:"var(--font-size-lg)",textDecoration:"none",transition:"var(--transition-fast)",padding:"var(--space-sm) 0"},children:"Sobre Nós"}),f.jsx(I,{targetId:"/blog",className:"font-medium",onClick:()=>e(!1),isExternalLink:!0,style:{color:"var(--text-secondary)",fontSize:"var(--font-size-lg)",textDecoration:"none",transition:"var(--transition-fast)",padding:"var(--space-sm) 0"},children:"Blog"}),f.jsxs(I,{targetId:"/solutions",className:"btn btn-secondary text-center",onClick:()=>e(!1),isExternalLink:!0,style:{textDecoration:"none",gap:"var(--space-sm)",marginTop:"var(--space-md)",width:"100%",justifyContent:"center"},children:["Contato ",f.jsx(ge,{className:"h-4 w-4"})]})]})})]})};function sr(t){if(typeof Proxy>"u")return t;const e=new Map,n=(...s)=>t(...s);return new Proxy(n,{get:(s,i)=>i==="create"?t:(e.has(i)||e.set(i,t(i)),e.get(i))})}function Gt(t){return t!==null&&typeof t=="object"&&typeof t.start=="function"}const ye=t=>Array.isArray(t);function As(t,e){if(!Array.isArray(e))return!1;const n=e.length;if(n!==t.length)return!1;for(let s=0;s<n;s++)if(e[s]!==t[s])return!1;return!0}function Ct(t){return typeof t=="string"||Array.isArray(t)}function mn(t){const e=[{},{}];return t==null||t.values.forEach((n,s)=>{e[0][s]=n.get(),e[1][s]=n.getVelocity()}),e}function Le(t,e,n,s){if(typeof e=="function"){const[i,o]=mn(s);e=e(n!==void 0?n:t.custom,i,o)}if(typeof e=="string"&&(e=t.variants&&t.variants[e]),typeof e=="function"){const[i,o]=mn(s);e=e(n!==void 0?n:t.custom,i,o)}return e}function Xt(t,e,n){const s=t.getProps();return Le(s,e,n!==void 0?n:s.custom,t)}const Fe=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],Ne=["initial",...Fe],Et=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],lt=new Set(Et),X=t=>t*1e3,Y=t=>t/1e3,ir={type:"spring",stiffness:500,damping:25,restSpeed:10},rr=t=>({type:"spring",stiffness:550,damping:t===0?2*Math.sqrt(550):30,restSpeed:10}),or={type:"keyframes",duration:.8},ar={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},lr=(t,{keyframes:e})=>e.length>2?or:lt.has(t)?t.startsWith("scale")?rr(e[1]):ir:ar;function Be(t,e){return t?t[e]||t.default||t:void 0}const cr={skipAnimations:!1,useManualTiming:!1},ur=t=>t!==null;function Yt(t,{repeat:e,repeatType:n="loop"},s){const i=t.filter(ur),o=e&&n!=="loop"&&e%2===1?0:i.length-1;return!o||s===void 0?i[o]:s}const F=t=>t;let ve=F;function hr(t){let e=new Set,n=new Set,s=!1,i=!1;const o=new WeakSet;let r={delta:0,timestamp:0,isProcessing:!1};function a(c){o.has(c)&&(l.schedule(c),t()),c(r)}const l={schedule:(c,u=!1,h=!1)=>{const m=h&&s?e:n;return u&&o.add(c),m.has(c)||m.add(c),c},cancel:c=>{n.delete(c),o.delete(c)},process:c=>{if(r=c,s){i=!0;return}s=!0,[e,n]=[n,e],n.clear(),e.forEach(a),s=!1,i&&(i=!1,l.process(c))}};return l}const Lt=["read","resolveKeyframes","update","preRender","render","postRender"],dr=40;function Cs(t,e){let n=!1,s=!0;const i={delta:0,timestamp:0,isProcessing:!1},o=()=>n=!0,r=Lt.reduce((p,x)=>(p[x]=hr(o),p),{}),{read:a,resolveKeyframes:l,update:c,preRender:u,render:h,postRender:d}=r,m=()=>{const p=performance.now();n=!1,i.delta=s?1e3/60:Math.max(Math.min(p-i.timestamp,dr),1),i.timestamp=p,i.isProcessing=!0,a.process(i),l.process(i),c.process(i),u.process(i),h.process(i),d.process(i),i.isProcessing=!1,n&&e&&(s=!1,t(m))},g=()=>{n=!0,s=!0,i.isProcessing||t(m)};return{schedule:Lt.reduce((p,x)=>{const T=r[x];return p[x]=(A,P=!1,V=!1)=>(n||g(),T.schedule(A,P,V)),p},{}),cancel:p=>{for(let x=0;x<Lt.length;x++)r[Lt[x]].cancel(p)},state:i,steps:r}}const{schedule:C,cancel:tt,state:R,steps:se}=Cs(typeof requestAnimationFrame<"u"?requestAnimationFrame:F,!0),Vs=(t,e,n)=>(((1-3*n+3*e)*t+(3*n-6*e))*t+3*e)*t,fr=1e-7,mr=12;function pr(t,e,n,s,i){let o,r,a=0;do r=e+(n-e)/2,o=Vs(r,s,i)-t,o>0?n=r:e=r;while(Math.abs(o)>fr&&++a<mr);return r}function jt(t,e,n,s){if(t===e&&n===s)return F;const i=o=>pr(o,0,1,t,n);return o=>o===0||o===1?o:Vs(i(o),e,s)}const Ds=t=>e=>e<=.5?t(2*e)/2:(2-t(2*(1-e)))/2,Ms=t=>e=>1-t(1-e),Es=jt(.33,1.53,.69,.99),Ie=Ms(Es),js=Ds(Ie),Rs=t=>(t*=2)<1?.5*Ie(t):.5*(2-Math.pow(2,-10*(t-1))),Oe=t=>1-Math.sin(Math.acos(t)),ks=Ms(Oe),Ls=Ds(Oe),Fs=t=>/^0[^.\s]+$/u.test(t);function gr(t){return typeof t=="number"?t===0:t!==null?t==="none"||t==="0"||Fs(t):!0}const Ns=t=>/^-?(?:\d+(?:\.\d+)?|\.\d+)$/u.test(t),Bs=t=>e=>typeof e=="string"&&e.startsWith(t),Is=Bs("--"),yr=Bs("var(--"),_e=t=>yr(t)?vr.test(t.split("/*")[0].trim()):!1,vr=/var\(--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)$/iu,xr=/^var\(--(?:([\w-]+)|([\w-]+), ?([a-zA-Z\d ()%#.,-]+))\)/u;function wr(t){const e=xr.exec(t);if(!e)return[,];const[,n,s,i]=e;return[`--${n??s}`,i]}function Os(t,e,n=1){const[s,i]=wr(t);if(!s)return;const o=window.getComputedStyle(e).getPropertyValue(s);if(o){const r=o.trim();return Ns(r)?parseFloat(r):r}return _e(i)?Os(i,e,n+1):i}const q=(t,e,n)=>n>e?e:n<t?t:n,xt={test:t=>typeof t=="number",parse:parseFloat,transform:t=>t},Vt={...xt,transform:t=>q(0,1,t)},Ft={...xt,default:1},Rt=t=>({test:e=>typeof e=="string"&&e.endsWith(t)&&e.split(" ").length===1,parse:parseFloat,transform:e=>`${e}${t}`}),Z=Rt("deg"),W=Rt("%"),w=Rt("px"),Tr=Rt("vh"),Pr=Rt("vw"),pn={...W,parse:t=>W.parse(t)/100,transform:t=>W.transform(t*100)},br=new Set(["width","height","top","left","right","bottom","x","y","translateX","translateY"]),gn=t=>t===xt||t===w,yn=(t,e)=>parseFloat(t.split(", ")[e]),vn=(t,e)=>(n,{transform:s})=>{if(s==="none"||!s)return 0;const i=s.match(/^matrix3d\((.+)\)$/u);if(i)return yn(i[1],e);{const o=s.match(/^matrix\((.+)\)$/u);return o?yn(o[1],t):0}},Sr=new Set(["x","y","z"]),Ar=Et.filter(t=>!Sr.has(t));function Cr(t){const e=[];return Ar.forEach(n=>{const s=t.getValue(n);s!==void 0&&(e.push([n,s.get()]),s.set(n.startsWith("scale")?1:0))}),e}const pt={width:({x:t},{paddingLeft:e="0",paddingRight:n="0"})=>t.max-t.min-parseFloat(e)-parseFloat(n),height:({y:t},{paddingTop:e="0",paddingBottom:n="0"})=>t.max-t.min-parseFloat(e)-parseFloat(n),top:(t,{top:e})=>parseFloat(e),left:(t,{left:e})=>parseFloat(e),bottom:({y:t},{top:e})=>parseFloat(e)+(t.max-t.min),right:({x:t},{left:e})=>parseFloat(e)+(t.max-t.min),x:vn(4,13),y:vn(5,14)};pt.translateX=pt.x;pt.translateY=pt.y;const _s=t=>e=>e.test(t),Vr={test:t=>t==="auto",parse:t=>t},Us=[xt,w,W,Z,Pr,Tr,Vr],xn=t=>Us.find(_s(t)),at=new Set;let xe=!1,we=!1;function zs(){if(we){const t=Array.from(at).filter(s=>s.needsMeasurement),e=new Set(t.map(s=>s.element)),n=new Map;e.forEach(s=>{const i=Cr(s);i.length&&(n.set(s,i),s.render())}),t.forEach(s=>s.measureInitialState()),e.forEach(s=>{s.render();const i=n.get(s);i&&i.forEach(([o,r])=>{var a;(a=s.getValue(o))===null||a===void 0||a.set(r)})}),t.forEach(s=>s.measureEndState()),t.forEach(s=>{s.suspendedScrollY!==void 0&&window.scrollTo(0,s.suspendedScrollY)})}we=!1,xe=!1,at.forEach(t=>t.complete()),at.clear()}function Ks(){at.forEach(t=>{t.readKeyframes(),t.needsMeasurement&&(we=!0)})}function Dr(){Ks(),zs()}class Ue{constructor(e,n,s,i,o,r=!1){this.isComplete=!1,this.isAsync=!1,this.needsMeasurement=!1,this.isScheduled=!1,this.unresolvedKeyframes=[...e],this.onComplete=n,this.name=s,this.motionValue=i,this.element=o,this.isAsync=r}scheduleResolve(){this.isScheduled=!0,this.isAsync?(at.add(this),xe||(xe=!0,C.read(Ks),C.resolveKeyframes(zs))):(this.readKeyframes(),this.complete())}readKeyframes(){const{unresolvedKeyframes:e,name:n,element:s,motionValue:i}=this;for(let o=0;o<e.length;o++)if(e[o]===null)if(o===0){const r=i==null?void 0:i.get(),a=e[e.length-1];if(r!==void 0)e[0]=r;else if(s&&n){const l=s.readValue(n,a);l!=null&&(e[0]=l)}e[0]===void 0&&(e[0]=a),i&&r===void 0&&i.set(e[0])}else e[o]=e[o-1]}setFinalKeyframe(){}measureInitialState(){}renderEndStyles(){}measureEndState(){}complete(){this.isComplete=!0,this.onComplete(this.unresolvedKeyframes,this.finalKeyframe),at.delete(this)}cancel(){this.isComplete||(this.isScheduled=!1,at.delete(this))}resume(){this.isComplete||this.scheduleResolve()}}const bt=t=>Math.round(t*1e5)/1e5,ze=/-?(?:\d+(?:\.\d+)?|\.\d+)/gu;function Mr(t){return t==null}const Er=/^(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))$/iu,Ke=(t,e)=>n=>!!(typeof n=="string"&&Er.test(n)&&n.startsWith(t)||e&&!Mr(n)&&Object.prototype.hasOwnProperty.call(n,e)),Ws=(t,e,n)=>s=>{if(typeof s!="string")return s;const[i,o,r,a]=s.match(ze);return{[t]:parseFloat(i),[e]:parseFloat(o),[n]:parseFloat(r),alpha:a!==void 0?parseFloat(a):1}},jr=t=>q(0,255,t),ie={...xt,transform:t=>Math.round(jr(t))},ot={test:Ke("rgb","red"),parse:Ws("red","green","blue"),transform:({red:t,green:e,blue:n,alpha:s=1})=>"rgba("+ie.transform(t)+", "+ie.transform(e)+", "+ie.transform(n)+", "+bt(Vt.transform(s))+")"};function Rr(t){let e="",n="",s="",i="";return t.length>5?(e=t.substring(1,3),n=t.substring(3,5),s=t.substring(5,7),i=t.substring(7,9)):(e=t.substring(1,2),n=t.substring(2,3),s=t.substring(3,4),i=t.substring(4,5),e+=e,n+=n,s+=s,i+=i),{red:parseInt(e,16),green:parseInt(n,16),blue:parseInt(s,16),alpha:i?parseInt(i,16)/255:1}}const Te={test:Ke("#"),parse:Rr,transform:ot.transform},ut={test:Ke("hsl","hue"),parse:Ws("hue","saturation","lightness"),transform:({hue:t,saturation:e,lightness:n,alpha:s=1})=>"hsla("+Math.round(t)+", "+W.transform(bt(e))+", "+W.transform(bt(n))+", "+bt(Vt.transform(s))+")"},k={test:t=>ot.test(t)||Te.test(t)||ut.test(t),parse:t=>ot.test(t)?ot.parse(t):ut.test(t)?ut.parse(t):Te.parse(t),transform:t=>typeof t=="string"?t:t.hasOwnProperty("red")?ot.transform(t):ut.transform(t)},kr=/(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))/giu;function Lr(t){var e,n;return isNaN(t)&&typeof t=="string"&&(((e=t.match(ze))===null||e===void 0?void 0:e.length)||0)+(((n=t.match(kr))===null||n===void 0?void 0:n.length)||0)>0}const $s="number",Hs="color",Fr="var",Nr="var(",wn="${}",Br=/var\s*\(\s*--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)|#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\)|-?(?:\d+(?:\.\d+)?|\.\d+)/giu;function Dt(t){const e=t.toString(),n=[],s={color:[],number:[],var:[]},i=[];let o=0;const a=e.replace(Br,l=>(k.test(l)?(s.color.push(o),i.push(Hs),n.push(k.parse(l))):l.startsWith(Nr)?(s.var.push(o),i.push(Fr),n.push(l)):(s.number.push(o),i.push($s),n.push(parseFloat(l))),++o,wn)).split(wn);return{values:n,split:a,indexes:s,types:i}}function Gs(t){return Dt(t).values}function Xs(t){const{split:e,types:n}=Dt(t),s=e.length;return i=>{let o="";for(let r=0;r<s;r++)if(o+=e[r],i[r]!==void 0){const a=n[r];a===$s?o+=bt(i[r]):a===Hs?o+=k.transform(i[r]):o+=i[r]}return o}}const Ir=t=>typeof t=="number"?0:t;function Or(t){const e=Gs(t);return Xs(t)(e.map(Ir))}const et={test:Lr,parse:Gs,createTransformer:Xs,getAnimatableNone:Or},_r=new Set(["brightness","contrast","saturate","opacity"]);function Ur(t){const[e,n]=t.slice(0,-1).split("(");if(e==="drop-shadow")return t;const[s]=n.match(ze)||[];if(!s)return t;const i=n.replace(s,"");let o=_r.has(e)?1:0;return s!==n&&(o*=100),e+"("+o+i+")"}const zr=/\b([a-z-]*)\(.*?\)/gu,Pe={...et,getAnimatableNone:t=>{const e=t.match(zr);return e?e.map(Ur).join(" "):t}},Kr={borderWidth:w,borderTopWidth:w,borderRightWidth:w,borderBottomWidth:w,borderLeftWidth:w,borderRadius:w,radius:w,borderTopLeftRadius:w,borderTopRightRadius:w,borderBottomRightRadius:w,borderBottomLeftRadius:w,width:w,maxWidth:w,height:w,maxHeight:w,top:w,right:w,bottom:w,left:w,padding:w,paddingTop:w,paddingRight:w,paddingBottom:w,paddingLeft:w,margin:w,marginTop:w,marginRight:w,marginBottom:w,marginLeft:w,backgroundPositionX:w,backgroundPositionY:w},Wr={rotate:Z,rotateX:Z,rotateY:Z,rotateZ:Z,scale:Ft,scaleX:Ft,scaleY:Ft,scaleZ:Ft,skew:Z,skewX:Z,skewY:Z,distance:w,translateX:w,translateY:w,translateZ:w,x:w,y:w,z:w,perspective:w,transformPerspective:w,opacity:Vt,originX:pn,originY:pn,originZ:w},Tn={...xt,transform:Math.round},We={...Kr,...Wr,zIndex:Tn,size:w,fillOpacity:Vt,strokeOpacity:Vt,numOctaves:Tn},$r={...We,color:k,backgroundColor:k,outlineColor:k,fill:k,stroke:k,borderColor:k,borderTopColor:k,borderRightColor:k,borderBottomColor:k,borderLeftColor:k,filter:Pe,WebkitFilter:Pe},$e=t=>$r[t];function Ys(t,e){let n=$e(t);return n!==Pe&&(n=et),n.getAnimatableNone?n.getAnimatableNone(e):void 0}const Hr=new Set(["auto","none","0"]);function Gr(t,e,n){let s=0,i;for(;s<t.length&&!i;){const o=t[s];typeof o=="string"&&!Hr.has(o)&&Dt(o).values.length&&(i=t[s]),s++}if(i&&n)for(const o of e)t[o]=Ys(n,i)}class qs extends Ue{constructor(e,n,s,i,o){super(e,n,s,i,o,!0)}readKeyframes(){const{unresolvedKeyframes:e,element:n,name:s}=this;if(!n||!n.current)return;super.readKeyframes();for(let l=0;l<e.length;l++){let c=e[l];if(typeof c=="string"&&(c=c.trim(),_e(c))){const u=Os(c,n.current);u!==void 0&&(e[l]=u),l===e.length-1&&(this.finalKeyframe=c)}}if(this.resolveNoneKeyframes(),!br.has(s)||e.length!==2)return;const[i,o]=e,r=xn(i),a=xn(o);if(r!==a)if(gn(r)&&gn(a))for(let l=0;l<e.length;l++){const c=e[l];typeof c=="string"&&(e[l]=parseFloat(c))}else this.needsMeasurement=!0}resolveNoneKeyframes(){const{unresolvedKeyframes:e,name:n}=this,s=[];for(let i=0;i<e.length;i++)gr(e[i])&&s.push(i);s.length&&Gr(e,s,n)}measureInitialState(){const{element:e,unresolvedKeyframes:n,name:s}=this;if(!e||!e.current)return;s==="height"&&(this.suspendedScrollY=window.pageYOffset),this.measuredOrigin=pt[s](e.measureViewportBox(),window.getComputedStyle(e.current)),n[0]=this.measuredOrigin;const i=n[n.length-1];i!==void 0&&e.getValue(s,i).jump(i,!1)}measureEndState(){var e;const{element:n,name:s,unresolvedKeyframes:i}=this;if(!n||!n.current)return;const o=n.getValue(s);o&&o.jump(this.measuredOrigin,!1);const r=i.length-1,a=i[r];i[r]=pt[s](n.measureViewportBox(),window.getComputedStyle(n.current)),a!==null&&this.finalKeyframe===void 0&&(this.finalKeyframe=a),!((e=this.removedTransforms)===null||e===void 0)&&e.length&&this.removedTransforms.forEach(([l,c])=>{n.getValue(l).set(c)}),this.resolveNoneKeyframes()}}function He(t){return typeof t=="function"}let Bt;function Xr(){Bt=void 0}const $={now:()=>(Bt===void 0&&$.set(R.isProcessing||cr.useManualTiming?R.timestamp:performance.now()),Bt),set:t=>{Bt=t,queueMicrotask(Xr)}},Pn=(t,e)=>e==="zIndex"?!1:!!(typeof t=="number"||Array.isArray(t)||typeof t=="string"&&(et.test(t)||t==="0")&&!t.startsWith("url("));function Yr(t){const e=t[0];if(t.length===1)return!0;for(let n=0;n<t.length;n++)if(t[n]!==e)return!0}function qr(t,e,n,s){const i=t[0];if(i===null)return!1;if(e==="display"||e==="visibility")return!0;const o=t[t.length-1],r=Pn(i,e),a=Pn(o,e);return!r||!a?!1:Yr(t)||(n==="spring"||He(n))&&s}const Zr=40;class Zs{constructor({autoplay:e=!0,delay:n=0,type:s="keyframes",repeat:i=0,repeatDelay:o=0,repeatType:r="loop",...a}){this.isStopped=!1,this.hasAttemptedResolve=!1,this.createdAt=$.now(),this.options={autoplay:e,delay:n,type:s,repeat:i,repeatDelay:o,repeatType:r,...a},this.updateFinishedPromise()}calcStartTime(){return this.resolvedAt?this.resolvedAt-this.createdAt>Zr?this.resolvedAt:this.createdAt:this.createdAt}get resolved(){return!this._resolved&&!this.hasAttemptedResolve&&Dr(),this._resolved}onKeyframesResolved(e,n){this.resolvedAt=$.now(),this.hasAttemptedResolve=!0;const{name:s,type:i,velocity:o,delay:r,onComplete:a,onUpdate:l,isGenerator:c}=this.options;if(!c&&!qr(e,s,i,o))if(r)this.options.duration=0;else{l==null||l(Yt(e,this.options,n)),a==null||a(),this.resolveFinishedPromise();return}const u=this.initPlayback(e,n);u!==!1&&(this._resolved={keyframes:e,finalKeyframe:n,...u},this.onPostResolved())}onPostResolved(){}then(e,n){return this.currentFinishedPromise.then(e,n)}flatten(){this.options.type="keyframes",this.options.ease="linear"}updateFinishedPromise(){this.currentFinishedPromise=new Promise(e=>{this.resolveFinishedPromise=e})}}const gt=(t,e,n)=>{const s=e-t;return s===0?1:(n-t)/s},Js=(t,e,n=10)=>{let s="";const i=Math.max(Math.round(e/n),2);for(let o=0;o<i;o++)s+=t(gt(0,i-1,o))+", ";return`linear(${s.substring(0,s.length-2)})`};function Qs(t,e){return e?t*(1e3/e):0}const Jr=5;function ti(t,e,n){const s=Math.max(e-Jr,0);return Qs(n-t(s),e-s)}const M={stiffness:100,damping:10,mass:1,velocity:0,duration:800,bounce:.3,visualDuration:.3,restSpeed:{granular:.01,default:2},restDelta:{granular:.005,default:.5},minDuration:.01,maxDuration:10,minDamping:.05,maxDamping:1},re=.001;function Qr({duration:t=M.duration,bounce:e=M.bounce,velocity:n=M.velocity,mass:s=M.mass}){let i,o,r=1-e;r=q(M.minDamping,M.maxDamping,r),t=q(M.minDuration,M.maxDuration,Y(t)),r<1?(i=c=>{const u=c*r,h=u*t,d=u-n,m=be(c,r),g=Math.exp(-h);return re-d/m*g},o=c=>{const h=c*r*t,d=h*n+n,m=Math.pow(r,2)*Math.pow(c,2)*t,g=Math.exp(-h),y=be(Math.pow(c,2),r);return(-i(c)+re>0?-1:1)*((d-m)*g)/y}):(i=c=>{const u=Math.exp(-c*t),h=(c-n)*t+1;return-re+u*h},o=c=>{const u=Math.exp(-c*t),h=(n-c)*(t*t);return u*h});const a=5/t,l=eo(i,o,a);if(t=X(t),isNaN(l))return{stiffness:M.stiffness,damping:M.damping,duration:t};{const c=Math.pow(l,2)*s;return{stiffness:c,damping:r*2*Math.sqrt(s*c),duration:t}}}const to=12;function eo(t,e,n){let s=n;for(let i=1;i<to;i++)s=s-t(s)/e(s);return s}function be(t,e){return t*Math.sqrt(1-e*e)}const Se=2e4;function ei(t){let e=0;const n=50;let s=t.next(e);for(;!s.done&&e<Se;)e+=n,s=t.next(e);return e>=Se?1/0:e}const no=["duration","bounce"],so=["stiffness","damping","mass"];function bn(t,e){return e.some(n=>t[n]!==void 0)}function io(t){let e={velocity:M.velocity,stiffness:M.stiffness,damping:M.damping,mass:M.mass,isResolvedFromDuration:!1,...t};if(!bn(t,so)&&bn(t,no))if(t.visualDuration){const n=t.visualDuration,s=2*Math.PI/(n*1.2),i=s*s,o=2*q(.05,1,1-t.bounce)*Math.sqrt(i);e={...e,mass:M.mass,stiffness:i,damping:o}}else{const n=Qr(t);e={...e,...n,mass:M.mass},e.isResolvedFromDuration=!0}return e}function ni(t=M.visualDuration,e=M.bounce){const n=typeof t!="object"?{visualDuration:t,keyframes:[0,1],bounce:e}:t;let{restSpeed:s,restDelta:i}=n;const o=n.keyframes[0],r=n.keyframes[n.keyframes.length-1],a={done:!1,value:o},{stiffness:l,damping:c,mass:u,duration:h,velocity:d,isResolvedFromDuration:m}=io({...n,velocity:-Y(n.velocity||0)}),g=d||0,y=c/(2*Math.sqrt(l*u)),v=r-o,p=Y(Math.sqrt(l/u)),x=Math.abs(v)<5;s||(s=x?M.restSpeed.granular:M.restSpeed.default),i||(i=x?M.restDelta.granular:M.restDelta.default);let T;if(y<1){const P=be(p,y);T=V=>{const j=Math.exp(-y*p*V);return r-j*((g+y*p*v)/P*Math.sin(P*V)+v*Math.cos(P*V))}}else if(y===1)T=P=>r-Math.exp(-p*P)*(v+(g+p*v)*P);else{const P=p*Math.sqrt(y*y-1);T=V=>{const j=Math.exp(-y*p*V),S=Math.min(P*V,300);return r-j*((g+y*p*v)*Math.sinh(S)+P*v*Math.cosh(S))/P}}const A={calculatedDuration:m&&h||null,next:P=>{const V=T(P);if(m)a.done=P>=h;else{let j=0;y<1&&(j=P===0?X(g):ti(T,P,V));const S=Math.abs(j)<=s,B=Math.abs(r-V)<=i;a.done=S&&B}return a.value=a.done?r:V,a},toString:()=>{const P=Math.min(ei(A),Se),V=Js(j=>A.next(P*j).value,P,30);return P+"ms "+V}};return A}function Sn({keyframes:t,velocity:e=0,power:n=.8,timeConstant:s=325,bounceDamping:i=10,bounceStiffness:o=500,modifyTarget:r,min:a,max:l,restDelta:c=.5,restSpeed:u}){const h=t[0],d={done:!1,value:h},m=S=>a!==void 0&&S<a||l!==void 0&&S>l,g=S=>a===void 0?l:l===void 0||Math.abs(a-S)<Math.abs(l-S)?a:l;let y=n*e;const v=h+y,p=r===void 0?v:r(v);p!==v&&(y=p-h);const x=S=>-y*Math.exp(-S/s),T=S=>p+x(S),A=S=>{const B=x(S),_=T(S);d.done=Math.abs(B)<=c,d.value=d.done?p:_};let P,V;const j=S=>{m(d.value)&&(P=S,V=ni({keyframes:[d.value,g(d.value)],velocity:ti(T,S,d.value),damping:i,stiffness:o,restDelta:c,restSpeed:u}))};return j(0),{calculatedDuration:null,next:S=>{let B=!1;return!V&&P===void 0&&(B=!0,A(S),j(S)),P!==void 0&&S>=P?V.next(S-P):(!B&&A(S),d)}}}const ro=jt(.42,0,1,1),oo=jt(0,0,.58,1),si=jt(.42,0,.58,1),ao=t=>Array.isArray(t)&&typeof t[0]!="number",Ge=t=>Array.isArray(t)&&typeof t[0]=="number",An={linear:F,easeIn:ro,easeInOut:si,easeOut:oo,circIn:Oe,circInOut:Ls,circOut:ks,backIn:Ie,backInOut:js,backOut:Es,anticipate:Rs},Cn=t=>{if(Ge(t)){ve(t.length===4);const[e,n,s,i]=t;return jt(e,n,s,i)}else if(typeof t=="string")return ve(An[t]!==void 0),An[t];return t},lo=(t,e)=>n=>e(t(n)),J=(...t)=>t.reduce(lo),D=(t,e,n)=>t+(e-t)*n;function oe(t,e,n){return n<0&&(n+=1),n>1&&(n-=1),n<1/6?t+(e-t)*6*n:n<1/2?e:n<2/3?t+(e-t)*(2/3-n)*6:t}function co({hue:t,saturation:e,lightness:n,alpha:s}){t/=360,e/=100,n/=100;let i=0,o=0,r=0;if(!e)i=o=r=n;else{const a=n<.5?n*(1+e):n+e-n*e,l=2*n-a;i=oe(l,a,t+1/3),o=oe(l,a,t),r=oe(l,a,t-1/3)}return{red:Math.round(i*255),green:Math.round(o*255),blue:Math.round(r*255),alpha:s}}function _t(t,e){return n=>n>0?e:t}const ae=(t,e,n)=>{const s=t*t,i=n*(e*e-s)+s;return i<0?0:Math.sqrt(i)},uo=[Te,ot,ut],ho=t=>uo.find(e=>e.test(t));function Vn(t){const e=ho(t);if(!e)return!1;let n=e.parse(t);return e===ut&&(n=co(n)),n}const Dn=(t,e)=>{const n=Vn(t),s=Vn(e);if(!n||!s)return _t(t,e);const i={...n};return o=>(i.red=ae(n.red,s.red,o),i.green=ae(n.green,s.green,o),i.blue=ae(n.blue,s.blue,o),i.alpha=D(n.alpha,s.alpha,o),ot.transform(i))},Ae=new Set(["none","hidden"]);function fo(t,e){return Ae.has(t)?n=>n<=0?t:e:n=>n>=1?e:t}function mo(t,e){return n=>D(t,e,n)}function Xe(t){return typeof t=="number"?mo:typeof t=="string"?_e(t)?_t:k.test(t)?Dn:yo:Array.isArray(t)?ii:typeof t=="object"?k.test(t)?Dn:po:_t}function ii(t,e){const n=[...t],s=n.length,i=t.map((o,r)=>Xe(o)(o,e[r]));return o=>{for(let r=0;r<s;r++)n[r]=i[r](o);return n}}function po(t,e){const n={...t,...e},s={};for(const i in n)t[i]!==void 0&&e[i]!==void 0&&(s[i]=Xe(t[i])(t[i],e[i]));return i=>{for(const o in s)n[o]=s[o](i);return n}}function go(t,e){var n;const s=[],i={color:0,var:0,number:0};for(let o=0;o<e.values.length;o++){const r=e.types[o],a=t.indexes[r][i[r]],l=(n=t.values[a])!==null&&n!==void 0?n:0;s[o]=l,i[r]++}return s}const yo=(t,e)=>{const n=et.createTransformer(e),s=Dt(t),i=Dt(e);return s.indexes.var.length===i.indexes.var.length&&s.indexes.color.length===i.indexes.color.length&&s.indexes.number.length>=i.indexes.number.length?Ae.has(t)&&!i.values.length||Ae.has(e)&&!s.values.length?fo(t,e):J(ii(go(s,i),i.values),n):_t(t,e)};function ri(t,e,n){return typeof t=="number"&&typeof e=="number"&&typeof n=="number"?D(t,e,n):Xe(t)(t,e)}function vo(t,e,n){const s=[],i=n||ri,o=t.length-1;for(let r=0;r<o;r++){let a=i(t[r],t[r+1]);if(e){const l=Array.isArray(e)?e[r]||F:e;a=J(l,a)}s.push(a)}return s}function xo(t,e,{clamp:n=!0,ease:s,mixer:i}={}){const o=t.length;if(ve(o===e.length),o===1)return()=>e[0];if(o===2&&t[0]===t[1])return()=>e[1];t[0]>t[o-1]&&(t=[...t].reverse(),e=[...e].reverse());const r=vo(e,s,i),a=r.length,l=c=>{let u=0;if(a>1)for(;u<t.length-2&&!(c<t[u+1]);u++);const h=gt(t[u],t[u+1],c);return r[u](h)};return n?c=>l(q(t[0],t[o-1],c)):l}function wo(t,e){const n=t[t.length-1];for(let s=1;s<=e;s++){const i=gt(0,e,s);t.push(D(n,1,i))}}function To(t){const e=[0];return wo(e,t.length-1),e}function Po(t,e){return t.map(n=>n*e)}function bo(t,e){return t.map(()=>e||si).splice(0,t.length-1)}function Ut({duration:t=300,keyframes:e,times:n,ease:s="easeInOut"}){const i=ao(s)?s.map(Cn):Cn(s),o={done:!1,value:e[0]},r=Po(n&&n.length===e.length?n:To(e),t),a=xo(r,e,{ease:Array.isArray(i)?i:bo(e,i)});return{calculatedDuration:t,next:l=>(o.value=a(l),o.done=l>=t,o)}}const So=t=>{const e=({timestamp:n})=>t(n);return{start:()=>C.update(e,!0),stop:()=>tt(e),now:()=>R.isProcessing?R.timestamp:$.now()}},Ao={decay:Sn,inertia:Sn,tween:Ut,keyframes:Ut,spring:ni},Co=t=>t/100;class Ye extends Zs{constructor(e){super(e),this.holdTime=null,this.cancelTime=null,this.currentTime=0,this.playbackSpeed=1,this.pendingPlayState="running",this.startTime=null,this.state="idle",this.stop=()=>{if(this.resolver.cancel(),this.isStopped=!0,this.state==="idle")return;this.teardown();const{onStop:l}=this.options;l&&l()};const{name:n,motionValue:s,element:i,keyframes:o}=this.options,r=(i==null?void 0:i.KeyframeResolver)||Ue,a=(l,c)=>this.onKeyframesResolved(l,c);this.resolver=new r(o,a,n,s,i),this.resolver.scheduleResolve()}flatten(){super.flatten(),this._resolved&&Object.assign(this._resolved,this.initPlayback(this._resolved.keyframes))}initPlayback(e){const{type:n="keyframes",repeat:s=0,repeatDelay:i=0,repeatType:o,velocity:r=0}=this.options,a=He(n)?n:Ao[n]||Ut;let l,c;a!==Ut&&typeof e[0]!="number"&&(l=J(Co,ri(e[0],e[1])),e=[0,100]);const u=a({...this.options,keyframes:e});o==="mirror"&&(c=a({...this.options,keyframes:[...e].reverse(),velocity:-r})),u.calculatedDuration===null&&(u.calculatedDuration=ei(u));const{calculatedDuration:h}=u,d=h+i,m=d*(s+1)-i;return{generator:u,mirroredGenerator:c,mapPercentToKeyframes:l,calculatedDuration:h,resolvedDuration:d,totalDuration:m}}onPostResolved(){const{autoplay:e=!0}=this.options;this.play(),this.pendingPlayState==="paused"||!e?this.pause():this.state=this.pendingPlayState}tick(e,n=!1){const{resolved:s}=this;if(!s){const{keyframes:S}=this.options;return{done:!0,value:S[S.length-1]}}const{finalKeyframe:i,generator:o,mirroredGenerator:r,mapPercentToKeyframes:a,keyframes:l,calculatedDuration:c,totalDuration:u,resolvedDuration:h}=s;if(this.startTime===null)return o.next(0);const{delay:d,repeat:m,repeatType:g,repeatDelay:y,onUpdate:v}=this.options;this.speed>0?this.startTime=Math.min(this.startTime,e):this.speed<0&&(this.startTime=Math.min(e-u/this.speed,this.startTime)),n?this.currentTime=e:this.holdTime!==null?this.currentTime=this.holdTime:this.currentTime=Math.round(e-this.startTime)*this.speed;const p=this.currentTime-d*(this.speed>=0?1:-1),x=this.speed>=0?p<0:p>u;this.currentTime=Math.max(p,0),this.state==="finished"&&this.holdTime===null&&(this.currentTime=u);let T=this.currentTime,A=o;if(m){const S=Math.min(this.currentTime,u)/h;let B=Math.floor(S),_=S%1;!_&&S>=1&&(_=1),_===1&&B--,B=Math.min(B,m+1),!!(B%2)&&(g==="reverse"?(_=1-_,y&&(_-=y/h)):g==="mirror"&&(A=r)),T=q(0,1,_)*h}const P=x?{done:!1,value:l[0]}:A.next(T);a&&(P.value=a(P.value));let{done:V}=P;!x&&c!==null&&(V=this.speed>=0?this.currentTime>=u:this.currentTime<=0);const j=this.holdTime===null&&(this.state==="finished"||this.state==="running"&&V);return j&&i!==void 0&&(P.value=Yt(l,this.options,i)),v&&v(P.value),j&&this.finish(),P}get duration(){const{resolved:e}=this;return e?Y(e.calculatedDuration):0}get time(){return Y(this.currentTime)}set time(e){e=X(e),this.currentTime=e,this.holdTime!==null||this.speed===0?this.holdTime=e:this.driver&&(this.startTime=this.driver.now()-e/this.speed)}get speed(){return this.playbackSpeed}set speed(e){const n=this.playbackSpeed!==e;this.playbackSpeed=e,n&&(this.time=Y(this.currentTime))}play(){if(this.resolver.isScheduled||this.resolver.resume(),!this._resolved){this.pendingPlayState="running";return}if(this.isStopped)return;const{driver:e=So,onPlay:n,startTime:s}=this.options;this.driver||(this.driver=e(o=>this.tick(o))),n&&n();const i=this.driver.now();this.holdTime!==null?this.startTime=i-this.holdTime:this.startTime?this.state==="finished"&&(this.startTime=i):this.startTime=s??this.calcStartTime(),this.state==="finished"&&this.updateFinishedPromise(),this.cancelTime=this.startTime,this.holdTime=null,this.state="running",this.driver.start()}pause(){var e;if(!this._resolved){this.pendingPlayState="paused";return}this.state="paused",this.holdTime=(e=this.currentTime)!==null&&e!==void 0?e:0}complete(){this.state!=="running"&&this.play(),this.pendingPlayState=this.state="finished",this.holdTime=null}finish(){this.teardown(),this.state="finished";const{onComplete:e}=this.options;e&&e()}cancel(){this.cancelTime!==null&&this.tick(this.cancelTime),this.teardown(),this.updateFinishedPromise()}teardown(){this.state="idle",this.stopDriver(),this.resolveFinishedPromise(),this.updateFinishedPromise(),this.startTime=this.cancelTime=null,this.resolver.cancel()}stopDriver(){this.driver&&(this.driver.stop(),this.driver=void 0)}sample(e){return this.startTime=0,this.tick(e,!0)}}const Vo=new Set(["opacity","clipPath","filter","transform"]);function qe(t){let e;return()=>(e===void 0&&(e=t()),e)}const Do={linearEasing:void 0};function Mo(t,e){const n=qe(t);return()=>{var s;return(s=Do[e])!==null&&s!==void 0?s:n()}}const zt=Mo(()=>{try{document.createElement("div").animate({opacity:0},{easing:"linear(0, 1)"})}catch{return!1}return!0},"linearEasing");function oi(t){return!!(typeof t=="function"&&zt()||!t||typeof t=="string"&&(t in Ce||zt())||Ge(t)||Array.isArray(t)&&t.every(oi))}const Tt=([t,e,n,s])=>`cubic-bezier(${t}, ${e}, ${n}, ${s})`,Ce={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:Tt([0,.65,.55,1]),circOut:Tt([.55,0,1,.45]),backIn:Tt([.31,.01,.66,-.59]),backOut:Tt([.33,1.53,.69,.99])};function ai(t,e){if(t)return typeof t=="function"&&zt()?Js(t,e):Ge(t)?Tt(t):Array.isArray(t)?t.map(n=>ai(n,e)||Ce.easeOut):Ce[t]}function Eo(t,e,n,{delay:s=0,duration:i=300,repeat:o=0,repeatType:r="loop",ease:a="easeInOut",times:l}={}){const c={[e]:n};l&&(c.offset=l);const u=ai(a,i);return Array.isArray(u)&&(c.easing=u),t.animate(c,{delay:s,duration:i,easing:Array.isArray(u)?"linear":u,fill:"both",iterations:o+1,direction:r==="reverse"?"alternate":"normal"})}function Mn(t,e){t.timeline=e,t.onfinish=null}const jo=qe(()=>Object.hasOwnProperty.call(Element.prototype,"animate")),Kt=10,Ro=2e4;function ko(t){return He(t.type)||t.type==="spring"||!oi(t.ease)}function Lo(t,e){const n=new Ye({...e,keyframes:t,repeat:0,delay:0,isGenerator:!0});let s={done:!1,value:t[0]};const i=[];let o=0;for(;!s.done&&o<Ro;)s=n.sample(o),i.push(s.value),o+=Kt;return{times:void 0,keyframes:i,duration:o-Kt,ease:"linear"}}const li={anticipate:Rs,backInOut:js,circInOut:Ls};function Fo(t){return t in li}class En extends Zs{constructor(e){super(e);const{name:n,motionValue:s,element:i,keyframes:o}=this.options;this.resolver=new qs(o,(r,a)=>this.onKeyframesResolved(r,a),n,s,i),this.resolver.scheduleResolve()}initPlayback(e,n){var s;let{duration:i=300,times:o,ease:r,type:a,motionValue:l,name:c,startTime:u}=this.options;if(!(!((s=l.owner)===null||s===void 0)&&s.current))return!1;if(typeof r=="string"&&zt()&&Fo(r)&&(r=li[r]),ko(this.options)){const{onComplete:d,onUpdate:m,motionValue:g,element:y,...v}=this.options,p=Lo(e,v);e=p.keyframes,e.length===1&&(e[1]=e[0]),i=p.duration,o=p.times,r=p.ease,a="keyframes"}const h=Eo(l.owner.current,c,e,{...this.options,duration:i,times:o,ease:r});return h.startTime=u??this.calcStartTime(),this.pendingTimeline?(Mn(h,this.pendingTimeline),this.pendingTimeline=void 0):h.onfinish=()=>{const{onComplete:d}=this.options;l.set(Yt(e,this.options,n)),d&&d(),this.cancel(),this.resolveFinishedPromise()},{animation:h,duration:i,times:o,type:a,ease:r,keyframes:e}}get duration(){const{resolved:e}=this;if(!e)return 0;const{duration:n}=e;return Y(n)}get time(){const{resolved:e}=this;if(!e)return 0;const{animation:n}=e;return Y(n.currentTime||0)}set time(e){const{resolved:n}=this;if(!n)return;const{animation:s}=n;s.currentTime=X(e)}get speed(){const{resolved:e}=this;if(!e)return 1;const{animation:n}=e;return n.playbackRate}set speed(e){const{resolved:n}=this;if(!n)return;const{animation:s}=n;s.playbackRate=e}get state(){const{resolved:e}=this;if(!e)return"idle";const{animation:n}=e;return n.playState}get startTime(){const{resolved:e}=this;if(!e)return null;const{animation:n}=e;return n.startTime}attachTimeline(e){if(!this._resolved)this.pendingTimeline=e;else{const{resolved:n}=this;if(!n)return F;const{animation:s}=n;Mn(s,e)}return F}play(){if(this.isStopped)return;const{resolved:e}=this;if(!e)return;const{animation:n}=e;n.playState==="finished"&&this.updateFinishedPromise(),n.play()}pause(){const{resolved:e}=this;if(!e)return;const{animation:n}=e;n.pause()}stop(){if(this.resolver.cancel(),this.isStopped=!0,this.state==="idle")return;this.resolveFinishedPromise(),this.updateFinishedPromise();const{resolved:e}=this;if(!e)return;const{animation:n,keyframes:s,duration:i,type:o,ease:r,times:a}=e;if(n.playState==="idle"||n.playState==="finished")return;if(this.time){const{motionValue:c,onUpdate:u,onComplete:h,element:d,...m}=this.options,g=new Ye({...m,keyframes:s,duration:i,type:o,ease:r,times:a,isGenerator:!0}),y=X(this.time);c.setWithVelocity(g.sample(y-Kt).value,g.sample(y).value,Kt)}const{onStop:l}=this.options;l&&l(),this.cancel()}complete(){const{resolved:e}=this;e&&e.animation.finish()}cancel(){const{resolved:e}=this;e&&e.animation.cancel()}static supports(e){const{motionValue:n,name:s,repeatDelay:i,repeatType:o,damping:r,type:a}=e;return jo()&&s&&Vo.has(s)&&n&&n.owner&&n.owner.current instanceof HTMLElement&&!n.owner.getProps().onUpdate&&!i&&o!=="mirror"&&r!==0&&a!=="inertia"}}const No=qe(()=>window.ScrollTimeline!==void 0);class Bo{constructor(e){this.stop=()=>this.runAll("stop"),this.animations=e.filter(Boolean)}then(e,n){return Promise.all(this.animations).then(e).catch(n)}getAll(e){return this.animations[0][e]}setAll(e,n){for(let s=0;s<this.animations.length;s++)this.animations[s][e]=n}attachTimeline(e,n){const s=this.animations.map(i=>No()&&i.attachTimeline?i.attachTimeline(e):n(i));return()=>{s.forEach((i,o)=>{i&&i(),this.animations[o].stop()})}}get time(){return this.getAll("time")}set time(e){this.setAll("time",e)}get speed(){return this.getAll("speed")}set speed(e){this.setAll("speed",e)}get startTime(){return this.getAll("startTime")}get duration(){let e=0;for(let n=0;n<this.animations.length;n++)e=Math.max(e,this.animations[n].duration);return e}runAll(e){this.animations.forEach(n=>n[e]())}flatten(){this.runAll("flatten")}play(){this.runAll("play")}pause(){this.runAll("pause")}cancel(){this.runAll("cancel")}complete(){this.runAll("complete")}}function Io({when:t,delay:e,delayChildren:n,staggerChildren:s,staggerDirection:i,repeat:o,repeatType:r,repeatDelay:a,from:l,elapsed:c,...u}){return!!Object.keys(u).length}const Ze=(t,e,n,s={},i,o)=>r=>{const a=Be(s,t)||{},l=a.delay||s.delay||0;let{elapsed:c=0}=s;c=c-X(l);let u={keyframes:Array.isArray(n)?n:[null,n],ease:"easeOut",velocity:e.getVelocity(),...a,delay:-c,onUpdate:d=>{e.set(d),a.onUpdate&&a.onUpdate(d)},onComplete:()=>{r(),a.onComplete&&a.onComplete()},name:t,motionValue:e,element:o?void 0:i};Io(a)||(u={...u,...lr(t,u)}),u.duration&&(u.duration=X(u.duration)),u.repeatDelay&&(u.repeatDelay=X(u.repeatDelay)),u.from!==void 0&&(u.keyframes[0]=u.from);let h=!1;if((u.type===!1||u.duration===0&&!u.repeatDelay)&&(u.duration=0,u.delay===0&&(h=!0)),h&&!o&&e.get()!==void 0){const d=Yt(u.keyframes,a);if(d!==void 0)return C.update(()=>{u.onUpdate(d),u.onComplete()}),new Bo([])}return!o&&En.supports(u)?new En(u):new Ye(u)},Oo=t=>!!(t&&typeof t=="object"&&t.mix&&t.toValue),_o=t=>ye(t)?t[t.length-1]||0:t;function Je(t,e){t.indexOf(e)===-1&&t.push(e)}function Qe(t,e){const n=t.indexOf(e);n>-1&&t.splice(n,1)}class tn{constructor(){this.subscriptions=[]}add(e){return Je(this.subscriptions,e),()=>Qe(this.subscriptions,e)}notify(e,n,s){const i=this.subscriptions.length;if(i)if(i===1)this.subscriptions[0](e,n,s);else for(let o=0;o<i;o++){const r=this.subscriptions[o];r&&r(e,n,s)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}const jn=30,Uo=t=>!isNaN(parseFloat(t));class zo{constructor(e,n={}){this.version="11.13.1",this.canTrackVelocity=null,this.events={},this.updateAndNotify=(s,i=!0)=>{const o=$.now();this.updatedAt!==o&&this.setPrevFrameValue(),this.prev=this.current,this.setCurrent(s),this.current!==this.prev&&this.events.change&&this.events.change.notify(this.current),i&&this.events.renderRequest&&this.events.renderRequest.notify(this.current)},this.hasAnimated=!1,this.setCurrent(e),this.owner=n.owner}setCurrent(e){this.current=e,this.updatedAt=$.now(),this.canTrackVelocity===null&&e!==void 0&&(this.canTrackVelocity=Uo(this.current))}setPrevFrameValue(e=this.current){this.prevFrameValue=e,this.prevUpdatedAt=this.updatedAt}onChange(e){return this.on("change",e)}on(e,n){this.events[e]||(this.events[e]=new tn);const s=this.events[e].add(n);return e==="change"?()=>{s(),C.read(()=>{this.events.change.getSize()||this.stop()})}:s}clearListeners(){for(const e in this.events)this.events[e].clear()}attach(e,n){this.passiveEffect=e,this.stopPassiveEffect=n}set(e,n=!0){!n||!this.passiveEffect?this.updateAndNotify(e,n):this.passiveEffect(e,this.updateAndNotify)}setWithVelocity(e,n,s){this.set(n),this.prev=void 0,this.prevFrameValue=e,this.prevUpdatedAt=this.updatedAt-s}jump(e,n=!0){this.updateAndNotify(e),this.prev=e,this.prevUpdatedAt=this.prevFrameValue=void 0,n&&this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}get(){return this.current}getPrevious(){return this.prev}getVelocity(){const e=$.now();if(!this.canTrackVelocity||this.prevFrameValue===void 0||e-this.updatedAt>jn)return 0;const n=Math.min(this.updatedAt-this.prevUpdatedAt,jn);return Qs(parseFloat(this.current)-parseFloat(this.prevFrameValue),n)}start(e){return this.stop(),new Promise(n=>{this.hasAnimated=!0,this.animation=e(n),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function Mt(t,e){return new zo(t,e)}function Ko(t,e,n){t.hasValue(e)?t.getValue(e).set(n):t.addValue(e,Mt(n))}function Wo(t,e){const n=Xt(t,e);let{transitionEnd:s={},transition:i={},...o}=n||{};o={...o,...s};for(const r in o){const a=_o(o[r]);Ko(t,r,a)}}const en=t=>t.replace(/([a-z])([A-Z])/gu,"$1-$2").toLowerCase(),$o="framerAppearId",ci="data-"+en($o);function ui(t){return t.props[ci]}const L=t=>!!(t&&t.getVelocity);function Ho(t){return!!(L(t)&&t.add)}function Ve(t,e){const n=t.getValue("willChange");if(Ho(n))return n.add(e)}function Go({protectedKeys:t,needsAnimating:e},n){const s=t.hasOwnProperty(n)&&e[n]!==!0;return e[n]=!1,s}function hi(t,e,{delay:n=0,transitionOverride:s,type:i}={}){var o;let{transition:r=t.getDefaultTransition(),transitionEnd:a,...l}=e;s&&(r=s);const c=[],u=i&&t.animationState&&t.animationState.getState()[i];for(const h in l){const d=t.getValue(h,(o=t.latestValues[h])!==null&&o!==void 0?o:null),m=l[h];if(m===void 0||u&&Go(u,h))continue;const g={delay:n,...Be(r||{},h)};let y=!1;if(window.MotionHandoffAnimation){const p=ui(t);if(p){const x=window.MotionHandoffAnimation(p,h,C);x!==null&&(g.startTime=x,y=!0)}}Ve(t,h),d.start(Ze(h,d,m,t.shouldReduceMotion&&lt.has(h)?{type:!1}:g,t,y));const v=d.animation;v&&c.push(v)}return a&&Promise.all(c).then(()=>{C.update(()=>{a&&Wo(t,a)})}),c}function De(t,e,n={}){var s;const i=Xt(t,e,n.type==="exit"?(s=t.presenceContext)===null||s===void 0?void 0:s.custom:void 0);let{transition:o=t.getDefaultTransition()||{}}=i||{};n.transitionOverride&&(o=n.transitionOverride);const r=i?()=>Promise.all(hi(t,i,n)):()=>Promise.resolve(),a=t.variantChildren&&t.variantChildren.size?(c=0)=>{const{delayChildren:u=0,staggerChildren:h,staggerDirection:d}=o;return Xo(t,e,u+c,h,d,n)}:()=>Promise.resolve(),{when:l}=o;if(l){const[c,u]=l==="beforeChildren"?[r,a]:[a,r];return c().then(()=>u())}else return Promise.all([r(),a(n.delay)])}function Xo(t,e,n=0,s=0,i=1,o){const r=[],a=(t.variantChildren.size-1)*s,l=i===1?(c=0)=>c*s:(c=0)=>a-c*s;return Array.from(t.variantChildren).sort(Yo).forEach((c,u)=>{c.notify("AnimationStart",e),r.push(De(c,e,{...o,delay:n+l(u)}).then(()=>c.notify("AnimationComplete",e)))}),Promise.all(r)}function Yo(t,e){return t.sortNodePosition(e)}function qo(t,e,n={}){t.notify("AnimationStart",e);let s;if(Array.isArray(e)){const i=e.map(o=>De(t,o,n));s=Promise.all(i)}else if(typeof e=="string")s=De(t,e,n);else{const i=typeof e=="function"?Xt(t,e,n.custom):e;s=Promise.all(hi(t,i,n))}return s.then(()=>{t.notify("AnimationComplete",e)})}const Zo=Ne.length;function di(t){if(!t)return;if(!t.isControllingVariants){const n=t.parent?di(t.parent)||{}:{};return t.props.initial!==void 0&&(n.initial=t.props.initial),n}const e={};for(let n=0;n<Zo;n++){const s=Ne[n],i=t.props[s];(Ct(i)||i===!1)&&(e[s]=i)}return e}const Jo=[...Fe].reverse(),Qo=Fe.length;function ta(t){return e=>Promise.all(e.map(({animation:n,options:s})=>qo(t,n,s)))}function ea(t){let e=ta(t),n=Rn(),s=!0;const i=l=>(c,u)=>{var h;const d=Xt(t,u,l==="exit"?(h=t.presenceContext)===null||h===void 0?void 0:h.custom:void 0);if(d){const{transition:m,transitionEnd:g,...y}=d;c={...c,...y,...g}}return c};function o(l){e=l(t)}function r(l){const{props:c}=t,u=di(t.parent)||{},h=[],d=new Set;let m={},g=1/0;for(let v=0;v<Qo;v++){const p=Jo[v],x=n[p],T=c[p]!==void 0?c[p]:u[p],A=Ct(T),P=p===l?x.isActive:null;P===!1&&(g=v);let V=T===u[p]&&T!==c[p]&&A;if(V&&s&&t.manuallyAnimateOnMount&&(V=!1),x.protectedKeys={...m},!x.isActive&&P===null||!T&&!x.prevProp||Gt(T)||typeof T=="boolean")continue;const j=na(x.prevProp,T);let S=j||p===l&&x.isActive&&!V&&A||v>g&&A,B=!1;const _=Array.isArray(T)?T:[T];let ct=_.reduce(i(p),{});P===!1&&(ct={});const{prevResolvedValues:dn={}}=x,Ji={...dn,...ct},fn=N=>{S=!0,d.has(N)&&(B=!0,d.delete(N)),x.needsAnimating[N]=!0;const H=t.getValue(N);H&&(H.liveStyle=!1)};for(const N in Ji){const H=ct[N],Jt=dn[N];if(m.hasOwnProperty(N))continue;let Qt=!1;ye(H)&&ye(Jt)?Qt=!As(H,Jt):Qt=H!==Jt,Qt?H!=null?fn(N):d.add(N):H!==void 0&&d.has(N)?fn(N):x.protectedKeys[N]=!0}x.prevProp=T,x.prevResolvedValues=ct,x.isActive&&(m={...m,...ct}),s&&t.blockInitialAnimation&&(S=!1),S&&(!(V&&j)||B)&&h.push(..._.map(N=>({animation:N,options:{type:p}})))}if(d.size){const v={};d.forEach(p=>{const x=t.getBaseTarget(p),T=t.getValue(p);T&&(T.liveStyle=!0),v[p]=x??null}),h.push({animation:v})}let y=!!h.length;return s&&(c.initial===!1||c.initial===c.animate)&&!t.manuallyAnimateOnMount&&(y=!1),s=!1,y?e(h):Promise.resolve()}function a(l,c){var u;if(n[l].isActive===c)return Promise.resolve();(u=t.variantChildren)===null||u===void 0||u.forEach(d=>{var m;return(m=d.animationState)===null||m===void 0?void 0:m.setActive(l,c)}),n[l].isActive=c;const h=r(l);for(const d in n)n[d].protectedKeys={};return h}return{animateChanges:r,setActive:a,setAnimateFunction:o,getState:()=>n,reset:()=>{n=Rn(),s=!0}}}function na(t,e){return typeof e=="string"?e!==t:Array.isArray(e)?!As(e,t):!1}function st(t=!1){return{isActive:t,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function Rn(){return{animate:st(!0),whileInView:st(),whileHover:st(),whileTap:st(),whileDrag:st(),whileFocus:st(),exit:st()}}class nt{constructor(e){this.isMounted=!1,this.node=e}update(){}}class sa extends nt{constructor(e){super(e),e.animationState||(e.animationState=ea(e))}updateAnimationControlsSubscription(){const{animate:e}=this.node.getProps();Gt(e)&&(this.unmountControls=e.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){const{animate:e}=this.node.getProps(),{animate:n}=this.node.prevProps||{};e!==n&&this.updateAnimationControlsSubscription()}unmount(){var e;this.node.animationState.reset(),(e=this.unmountControls)===null||e===void 0||e.call(this)}}let ia=0;class ra extends nt{constructor(){super(...arguments),this.id=ia++}update(){if(!this.node.presenceContext)return;const{isPresent:e,onExitComplete:n}=this.node.presenceContext,{isPresent:s}=this.node.prevPresenceContext||{};if(!this.node.animationState||e===s)return;const i=this.node.animationState.setActive("exit",!e);n&&!e&&i.then(()=>n(this.id))}mount(){const{register:e}=this.node.presenceContext||{};e&&(this.unmount=e(this.id))}unmount(){}}const oa={animation:{Feature:sa},exit:{Feature:ra}};function aa(t,e,n){var s;if(t instanceof Element)return[t];if(typeof t=="string"){let i=document;const o=(s=void 0)!==null&&s!==void 0?s:i.querySelectorAll(t);return o?Array.from(o):[]}return Array.from(t)}const K={x:!1,y:!1};function fi(){return K.x||K.y}function kn(t){return e=>{e.pointerType==="touch"||fi()||t(e)}}function la(t,e,n={}){const s=new AbortController,i={passive:!0,...n,signal:s.signal},o=kn(r=>{const{target:a}=r,l=e(r);if(!l||!a)return;const c=kn(u=>{l(u),a.removeEventListener("pointerleave",c)});a.addEventListener("pointerleave",c,i)});return aa(t).forEach(r=>{r.addEventListener("pointerenter",o,i)}),()=>s.abort()}function ca(t){return t==="x"||t==="y"?K[t]?null:(K[t]=!0,()=>{K[t]=!1}):K.x||K.y?null:(K.x=K.y=!0,()=>{K.x=K.y=!1})}const mi=t=>t.pointerType==="mouse"?typeof t.button!="number"||t.button<=0:t.isPrimary!==!1;function kt(t){return{point:{x:t.pageX,y:t.pageY}}}const ua=t=>e=>mi(e)&&t(e,kt(e));function G(t,e,n,s={passive:!0}){return t.addEventListener(e,n,s),()=>t.removeEventListener(e,n)}function Q(t,e,n,s){return G(t,e,ua(n),s)}const Ln=(t,e)=>Math.abs(t-e);function ha(t,e){const n=Ln(t.x,e.x),s=Ln(t.y,e.y);return Math.sqrt(n**2+s**2)}class pi{constructor(e,n,{transformPagePoint:s,contextWindow:i,dragSnapToOrigin:o=!1}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;const h=ce(this.lastMoveEventInfo,this.history),d=this.startEvent!==null,m=ha(h.offset,{x:0,y:0})>=3;if(!d&&!m)return;const{point:g}=h,{timestamp:y}=R;this.history.push({...g,timestamp:y});const{onStart:v,onMove:p}=this.handlers;d||(v&&v(this.lastMoveEvent,h),this.startEvent=this.lastMoveEvent),p&&p(this.lastMoveEvent,h)},this.handlePointerMove=(h,d)=>{this.lastMoveEvent=h,this.lastMoveEventInfo=le(d,this.transformPagePoint),C.update(this.updatePoint,!0)},this.handlePointerUp=(h,d)=>{this.end();const{onEnd:m,onSessionEnd:g,resumeAnimation:y}=this.handlers;if(this.dragSnapToOrigin&&y&&y(),!(this.lastMoveEvent&&this.lastMoveEventInfo))return;const v=ce(h.type==="pointercancel"?this.lastMoveEventInfo:le(d,this.transformPagePoint),this.history);this.startEvent&&m&&m(h,v),g&&g(h,v)},!mi(e))return;this.dragSnapToOrigin=o,this.handlers=n,this.transformPagePoint=s,this.contextWindow=i||window;const r=kt(e),a=le(r,this.transformPagePoint),{point:l}=a,{timestamp:c}=R;this.history=[{...l,timestamp:c}];const{onSessionStart:u}=n;u&&u(e,ce(a,this.history)),this.removeListeners=J(Q(this.contextWindow,"pointermove",this.handlePointerMove),Q(this.contextWindow,"pointerup",this.handlePointerUp),Q(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(e){this.handlers=e}end(){this.removeListeners&&this.removeListeners(),tt(this.updatePoint)}}function le(t,e){return e?{point:e(t.point)}:t}function Fn(t,e){return{x:t.x-e.x,y:t.y-e.y}}function ce({point:t},e){return{point:t,delta:Fn(t,gi(e)),offset:Fn(t,da(e)),velocity:fa(e,.1)}}function da(t){return t[0]}function gi(t){return t[t.length-1]}function fa(t,e){if(t.length<2)return{x:0,y:0};let n=t.length-1,s=null;const i=gi(t);for(;n>=0&&(s=t[n],!(i.timestamp-s.timestamp>X(e)));)n--;if(!s)return{x:0,y:0};const o=Y(i.timestamp-s.timestamp);if(o===0)return{x:0,y:0};const r={x:(i.x-s.x)/o,y:(i.y-s.y)/o};return r.x===1/0&&(r.x=0),r.y===1/0&&(r.y=0),r}function ht(t){return t&&typeof t=="object"&&Object.prototype.hasOwnProperty.call(t,"current")}const yi=1e-4,ma=1-yi,pa=1+yi,vi=.01,ga=0-vi,ya=0+vi;function O(t){return t.max-t.min}function va(t,e,n){return Math.abs(t-e)<=n}function Nn(t,e,n,s=.5){t.origin=s,t.originPoint=D(e.min,e.max,t.origin),t.scale=O(n)/O(e),t.translate=D(n.min,n.max,t.origin)-t.originPoint,(t.scale>=ma&&t.scale<=pa||isNaN(t.scale))&&(t.scale=1),(t.translate>=ga&&t.translate<=ya||isNaN(t.translate))&&(t.translate=0)}function St(t,e,n,s){Nn(t.x,e.x,n.x,s?s.originX:void 0),Nn(t.y,e.y,n.y,s?s.originY:void 0)}function Bn(t,e,n){t.min=n.min+e.min,t.max=t.min+O(e)}function xa(t,e,n){Bn(t.x,e.x,n.x),Bn(t.y,e.y,n.y)}function In(t,e,n){t.min=e.min-n.min,t.max=t.min+O(e)}function At(t,e,n){In(t.x,e.x,n.x),In(t.y,e.y,n.y)}function wa(t,{min:e,max:n},s){return e!==void 0&&t<e?t=s?D(e,t,s.min):Math.max(t,e):n!==void 0&&t>n&&(t=s?D(n,t,s.max):Math.min(t,n)),t}function On(t,e,n){return{min:e!==void 0?t.min+e:void 0,max:n!==void 0?t.max+n-(t.max-t.min):void 0}}function Ta(t,{top:e,left:n,bottom:s,right:i}){return{x:On(t.x,n,i),y:On(t.y,e,s)}}function _n(t,e){let n=e.min-t.min,s=e.max-t.max;return e.max-e.min<t.max-t.min&&([n,s]=[s,n]),{min:n,max:s}}function Pa(t,e){return{x:_n(t.x,e.x),y:_n(t.y,e.y)}}function ba(t,e){let n=.5;const s=O(t),i=O(e);return i>s?n=gt(e.min,e.max-s,t.min):s>i&&(n=gt(t.min,t.max-i,e.min)),q(0,1,n)}function Sa(t,e){const n={};return e.min!==void 0&&(n.min=e.min-t.min),e.max!==void 0&&(n.max=e.max-t.min),n}const Me=.35;function Aa(t=Me){return t===!1?t=0:t===!0&&(t=Me),{x:Un(t,"left","right"),y:Un(t,"top","bottom")}}function Un(t,e,n){return{min:zn(t,e),max:zn(t,n)}}function zn(t,e){return typeof t=="number"?t:t[e]||0}const Kn=()=>({translate:0,scale:1,origin:0,originPoint:0}),dt=()=>({x:Kn(),y:Kn()}),Wn=()=>({min:0,max:0}),E=()=>({x:Wn(),y:Wn()});function z(t){return[t("x"),t("y")]}function xi({top:t,left:e,right:n,bottom:s}){return{x:{min:e,max:n},y:{min:t,max:s}}}function Ca({x:t,y:e}){return{top:e.min,right:t.max,bottom:e.max,left:t.min}}function Va(t,e){if(!e)return t;const n=e({x:t.left,y:t.top}),s=e({x:t.right,y:t.bottom});return{top:n.y,left:n.x,bottom:s.y,right:s.x}}function ue(t){return t===void 0||t===1}function Ee({scale:t,scaleX:e,scaleY:n}){return!ue(t)||!ue(e)||!ue(n)}function it(t){return Ee(t)||wi(t)||t.z||t.rotate||t.rotateX||t.rotateY||t.skewX||t.skewY}function wi(t){return $n(t.x)||$n(t.y)}function $n(t){return t&&t!=="0%"}function Wt(t,e,n){const s=t-n,i=e*s;return n+i}function Hn(t,e,n,s,i){return i!==void 0&&(t=Wt(t,i,s)),Wt(t,n,s)+e}function je(t,e=0,n=1,s,i){t.min=Hn(t.min,e,n,s,i),t.max=Hn(t.max,e,n,s,i)}function Ti(t,{x:e,y:n}){je(t.x,e.translate,e.scale,e.originPoint),je(t.y,n.translate,n.scale,n.originPoint)}const Gn=.999999999999,Xn=1.0000000000001;function Da(t,e,n,s=!1){const i=n.length;if(!i)return;e.x=e.y=1;let o,r;for(let a=0;a<i;a++){o=n[a],r=o.projectionDelta;const{visualElement:l}=o.options;l&&l.props.style&&l.props.style.display==="contents"||(s&&o.options.layoutScroll&&o.scroll&&o!==o.root&&mt(t,{x:-o.scroll.offset.x,y:-o.scroll.offset.y}),r&&(e.x*=r.x.scale,e.y*=r.y.scale,Ti(t,r)),s&&it(o.latestValues)&&mt(t,o.latestValues))}e.x<Xn&&e.x>Gn&&(e.x=1),e.y<Xn&&e.y>Gn&&(e.y=1)}function ft(t,e){t.min=t.min+e,t.max=t.max+e}function Yn(t,e,n,s,i=.5){const o=D(t.min,t.max,i);je(t,e,n,o,s)}function mt(t,e){Yn(t.x,e.x,e.scaleX,e.scale,e.originX),Yn(t.y,e.y,e.scaleY,e.scale,e.originY)}function Pi(t,e){return xi(Va(t.getBoundingClientRect(),e))}function Ma(t,e,n){const s=Pi(t,n),{scroll:i}=e;return i&&(ft(s.x,i.offset.x),ft(s.y,i.offset.y)),s}const bi=({current:t})=>t?t.ownerDocument.defaultView:null,Ea=new WeakMap;class ja{constructor(e){this.openDragLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=E(),this.visualElement=e}start(e,{snapToCursor:n=!1}={}){const{presenceContext:s}=this.visualElement;if(s&&s.isPresent===!1)return;const i=u=>{const{dragSnapToOrigin:h}=this.getProps();h?this.pauseAnimation():this.stopAnimation(),n&&this.snapToCursor(kt(u).point)},o=(u,h)=>{const{drag:d,dragPropagation:m,onDragStart:g}=this.getProps();if(d&&!m&&(this.openDragLock&&this.openDragLock(),this.openDragLock=ca(d),!this.openDragLock))return;this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),z(v=>{let p=this.getAxisMotionValue(v).get()||0;if(W.test(p)){const{projection:x}=this.visualElement;if(x&&x.layout){const T=x.layout.layoutBox[v];T&&(p=O(T)*(parseFloat(p)/100))}}this.originPoint[v]=p}),g&&C.postRender(()=>g(u,h)),Ve(this.visualElement,"transform");const{animationState:y}=this.visualElement;y&&y.setActive("whileDrag",!0)},r=(u,h)=>{const{dragPropagation:d,dragDirectionLock:m,onDirectionLock:g,onDrag:y}=this.getProps();if(!d&&!this.openDragLock)return;const{offset:v}=h;if(m&&this.currentDirection===null){this.currentDirection=Ra(v),this.currentDirection!==null&&g&&g(this.currentDirection);return}this.updateAxis("x",h.point,v),this.updateAxis("y",h.point,v),this.visualElement.render(),y&&y(u,h)},a=(u,h)=>this.stop(u,h),l=()=>z(u=>{var h;return this.getAnimationState(u)==="paused"&&((h=this.getAxisMotionValue(u).animation)===null||h===void 0?void 0:h.play())}),{dragSnapToOrigin:c}=this.getProps();this.panSession=new pi(e,{onSessionStart:i,onStart:o,onMove:r,onSessionEnd:a,resumeAnimation:l},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:c,contextWindow:bi(this.visualElement)})}stop(e,n){const s=this.isDragging;if(this.cancel(),!s)return;const{velocity:i}=n;this.startAnimation(i);const{onDragEnd:o}=this.getProps();o&&C.postRender(()=>o(e,n))}cancel(){this.isDragging=!1;const{projection:e,animationState:n}=this.visualElement;e&&(e.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;const{dragPropagation:s}=this.getProps();!s&&this.openDragLock&&(this.openDragLock(),this.openDragLock=null),n&&n.setActive("whileDrag",!1)}updateAxis(e,n,s){const{drag:i}=this.getProps();if(!s||!Nt(e,i,this.currentDirection))return;const o=this.getAxisMotionValue(e);let r=this.originPoint[e]+s[e];this.constraints&&this.constraints[e]&&(r=wa(r,this.constraints[e],this.elastic[e])),o.set(r)}resolveConstraints(){var e;const{dragConstraints:n,dragElastic:s}=this.getProps(),i=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):(e=this.visualElement.projection)===null||e===void 0?void 0:e.layout,o=this.constraints;n&&ht(n)?this.constraints||(this.constraints=this.resolveRefConstraints()):n&&i?this.constraints=Ta(i.layoutBox,n):this.constraints=!1,this.elastic=Aa(s),o!==this.constraints&&i&&this.constraints&&!this.hasMutatedConstraints&&z(r=>{this.constraints!==!1&&this.getAxisMotionValue(r)&&(this.constraints[r]=Sa(i.layoutBox[r],this.constraints[r]))})}resolveRefConstraints(){const{dragConstraints:e,onMeasureDragConstraints:n}=this.getProps();if(!e||!ht(e))return!1;const s=e.current,{projection:i}=this.visualElement;if(!i||!i.layout)return!1;const o=Ma(s,i.root,this.visualElement.getTransformPagePoint());let r=Pa(i.layout.layoutBox,o);if(n){const a=n(Ca(r));this.hasMutatedConstraints=!!a,a&&(r=xi(a))}return r}startAnimation(e){const{drag:n,dragMomentum:s,dragElastic:i,dragTransition:o,dragSnapToOrigin:r,onDragTransitionEnd:a}=this.getProps(),l=this.constraints||{},c=z(u=>{if(!Nt(u,n,this.currentDirection))return;let h=l&&l[u]||{};r&&(h={min:0,max:0});const d=i?200:1e6,m=i?40:1e7,g={type:"inertia",velocity:s?e[u]:0,bounceStiffness:d,bounceDamping:m,timeConstant:750,restDelta:1,restSpeed:10,...o,...h};return this.startAxisValueAnimation(u,g)});return Promise.all(c).then(a)}startAxisValueAnimation(e,n){const s=this.getAxisMotionValue(e);return Ve(this.visualElement,e),s.start(Ze(e,s,0,n,this.visualElement,!1))}stopAnimation(){z(e=>this.getAxisMotionValue(e).stop())}pauseAnimation(){z(e=>{var n;return(n=this.getAxisMotionValue(e).animation)===null||n===void 0?void 0:n.pause()})}getAnimationState(e){var n;return(n=this.getAxisMotionValue(e).animation)===null||n===void 0?void 0:n.state}getAxisMotionValue(e){const n=`_drag${e.toUpperCase()}`,s=this.visualElement.getProps(),i=s[n];return i||this.visualElement.getValue(e,(s.initial?s.initial[e]:void 0)||0)}snapToCursor(e){z(n=>{const{drag:s}=this.getProps();if(!Nt(n,s,this.currentDirection))return;const{projection:i}=this.visualElement,o=this.getAxisMotionValue(n);if(i&&i.layout){const{min:r,max:a}=i.layout.layoutBox[n];o.set(e[n]-D(r,a,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;const{drag:e,dragConstraints:n}=this.getProps(),{projection:s}=this.visualElement;if(!ht(n)||!s||!this.constraints)return;this.stopAnimation();const i={x:0,y:0};z(r=>{const a=this.getAxisMotionValue(r);if(a&&this.constraints!==!1){const l=a.get();i[r]=ba({min:l,max:l},this.constraints[r])}});const{transformTemplate:o}=this.visualElement.getProps();this.visualElement.current.style.transform=o?o({},""):"none",s.root&&s.root.updateScroll(),s.updateLayout(),this.resolveConstraints(),z(r=>{if(!Nt(r,e,null))return;const a=this.getAxisMotionValue(r),{min:l,max:c}=this.constraints[r];a.set(D(l,c,i[r]))})}addListeners(){if(!this.visualElement.current)return;Ea.set(this.visualElement,this);const e=this.visualElement.current,n=Q(e,"pointerdown",l=>{const{drag:c,dragListener:u=!0}=this.getProps();c&&u&&this.start(l)}),s=()=>{const{dragConstraints:l}=this.getProps();ht(l)&&l.current&&(this.constraints=this.resolveRefConstraints())},{projection:i}=this.visualElement,o=i.addEventListener("measure",s);i&&!i.layout&&(i.root&&i.root.updateScroll(),i.updateLayout()),C.read(s);const r=G(window,"resize",()=>this.scalePositionWithinConstraints()),a=i.addEventListener("didUpdate",({delta:l,hasLayoutChanged:c})=>{this.isDragging&&c&&(z(u=>{const h=this.getAxisMotionValue(u);h&&(this.originPoint[u]+=l[u].translate,h.set(h.get()+l[u].translate))}),this.visualElement.render())});return()=>{r(),n(),o(),a&&a()}}getProps(){const e=this.visualElement.getProps(),{drag:n=!1,dragDirectionLock:s=!1,dragPropagation:i=!1,dragConstraints:o=!1,dragElastic:r=Me,dragMomentum:a=!0}=e;return{...e,drag:n,dragDirectionLock:s,dragPropagation:i,dragConstraints:o,dragElastic:r,dragMomentum:a}}}function Nt(t,e,n){return(e===!0||e===t)&&(n===null||n===t)}function Ra(t,e=10){let n=null;return Math.abs(t.y)>e?n="y":Math.abs(t.x)>e&&(n="x"),n}class ka extends nt{constructor(e){super(e),this.removeGroupControls=F,this.removeListeners=F,this.controls=new ja(e)}mount(){const{dragControls:e}=this.node.getProps();e&&(this.removeGroupControls=e.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||F}unmount(){this.removeGroupControls(),this.removeListeners()}}const qn=t=>(e,n)=>{t&&C.postRender(()=>t(e,n))};class La extends nt{constructor(){super(...arguments),this.removePointerDownListener=F}onPointerDown(e){this.session=new pi(e,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:bi(this.node)})}createPanHandlers(){const{onPanSessionStart:e,onPanStart:n,onPan:s,onPanEnd:i}=this.node.getProps();return{onSessionStart:qn(e),onStart:qn(n),onMove:s,onEnd:(o,r)=>{delete this.session,i&&C.postRender(()=>i(o,r))}}}mount(){this.removePointerDownListener=Q(this.node.current,"pointerdown",e=>this.onPointerDown(e))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}const nn=b.createContext(null);function Fa(){const t=b.useContext(nn);if(t===null)return[!0,null];const{isPresent:e,onExitComplete:n,register:s}=t,i=b.useId();b.useEffect(()=>s(i),[]);const o=b.useCallback(()=>n&&n(i),[i,n]);return!e&&n?[!1,o]:[!0]}const Si=b.createContext({}),Ai=b.createContext({}),It={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function Zn(t,e){return e.max===e.min?0:t/(e.max-e.min)*100}const wt={correct:(t,e)=>{if(!e.target)return t;if(typeof t=="string")if(w.test(t))t=parseFloat(t);else return t;const n=Zn(t,e.target.x),s=Zn(t,e.target.y);return`${n}% ${s}%`}},Na={correct:(t,{treeScale:e,projectionDelta:n})=>{const s=t,i=et.parse(t);if(i.length>5)return s;const o=et.createTransformer(t),r=typeof i[0]!="number"?1:0,a=n.x.scale*e.x,l=n.y.scale*e.y;i[0+r]/=a,i[1+r]/=l;const c=D(a,l,.5);return typeof i[2+r]=="number"&&(i[2+r]/=c),typeof i[3+r]=="number"&&(i[3+r]/=c),o(i)}},$t={};function Ba(t){Object.assign($t,t)}const{schedule:sn,cancel:Vc}=Cs(queueMicrotask,!1);class Ia extends b.Component{componentDidMount(){const{visualElement:e,layoutGroup:n,switchLayoutGroup:s,layoutId:i}=this.props,{projection:o}=e;Ba(Oa),o&&(n.group&&n.group.add(o),s&&s.register&&i&&s.register(o),o.root.didUpdate(),o.addEventListener("animationComplete",()=>{this.safeToRemove()}),o.setOptions({...o.options,onExitComplete:()=>this.safeToRemove()})),It.hasEverUpdated=!0}getSnapshotBeforeUpdate(e){const{layoutDependency:n,visualElement:s,drag:i,isPresent:o}=this.props,r=s.projection;return r&&(r.isPresent=o,i||e.layoutDependency!==n||n===void 0?r.willUpdate():this.safeToRemove(),e.isPresent!==o&&(o?r.promote():r.relegate()||C.postRender(()=>{const a=r.getStack();(!a||!a.members.length)&&this.safeToRemove()}))),null}componentDidUpdate(){const{projection:e}=this.props.visualElement;e&&(e.root.didUpdate(),sn.postRender(()=>{!e.currentAnimation&&e.isLead()&&this.safeToRemove()}))}componentWillUnmount(){const{visualElement:e,layoutGroup:n,switchLayoutGroup:s}=this.props,{projection:i}=e;i&&(i.scheduleCheckAfterUnmount(),n&&n.group&&n.group.remove(i),s&&s.deregister&&s.deregister(i))}safeToRemove(){const{safeToRemove:e}=this.props;e&&e()}render(){return null}}function Ci(t){const[e,n]=Fa(),s=b.useContext(Si);return f.jsx(Ia,{...t,layoutGroup:s,switchLayoutGroup:b.useContext(Ai),isPresent:e,safeToRemove:n})}const Oa={borderRadius:{...wt,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:wt,borderTopRightRadius:wt,borderBottomLeftRadius:wt,borderBottomRightRadius:wt,boxShadow:Na},Vi=["TopLeft","TopRight","BottomLeft","BottomRight"],_a=Vi.length,Jn=t=>typeof t=="string"?parseFloat(t):t,Qn=t=>typeof t=="number"||w.test(t);function Ua(t,e,n,s,i,o){i?(t.opacity=D(0,n.opacity!==void 0?n.opacity:1,za(s)),t.opacityExit=D(e.opacity!==void 0?e.opacity:1,0,Ka(s))):o&&(t.opacity=D(e.opacity!==void 0?e.opacity:1,n.opacity!==void 0?n.opacity:1,s));for(let r=0;r<_a;r++){const a=`border${Vi[r]}Radius`;let l=ts(e,a),c=ts(n,a);if(l===void 0&&c===void 0)continue;l||(l=0),c||(c=0),l===0||c===0||Qn(l)===Qn(c)?(t[a]=Math.max(D(Jn(l),Jn(c),s),0),(W.test(c)||W.test(l))&&(t[a]+="%")):t[a]=c}(e.rotate||n.rotate)&&(t.rotate=D(e.rotate||0,n.rotate||0,s))}function ts(t,e){return t[e]!==void 0?t[e]:t.borderRadius}const za=Di(0,.5,ks),Ka=Di(.5,.95,F);function Di(t,e,n){return s=>s<t?0:s>e?1:n(gt(t,e,s))}function es(t,e){t.min=e.min,t.max=e.max}function U(t,e){es(t.x,e.x),es(t.y,e.y)}function ns(t,e){t.translate=e.translate,t.scale=e.scale,t.originPoint=e.originPoint,t.origin=e.origin}function ss(t,e,n,s,i){return t-=e,t=Wt(t,1/n,s),i!==void 0&&(t=Wt(t,1/i,s)),t}function Wa(t,e=0,n=1,s=.5,i,o=t,r=t){if(W.test(e)&&(e=parseFloat(e),e=D(r.min,r.max,e/100)-r.min),typeof e!="number")return;let a=D(o.min,o.max,s);t===o&&(a-=e),t.min=ss(t.min,e,n,a,i),t.max=ss(t.max,e,n,a,i)}function is(t,e,[n,s,i],o,r){Wa(t,e[n],e[s],e[i],e.scale,o,r)}const $a=["x","scaleX","originX"],Ha=["y","scaleY","originY"];function rs(t,e,n,s){is(t.x,e,$a,n?n.x:void 0,s?s.x:void 0),is(t.y,e,Ha,n?n.y:void 0,s?s.y:void 0)}function os(t){return t.translate===0&&t.scale===1}function Mi(t){return os(t.x)&&os(t.y)}function as(t,e){return t.min===e.min&&t.max===e.max}function Ga(t,e){return as(t.x,e.x)&&as(t.y,e.y)}function ls(t,e){return Math.round(t.min)===Math.round(e.min)&&Math.round(t.max)===Math.round(e.max)}function Ei(t,e){return ls(t.x,e.x)&&ls(t.y,e.y)}function cs(t){return O(t.x)/O(t.y)}function us(t,e){return t.translate===e.translate&&t.scale===e.scale&&t.originPoint===e.originPoint}class Xa{constructor(){this.members=[]}add(e){Je(this.members,e),e.scheduleRender()}remove(e){if(Qe(this.members,e),e===this.prevLead&&(this.prevLead=void 0),e===this.lead){const n=this.members[this.members.length-1];n&&this.promote(n)}}relegate(e){const n=this.members.findIndex(i=>e===i);if(n===0)return!1;let s;for(let i=n;i>=0;i--){const o=this.members[i];if(o.isPresent!==!1){s=o;break}}return s?(this.promote(s),!0):!1}promote(e,n){const s=this.lead;if(e!==s&&(this.prevLead=s,this.lead=e,e.show(),s)){s.instance&&s.scheduleRender(),e.scheduleRender(),e.resumeFrom=s,n&&(e.resumeFrom.preserveOpacity=!0),s.snapshot&&(e.snapshot=s.snapshot,e.snapshot.latestValues=s.animationValues||s.latestValues),e.root&&e.root.isUpdating&&(e.isLayoutDirty=!0);const{crossfade:i}=e.options;i===!1&&s.hide()}}exitAnimationComplete(){this.members.forEach(e=>{const{options:n,resumingFrom:s}=e;n.onExitComplete&&n.onExitComplete(),s&&s.options.onExitComplete&&s.options.onExitComplete()})}scheduleRender(){this.members.forEach(e=>{e.instance&&e.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}function Ya(t,e,n){let s="";const i=t.x.translate/e.x,o=t.y.translate/e.y,r=(n==null?void 0:n.z)||0;if((i||o||r)&&(s=`translate3d(${i}px, ${o}px, ${r}px) `),(e.x!==1||e.y!==1)&&(s+=`scale(${1/e.x}, ${1/e.y}) `),n){const{transformPerspective:c,rotate:u,rotateX:h,rotateY:d,skewX:m,skewY:g}=n;c&&(s=`perspective(${c}px) ${s}`),u&&(s+=`rotate(${u}deg) `),h&&(s+=`rotateX(${h}deg) `),d&&(s+=`rotateY(${d}deg) `),m&&(s+=`skewX(${m}deg) `),g&&(s+=`skewY(${g}deg) `)}const a=t.x.scale*e.x,l=t.y.scale*e.y;return(a!==1||l!==1)&&(s+=`scale(${a}, ${l})`),s||"none"}const qa=(t,e)=>t.depth-e.depth;class Za{constructor(){this.children=[],this.isDirty=!1}add(e){Je(this.children,e),this.isDirty=!0}remove(e){Qe(this.children,e),this.isDirty=!0}forEach(e){this.isDirty&&this.children.sort(qa),this.isDirty=!1,this.children.forEach(e)}}function Ot(t){const e=L(t)?t.get():t;return Oo(e)?e.toValue():e}function Ja(t,e){const n=$.now(),s=({timestamp:i})=>{const o=i-n;o>=e&&(tt(s),t(o-e))};return C.read(s,!0),()=>tt(s)}function Qa(t){return t instanceof SVGElement&&t.tagName!=="svg"}function tl(t,e,n){const s=L(t)?t:Mt(t);return s.start(Ze("",s,e,n)),s.animation}const rt={type:"projectionFrame",totalNodes:0,resolvedTargetDeltas:0,recalculatedProjection:0},Pt=typeof window<"u"&&window.MotionDebug!==void 0,he=["","X","Y","Z"],el={visibility:"hidden"},hs=1e3;let nl=0;function de(t,e,n,s){const{latestValues:i}=e;i[t]&&(n[t]=i[t],e.setStaticValue(t,0),s&&(s[t]=0))}function ji(t){if(t.hasCheckedOptimisedAppear=!0,t.root===t)return;const{visualElement:e}=t.options;if(!e)return;const n=ui(e);if(window.MotionHasOptimisedAnimation(n,"transform")){const{layout:i,layoutId:o}=t.options;window.MotionCancelOptimisedAnimation(n,"transform",C,!(i||o))}const{parent:s}=t;s&&!s.hasCheckedOptimisedAppear&&ji(s)}function Ri({attachResizeListener:t,defaultParent:e,measureScroll:n,checkIsScrollRoot:s,resetTransform:i}){return class{constructor(r={},a=e==null?void 0:e()){this.id=nl++,this.animationId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.hasCheckedOptimisedAppear=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.scheduleUpdate=()=>this.update(),this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,Pt&&(rt.totalNodes=rt.resolvedTargetDeltas=rt.recalculatedProjection=0),this.nodes.forEach(rl),this.nodes.forEach(ul),this.nodes.forEach(hl),this.nodes.forEach(ol),Pt&&window.MotionDebug.record(rt)},this.resolvedRelativeTargetAt=0,this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=r,this.root=a?a.root||a:this,this.path=a?[...a.path,a]:[],this.parent=a,this.depth=a?a.depth+1:0;for(let l=0;l<this.path.length;l++)this.path[l].shouldResetTransform=!0;this.root===this&&(this.nodes=new Za)}addEventListener(r,a){return this.eventHandlers.has(r)||this.eventHandlers.set(r,new tn),this.eventHandlers.get(r).add(a)}notifyListeners(r,...a){const l=this.eventHandlers.get(r);l&&l.notify(...a)}hasListeners(r){return this.eventHandlers.has(r)}mount(r,a=this.root.hasTreeAnimated){if(this.instance)return;this.isSVG=Qa(r),this.instance=r;const{layoutId:l,layout:c,visualElement:u}=this.options;if(u&&!u.current&&u.mount(r),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),a&&(c||l)&&(this.isLayoutDirty=!0),t){let h;const d=()=>this.root.updateBlockedByResize=!1;t(r,()=>{this.root.updateBlockedByResize=!0,h&&h(),h=Ja(d,250),It.hasAnimatedSinceResize&&(It.hasAnimatedSinceResize=!1,this.nodes.forEach(fs))})}l&&this.root.registerSharedNode(l,this),this.options.animate!==!1&&u&&(l||c)&&this.addEventListener("didUpdate",({delta:h,hasLayoutChanged:d,hasRelativeTargetChanged:m,layout:g})=>{if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}const y=this.options.transition||u.getDefaultTransition()||gl,{onLayoutAnimationStart:v,onLayoutAnimationComplete:p}=u.getProps(),x=!this.targetLayout||!Ei(this.targetLayout,g)||m,T=!d&&m;if(this.options.layoutRoot||this.resumeFrom&&this.resumeFrom.instance||T||d&&(x||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0),this.setAnimationOrigin(h,T);const A={...Be(y,"layout"),onPlay:v,onComplete:p};(u.shouldReduceMotion||this.options.layoutRoot)&&(A.delay=0,A.type=!1),this.startAnimation(A)}else d||fs(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=g})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);const r=this.getStack();r&&r.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,tt(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){this.isUpdateBlocked()||(this.isUpdating=!0,this.nodes&&this.nodes.forEach(dl),this.animationId++)}getTransformTemplate(){const{visualElement:r}=this.options;return r&&r.getProps().transformTemplate}willUpdate(r=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked()){this.options.onExitComplete&&this.options.onExitComplete();return}if(window.MotionCancelOptimisedAnimation&&!this.hasCheckedOptimisedAppear&&ji(this),!this.root.isUpdating&&this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let u=0;u<this.path.length;u++){const h=this.path[u];h.shouldResetTransform=!0,h.updateScroll("snapshot"),h.options.layoutRoot&&h.willUpdate(!1)}const{layoutId:a,layout:l}=this.options;if(a===void 0&&!l)return;const c=this.getTransformTemplate();this.prevTransformTemplateValue=c?c(this.latestValues,""):void 0,this.updateSnapshot(),r&&this.notifyListeners("willUpdate")}update(){if(this.updateScheduled=!1,this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(ds);return}this.isUpdating||this.nodes.forEach(ll),this.isUpdating=!1,this.nodes.forEach(cl),this.nodes.forEach(sl),this.nodes.forEach(il),this.clearAllSnapshots();const a=$.now();R.delta=q(0,1e3/60,a-R.timestamp),R.timestamp=a,R.isProcessing=!0,se.update.process(R),se.preRender.process(R),se.render.process(R),R.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,sn.read(this.scheduleUpdate))}clearAllSnapshots(){this.nodes.forEach(al),this.sharedNodes.forEach(fl)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,C.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){C.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){this.snapshot||!this.instance||(this.snapshot=this.measure())}updateLayout(){if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let l=0;l<this.path.length;l++)this.path[l].updateScroll();const r=this.layout;this.layout=this.measure(!1),this.layoutCorrected=E(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);const{visualElement:a}=this.options;a&&a.notify("LayoutMeasure",this.layout.layoutBox,r?r.layoutBox:void 0)}updateScroll(r="measure"){let a=!!(this.options.layoutScroll&&this.instance);if(this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===r&&(a=!1),a){const l=s(this.instance);this.scroll={animationId:this.root.animationId,phase:r,isRoot:l,offset:n(this.instance),wasRoot:this.scroll?this.scroll.isRoot:l}}}resetTransform(){if(!i)return;const r=this.isLayoutDirty||this.shouldResetTransform||this.options.alwaysMeasureLayout,a=this.projectionDelta&&!Mi(this.projectionDelta),l=this.getTransformTemplate(),c=l?l(this.latestValues,""):void 0,u=c!==this.prevTransformTemplateValue;r&&(a||it(this.latestValues)||u)&&(i(this.instance,c),this.shouldResetTransform=!1,this.scheduleRender())}measure(r=!0){const a=this.measurePageBox();let l=this.removeElementScroll(a);return r&&(l=this.removeTransform(l)),yl(l),{animationId:this.root.animationId,measuredBox:a,layoutBox:l,latestValues:{},source:this.id}}measurePageBox(){var r;const{visualElement:a}=this.options;if(!a)return E();const l=a.measureViewportBox();if(!(((r=this.scroll)===null||r===void 0?void 0:r.wasRoot)||this.path.some(vl))){const{scroll:u}=this.root;u&&(ft(l.x,u.offset.x),ft(l.y,u.offset.y))}return l}removeElementScroll(r){var a;const l=E();if(U(l,r),!((a=this.scroll)===null||a===void 0)&&a.wasRoot)return l;for(let c=0;c<this.path.length;c++){const u=this.path[c],{scroll:h,options:d}=u;u!==this.root&&h&&d.layoutScroll&&(h.wasRoot&&U(l,r),ft(l.x,h.offset.x),ft(l.y,h.offset.y))}return l}applyTransform(r,a=!1){const l=E();U(l,r);for(let c=0;c<this.path.length;c++){const u=this.path[c];!a&&u.options.layoutScroll&&u.scroll&&u!==u.root&&mt(l,{x:-u.scroll.offset.x,y:-u.scroll.offset.y}),it(u.latestValues)&&mt(l,u.latestValues)}return it(this.latestValues)&&mt(l,this.latestValues),l}removeTransform(r){const a=E();U(a,r);for(let l=0;l<this.path.length;l++){const c=this.path[l];if(!c.instance||!it(c.latestValues))continue;Ee(c.latestValues)&&c.updateSnapshot();const u=E(),h=c.measurePageBox();U(u,h),rs(a,c.latestValues,c.snapshot?c.snapshot.layoutBox:void 0,u)}return it(this.latestValues)&&rs(a,this.latestValues),a}setTargetDelta(r){this.targetDelta=r,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(r){this.options={...this.options,...r,crossfade:r.crossfade!==void 0?r.crossfade:!0}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==R.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(r=!1){var a;const l=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=l.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=l.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=l.isSharedProjectionDirty);const c=!!this.resumingFrom||this!==l;if(!(r||c&&this.isSharedProjectionDirty||this.isProjectionDirty||!((a=this.parent)===null||a===void 0)&&a.isProjectionDirty||this.attemptToResolveRelativeTarget||this.root.updateBlockedByResize))return;const{layout:h,layoutId:d}=this.options;if(!(!this.layout||!(h||d))){if(this.resolvedRelativeTargetAt=R.timestamp,!this.targetDelta&&!this.relativeTarget){const m=this.getClosestProjectingParent();m&&m.layout&&this.animationProgress!==1?(this.relativeParent=m,this.forceRelativeParentToResolveTarget(),this.relativeTarget=E(),this.relativeTargetOrigin=E(),At(this.relativeTargetOrigin,this.layout.layoutBox,m.layout.layoutBox),U(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(!(!this.relativeTarget&&!this.targetDelta)){if(this.target||(this.target=E(),this.targetWithTransforms=E()),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target?(this.forceRelativeParentToResolveTarget(),xa(this.target,this.relativeTarget,this.relativeParent.target)):this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):U(this.target,this.layout.layoutBox),Ti(this.target,this.targetDelta)):U(this.target,this.layout.layoutBox),this.attemptToResolveRelativeTarget){this.attemptToResolveRelativeTarget=!1;const m=this.getClosestProjectingParent();m&&!!m.resumingFrom==!!this.resumingFrom&&!m.options.layoutScroll&&m.target&&this.animationProgress!==1?(this.relativeParent=m,this.forceRelativeParentToResolveTarget(),this.relativeTarget=E(),this.relativeTargetOrigin=E(),At(this.relativeTargetOrigin,this.target,m.target),U(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}Pt&&rt.resolvedTargetDeltas++}}}getClosestProjectingParent(){if(!(!this.parent||Ee(this.parent.latestValues)||wi(this.parent.latestValues)))return this.parent.isProjecting()?this.parent:this.parent.getClosestProjectingParent()}isProjecting(){return!!((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){var r;const a=this.getLead(),l=!!this.resumingFrom||this!==a;let c=!0;if((this.isProjectionDirty||!((r=this.parent)===null||r===void 0)&&r.isProjectionDirty)&&(c=!1),l&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(c=!1),this.resolvedRelativeTargetAt===R.timestamp&&(c=!1),c)return;const{layout:u,layoutId:h}=this.options;if(this.isTreeAnimating=!!(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(u||h))return;U(this.layoutCorrected,this.layout.layoutBox);const d=this.treeScale.x,m=this.treeScale.y;Da(this.layoutCorrected,this.treeScale,this.path,l),a.layout&&!a.target&&(this.treeScale.x!==1||this.treeScale.y!==1)&&(a.target=a.layout.layoutBox,a.targetWithTransforms=E());const{target:g}=a;if(!g){this.prevProjectionDelta&&(this.createProjectionDeltas(),this.scheduleRender());return}!this.projectionDelta||!this.prevProjectionDelta?this.createProjectionDeltas():(ns(this.prevProjectionDelta.x,this.projectionDelta.x),ns(this.prevProjectionDelta.y,this.projectionDelta.y)),St(this.projectionDelta,this.layoutCorrected,g,this.latestValues),(this.treeScale.x!==d||this.treeScale.y!==m||!us(this.projectionDelta.x,this.prevProjectionDelta.x)||!us(this.projectionDelta.y,this.prevProjectionDelta.y))&&(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",g)),Pt&&rt.recalculatedProjection++}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(r=!0){var a;if((a=this.options.visualElement)===null||a===void 0||a.scheduleRender(),r){const l=this.getStack();l&&l.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}createProjectionDeltas(){this.prevProjectionDelta=dt(),this.projectionDelta=dt(),this.projectionDeltaWithTransform=dt()}setAnimationOrigin(r,a=!1){const l=this.snapshot,c=l?l.latestValues:{},u={...this.latestValues},h=dt();(!this.relativeParent||!this.relativeParent.options.layoutRoot)&&(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!a;const d=E(),m=l?l.source:void 0,g=this.layout?this.layout.source:void 0,y=m!==g,v=this.getStack(),p=!v||v.members.length<=1,x=!!(y&&!p&&this.options.crossfade===!0&&!this.path.some(pl));this.animationProgress=0;let T;this.mixTargetDelta=A=>{const P=A/1e3;ms(h.x,r.x,P),ms(h.y,r.y,P),this.setTargetDelta(h),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout&&(At(d,this.layout.layoutBox,this.relativeParent.layout.layoutBox),ml(this.relativeTarget,this.relativeTargetOrigin,d,P),T&&Ga(this.relativeTarget,T)&&(this.isProjectionDirty=!1),T||(T=E()),U(T,this.relativeTarget)),y&&(this.animationValues=u,Ua(u,c,this.latestValues,P,x,p)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=P},this.mixTargetDelta(this.options.layoutRoot?1e3:0)}startAnimation(r){this.notifyListeners("animationStart"),this.currentAnimation&&this.currentAnimation.stop(),this.resumingFrom&&this.resumingFrom.currentAnimation&&this.resumingFrom.currentAnimation.stop(),this.pendingAnimation&&(tt(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=C.update(()=>{It.hasAnimatedSinceResize=!0,this.currentAnimation=tl(0,hs,{...r,onUpdate:a=>{this.mixTargetDelta(a),r.onUpdate&&r.onUpdate(a)},onComplete:()=>{r.onComplete&&r.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);const r=this.getStack();r&&r.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(hs),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){const r=this.getLead();let{targetWithTransforms:a,target:l,layout:c,latestValues:u}=r;if(!(!a||!l||!c)){if(this!==r&&this.layout&&c&&ki(this.options.animationType,this.layout.layoutBox,c.layoutBox)){l=this.target||E();const h=O(this.layout.layoutBox.x);l.x.min=r.target.x.min,l.x.max=l.x.min+h;const d=O(this.layout.layoutBox.y);l.y.min=r.target.y.min,l.y.max=l.y.min+d}U(a,l),mt(a,u),St(this.projectionDeltaWithTransform,this.layoutCorrected,a,u)}}registerSharedNode(r,a){this.sharedNodes.has(r)||this.sharedNodes.set(r,new Xa),this.sharedNodes.get(r).add(a);const c=a.options.initialPromotionConfig;a.promote({transition:c?c.transition:void 0,preserveFollowOpacity:c&&c.shouldPreserveFollowOpacity?c.shouldPreserveFollowOpacity(a):void 0})}isLead(){const r=this.getStack();return r?r.lead===this:!0}getLead(){var r;const{layoutId:a}=this.options;return a?((r=this.getStack())===null||r===void 0?void 0:r.lead)||this:this}getPrevLead(){var r;const{layoutId:a}=this.options;return a?(r=this.getStack())===null||r===void 0?void 0:r.prevLead:void 0}getStack(){const{layoutId:r}=this.options;if(r)return this.root.sharedNodes.get(r)}promote({needsReset:r,transition:a,preserveFollowOpacity:l}={}){const c=this.getStack();c&&c.promote(this,l),r&&(this.projectionDelta=void 0,this.needsReset=!0),a&&this.setOptions({transition:a})}relegate(){const r=this.getStack();return r?r.relegate(this):!1}resetSkewAndRotation(){const{visualElement:r}=this.options;if(!r)return;let a=!1;const{latestValues:l}=r;if((l.z||l.rotate||l.rotateX||l.rotateY||l.rotateZ||l.skewX||l.skewY)&&(a=!0),!a)return;const c={};l.z&&de("z",r,c,this.animationValues);for(let u=0;u<he.length;u++)de(`rotate${he[u]}`,r,c,this.animationValues),de(`skew${he[u]}`,r,c,this.animationValues);r.render();for(const u in c)r.setStaticValue(u,c[u]),this.animationValues&&(this.animationValues[u]=c[u]);r.scheduleRender()}getProjectionStyles(r){var a,l;if(!this.instance||this.isSVG)return;if(!this.isVisible)return el;const c={visibility:""},u=this.getTransformTemplate();if(this.needsReset)return this.needsReset=!1,c.opacity="",c.pointerEvents=Ot(r==null?void 0:r.pointerEvents)||"",c.transform=u?u(this.latestValues,""):"none",c;const h=this.getLead();if(!this.projectionDelta||!this.layout||!h.target){const y={};return this.options.layoutId&&(y.opacity=this.latestValues.opacity!==void 0?this.latestValues.opacity:1,y.pointerEvents=Ot(r==null?void 0:r.pointerEvents)||""),this.hasProjected&&!it(this.latestValues)&&(y.transform=u?u({},""):"none",this.hasProjected=!1),y}const d=h.animationValues||h.latestValues;this.applyTransformsToTarget(),c.transform=Ya(this.projectionDeltaWithTransform,this.treeScale,d),u&&(c.transform=u(d,c.transform));const{x:m,y:g}=this.projectionDelta;c.transformOrigin=`${m.origin*100}% ${g.origin*100}% 0`,h.animationValues?c.opacity=h===this?(l=(a=d.opacity)!==null&&a!==void 0?a:this.latestValues.opacity)!==null&&l!==void 0?l:1:this.preserveOpacity?this.latestValues.opacity:d.opacityExit:c.opacity=h===this?d.opacity!==void 0?d.opacity:"":d.opacityExit!==void 0?d.opacityExit:0;for(const y in $t){if(d[y]===void 0)continue;const{correct:v,applyTo:p}=$t[y],x=c.transform==="none"?d[y]:v(d[y],h);if(p){const T=p.length;for(let A=0;A<T;A++)c[p[A]]=x}else c[y]=x}return this.options.layoutId&&(c.pointerEvents=h===this?Ot(r==null?void 0:r.pointerEvents)||"":"none"),c}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(r=>{var a;return(a=r.currentAnimation)===null||a===void 0?void 0:a.stop()}),this.root.nodes.forEach(ds),this.root.sharedNodes.clear()}}}function sl(t){t.updateLayout()}function il(t){var e;const n=((e=t.resumeFrom)===null||e===void 0?void 0:e.snapshot)||t.snapshot;if(t.isLead()&&t.layout&&n&&t.hasListeners("didUpdate")){const{layoutBox:s,measuredBox:i}=t.layout,{animationType:o}=t.options,r=n.source!==t.layout.source;o==="size"?z(h=>{const d=r?n.measuredBox[h]:n.layoutBox[h],m=O(d);d.min=s[h].min,d.max=d.min+m}):ki(o,n.layoutBox,s)&&z(h=>{const d=r?n.measuredBox[h]:n.layoutBox[h],m=O(s[h]);d.max=d.min+m,t.relativeTarget&&!t.currentAnimation&&(t.isProjectionDirty=!0,t.relativeTarget[h].max=t.relativeTarget[h].min+m)});const a=dt();St(a,s,n.layoutBox);const l=dt();r?St(l,t.applyTransform(i,!0),n.measuredBox):St(l,s,n.layoutBox);const c=!Mi(a);let u=!1;if(!t.resumeFrom){const h=t.getClosestProjectingParent();if(h&&!h.resumeFrom){const{snapshot:d,layout:m}=h;if(d&&m){const g=E();At(g,n.layoutBox,d.layoutBox);const y=E();At(y,s,m.layoutBox),Ei(g,y)||(u=!0),h.options.layoutRoot&&(t.relativeTarget=y,t.relativeTargetOrigin=g,t.relativeParent=h)}}}t.notifyListeners("didUpdate",{layout:s,snapshot:n,delta:l,layoutDelta:a,hasLayoutChanged:c,hasRelativeTargetChanged:u})}else if(t.isLead()){const{onExitComplete:s}=t.options;s&&s()}t.options.transition=void 0}function rl(t){Pt&&rt.totalNodes++,t.parent&&(t.isProjecting()||(t.isProjectionDirty=t.parent.isProjectionDirty),t.isSharedProjectionDirty||(t.isSharedProjectionDirty=!!(t.isProjectionDirty||t.parent.isProjectionDirty||t.parent.isSharedProjectionDirty)),t.isTransformDirty||(t.isTransformDirty=t.parent.isTransformDirty))}function ol(t){t.isProjectionDirty=t.isSharedProjectionDirty=t.isTransformDirty=!1}function al(t){t.clearSnapshot()}function ds(t){t.clearMeasurements()}function ll(t){t.isLayoutDirty=!1}function cl(t){const{visualElement:e}=t.options;e&&e.getProps().onBeforeLayoutMeasure&&e.notify("BeforeLayoutMeasure"),t.resetTransform()}function fs(t){t.finishAnimation(),t.targetDelta=t.relativeTarget=t.target=void 0,t.isProjectionDirty=!0}function ul(t){t.resolveTargetDelta()}function hl(t){t.calcProjection()}function dl(t){t.resetSkewAndRotation()}function fl(t){t.removeLeadSnapshot()}function ms(t,e,n){t.translate=D(e.translate,0,n),t.scale=D(e.scale,1,n),t.origin=e.origin,t.originPoint=e.originPoint}function ps(t,e,n,s){t.min=D(e.min,n.min,s),t.max=D(e.max,n.max,s)}function ml(t,e,n,s){ps(t.x,e.x,n.x,s),ps(t.y,e.y,n.y,s)}function pl(t){return t.animationValues&&t.animationValues.opacityExit!==void 0}const gl={duration:.45,ease:[.4,0,.1,1]},gs=t=>typeof navigator<"u"&&navigator.userAgent&&navigator.userAgent.toLowerCase().includes(t),ys=gs("applewebkit/")&&!gs("chrome/")?Math.round:F;function vs(t){t.min=ys(t.min),t.max=ys(t.max)}function yl(t){vs(t.x),vs(t.y)}function ki(t,e,n){return t==="position"||t==="preserve-aspect"&&!va(cs(e),cs(n),.2)}function vl(t){var e;return t!==t.root&&((e=t.scroll)===null||e===void 0?void 0:e.wasRoot)}const xl=Ri({attachResizeListener:(t,e)=>G(t,"resize",e),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),fe={current:void 0},Li=Ri({measureScroll:t=>({x:t.scrollLeft,y:t.scrollTop}),defaultParent:()=>{if(!fe.current){const t=new xl({});t.mount(window),t.setOptions({layoutScroll:!0}),fe.current=t}return fe.current},resetTransform:(t,e)=>{t.style.transform=e!==void 0?e:"none"},checkIsScrollRoot:t=>window.getComputedStyle(t).position==="fixed"}),wl={pan:{Feature:La},drag:{Feature:ka,ProjectionNode:Li,MeasureLayout:Ci}};function xs(t,e,n){const{props:s}=t;t.animationState&&s.whileHover&&t.animationState.setActive("whileHover",n);const i=s[n?"onHoverStart":"onHoverEnd"];i&&C.postRender(()=>i(e,kt(e)))}class Tl extends nt{mount(){const{current:e,props:n}=this.node;e&&(this.unmount=la(e,s=>(xs(this.node,s,!0),i=>xs(this.node,i,!1)),{passive:!n.onHoverStart&&!n.onHoverEnd}))}unmount(){}}class Pl extends nt{constructor(){super(...arguments),this.isActive=!1}onFocus(){let e=!1;try{e=this.node.current.matches(":focus-visible")}catch{e=!0}!e||!this.node.animationState||(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){!this.isActive||!this.node.animationState||(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=J(G(this.node.current,"focus",()=>this.onFocus()),G(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}const Fi=(t,e)=>e?t===e?!0:Fi(t,e.parentElement):!1;function me(t,e){if(!e)return;const n=new PointerEvent("pointer"+t);e(n,kt(n))}class bl extends nt{constructor(){super(...arguments),this.removeStartListeners=F,this.removeEndListeners=F,this.removeAccessibleListeners=F,this.startPointerPress=(e,n)=>{if(this.isPressing)return;this.removeEndListeners();const s=this.node.getProps(),o=Q(window,"pointerup",(a,l)=>{if(!this.checkPressEnd())return;const{onTap:c,onTapCancel:u,globalTapTarget:h}=this.node.getProps(),d=!h&&!Fi(this.node.current,a.target)?u:c;d&&C.update(()=>d(a,l))},{passive:!(s.onTap||s.onPointerUp)}),r=Q(window,"pointercancel",(a,l)=>this.cancelPress(a,l),{passive:!(s.onTapCancel||s.onPointerCancel)});this.removeEndListeners=J(o,r),this.startPress(e,n)},this.startAccessiblePress=()=>{const e=o=>{if(o.key!=="Enter"||this.isPressing)return;const r=a=>{a.key!=="Enter"||!this.checkPressEnd()||me("up",(l,c)=>{const{onTap:u}=this.node.getProps();u&&C.postRender(()=>u(l,c))})};this.removeEndListeners(),this.removeEndListeners=G(this.node.current,"keyup",r),me("down",(a,l)=>{this.startPress(a,l)})},n=G(this.node.current,"keydown",e),s=()=>{this.isPressing&&me("cancel",(o,r)=>this.cancelPress(o,r))},i=G(this.node.current,"blur",s);this.removeAccessibleListeners=J(n,i)}}startPress(e,n){this.isPressing=!0;const{onTapStart:s,whileTap:i}=this.node.getProps();i&&this.node.animationState&&this.node.animationState.setActive("whileTap",!0),s&&C.postRender(()=>s(e,n))}checkPressEnd(){return this.removeEndListeners(),this.isPressing=!1,this.node.getProps().whileTap&&this.node.animationState&&this.node.animationState.setActive("whileTap",!1),!fi()}cancelPress(e,n){if(!this.checkPressEnd())return;const{onTapCancel:s}=this.node.getProps();s&&C.postRender(()=>s(e,n))}mount(){const e=this.node.getProps(),n=Q(e.globalTapTarget?window:this.node.current,"pointerdown",this.startPointerPress,{passive:!(e.onTapStart||e.onPointerStart)}),s=G(this.node.current,"focus",this.startAccessiblePress);this.removeStartListeners=J(n,s)}unmount(){this.removeStartListeners(),this.removeEndListeners(),this.removeAccessibleListeners()}}const Re=new WeakMap,pe=new WeakMap,Sl=t=>{const e=Re.get(t.target);e&&e(t)},Al=t=>{t.forEach(Sl)};function Cl({root:t,...e}){const n=t||document;pe.has(n)||pe.set(n,{});const s=pe.get(n),i=JSON.stringify(e);return s[i]||(s[i]=new IntersectionObserver(Al,{root:t,...e})),s[i]}function Vl(t,e,n){const s=Cl(e);return Re.set(t,n),s.observe(t),()=>{Re.delete(t),s.unobserve(t)}}const Dl={some:0,all:1};class Ml extends nt{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();const{viewport:e={}}=this.node.getProps(),{root:n,margin:s,amount:i="some",once:o}=e,r={root:n?n.current:void 0,rootMargin:s,threshold:typeof i=="number"?i:Dl[i]},a=l=>{const{isIntersecting:c}=l;if(this.isInView===c||(this.isInView=c,o&&!c&&this.hasEnteredView))return;c&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",c);const{onViewportEnter:u,onViewportLeave:h}=this.node.getProps(),d=c?u:h;d&&d(l)};return Vl(this.node.current,r,a)}mount(){this.startObserver()}update(){if(typeof IntersectionObserver>"u")return;const{props:e,prevProps:n}=this.node;["amount","margin","root"].some(El(e,n))&&this.startObserver()}unmount(){}}function El({viewport:t={}},{viewport:e={}}={}){return n=>t[n]!==e[n]}const jl={inView:{Feature:Ml},tap:{Feature:bl},focus:{Feature:Pl},hover:{Feature:Tl}},Rl={layout:{ProjectionNode:Li,MeasureLayout:Ci}},Ni=b.createContext({transformPagePoint:t=>t,isStatic:!1,reducedMotion:"never"}),qt=b.createContext({}),rn=typeof window<"u",kl=rn?b.useLayoutEffect:b.useEffect,Bi=b.createContext({strict:!1});function Ll(t,e,n,s,i){var o,r;const{visualElement:a}=b.useContext(qt),l=b.useContext(Bi),c=b.useContext(nn),u=b.useContext(Ni).reducedMotion,h=b.useRef();s=s||l.renderer,!h.current&&s&&(h.current=s(t,{visualState:e,parent:a,props:n,presenceContext:c,blockInitialAnimation:c?c.initial===!1:!1,reducedMotionConfig:u}));const d=h.current,m=b.useContext(Ai);d&&!d.projection&&i&&(d.type==="html"||d.type==="svg")&&Fl(h.current,n,i,m);const g=b.useRef(!1);b.useInsertionEffect(()=>{d&&g.current&&d.update(n,c)});const y=n[ci],v=b.useRef(!!y&&!(!((o=window.MotionHandoffIsComplete)===null||o===void 0)&&o.call(window,y))&&((r=window.MotionHasOptimisedAnimation)===null||r===void 0?void 0:r.call(window,y)));return kl(()=>{d&&(g.current=!0,window.MotionIsMounted=!0,d.updateFeatures(),sn.render(d.render),v.current&&d.animationState&&d.animationState.animateChanges())}),b.useEffect(()=>{d&&(!v.current&&d.animationState&&d.animationState.animateChanges(),v.current&&(queueMicrotask(()=>{var p;(p=window.MotionHandoffMarkAsComplete)===null||p===void 0||p.call(window,y)}),v.current=!1))}),d}function Fl(t,e,n,s){const{layoutId:i,layout:o,drag:r,dragConstraints:a,layoutScroll:l,layoutRoot:c}=e;t.projection=new n(t.latestValues,e["data-framer-portal-id"]?void 0:Ii(t.parent)),t.projection.setOptions({layoutId:i,layout:o,alwaysMeasureLayout:!!r||a&&ht(a),visualElement:t,animationType:typeof o=="string"?o:"both",initialPromotionConfig:s,layoutScroll:l,layoutRoot:c})}function Ii(t){if(t)return t.options.allowProjection!==!1?t.projection:Ii(t.parent)}function Nl(t,e,n){return b.useCallback(s=>{s&&t.mount&&t.mount(s),e&&(s?e.mount(s):e.unmount()),n&&(typeof n=="function"?n(s):ht(n)&&(n.current=s))},[e])}function Zt(t){return Gt(t.animate)||Ne.some(e=>Ct(t[e]))}function Oi(t){return!!(Zt(t)||t.variants)}function Bl(t,e){if(Zt(t)){const{initial:n,animate:s}=t;return{initial:n===!1||Ct(n)?n:void 0,animate:Ct(s)?s:void 0}}return t.inherit!==!1?e:{}}function Il(t){const{initial:e,animate:n}=Bl(t,b.useContext(qt));return b.useMemo(()=>({initial:e,animate:n}),[ws(e),ws(n)])}function ws(t){return Array.isArray(t)?t.join(" "):t}const Ts={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},yt={};for(const t in Ts)yt[t]={isEnabled:e=>Ts[t].some(n=>!!e[n])};function Ol(t){for(const e in t)yt[e]={...yt[e],...t[e]}}const _l=Symbol.for("motionComponentSymbol");function Ul({preloadedFeatures:t,createVisualElement:e,useRender:n,useVisualState:s,Component:i}){t&&Ol(t);function o(a,l){let c;const u={...b.useContext(Ni),...a,layoutId:zl(a)},{isStatic:h}=u,d=Il(a),m=s(a,h);if(!h&&rn){Kl();const g=Wl(u);c=g.MeasureLayout,d.visualElement=Ll(i,m,u,e,g.ProjectionNode)}return f.jsxs(qt.Provider,{value:d,children:[c&&d.visualElement?f.jsx(c,{visualElement:d.visualElement,...u}):null,n(i,a,Nl(m,d.visualElement,l),m,h,d.visualElement)]})}const r=b.forwardRef(o);return r[_l]=i,r}function zl({layoutId:t}){const e=b.useContext(Si).id;return e&&t!==void 0?e+"-"+t:t}function Kl(t,e){b.useContext(Bi).strict}function Wl(t){const{drag:e,layout:n}=yt;if(!e&&!n)return{};const s={...e,...n};return{MeasureLayout:e!=null&&e.isEnabled(t)||n!=null&&n.isEnabled(t)?s.MeasureLayout:void 0,ProjectionNode:s.ProjectionNode}}const $l=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function on(t){return typeof t!="string"||t.includes("-")?!1:!!($l.indexOf(t)>-1||/[A-Z]/u.test(t))}function _i(t,{style:e,vars:n},s,i){Object.assign(t.style,e,i&&i.getProjectionStyles(s));for(const o in n)t.style.setProperty(o,n[o])}const Ui=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);function zi(t,e,n,s){_i(t,e,void 0,s);for(const i in e.attrs)t.setAttribute(Ui.has(i)?i:en(i),e.attrs[i])}function Ki(t,{layout:e,layoutId:n}){return lt.has(t)||t.startsWith("origin")||(e||n!==void 0)&&(!!$t[t]||t==="opacity")}function an(t,e,n){var s;const{style:i}=t,o={};for(const r in i)(L(i[r])||e.style&&L(e.style[r])||Ki(r,t)||((s=n==null?void 0:n.getValue(r))===null||s===void 0?void 0:s.liveStyle)!==void 0)&&(o[r]=i[r]);return o}function Wi(t,e,n){const s=an(t,e,n);for(const i in t)if(L(t[i])||L(e[i])){const o=Et.indexOf(i)!==-1?"attr"+i.charAt(0).toUpperCase()+i.substring(1):i;s[o]=t[i]}return s}function Hl(t){const e=b.useRef(null);return e.current===null&&(e.current=t()),e.current}function Gl({scrapeMotionValuesFromProps:t,createRenderState:e,onMount:n},s,i,o){const r={latestValues:Xl(s,i,o,t),renderState:e()};return n&&(r.mount=a=>n(s,a,r)),r}const $i=t=>(e,n)=>{const s=b.useContext(qt),i=b.useContext(nn),o=()=>Gl(t,e,s,i);return n?o():Hl(o)};function Xl(t,e,n,s){const i={},o=s(t,{});for(const d in o)i[d]=Ot(o[d]);let{initial:r,animate:a}=t;const l=Zt(t),c=Oi(t);e&&c&&!l&&t.inherit!==!1&&(r===void 0&&(r=e.initial),a===void 0&&(a=e.animate));let u=n?n.initial===!1:!1;u=u||r===!1;const h=u?a:r;if(h&&typeof h!="boolean"&&!Gt(h)){const d=Array.isArray(h)?h:[h];for(let m=0;m<d.length;m++){const g=Le(t,d[m]);if(g){const{transitionEnd:y,transition:v,...p}=g;for(const x in p){let T=p[x];if(Array.isArray(T)){const A=u?T.length-1:0;T=T[A]}T!==null&&(i[x]=T)}for(const x in y)i[x]=y[x]}}}return i}const ln=()=>({style:{},transform:{},transformOrigin:{},vars:{}}),Hi=()=>({...ln(),attrs:{}}),Gi=(t,e)=>e&&typeof t=="number"?e.transform(t):t,Yl={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},ql=Et.length;function Zl(t,e,n){let s="",i=!0;for(let o=0;o<ql;o++){const r=Et[o],a=t[r];if(a===void 0)continue;let l=!0;if(typeof a=="number"?l=a===(r.startsWith("scale")?1:0):l=parseFloat(a)===0,!l||n){const c=Gi(a,We[r]);if(!l){i=!1;const u=Yl[r]||r;s+=`${u}(${c}) `}n&&(e[r]=c)}}return s=s.trim(),n?s=n(e,i?"":s):i&&(s="none"),s}function cn(t,e,n){const{style:s,vars:i,transformOrigin:o}=t;let r=!1,a=!1;for(const l in e){const c=e[l];if(lt.has(l)){r=!0;continue}else if(Is(l)){i[l]=c;continue}else{const u=Gi(c,We[l]);l.startsWith("origin")?(a=!0,o[l]=u):s[l]=u}}if(e.transform||(r||n?s.transform=Zl(e,t.transform,n):s.transform&&(s.transform="none")),a){const{originX:l="50%",originY:c="50%",originZ:u=0}=o;s.transformOrigin=`${l} ${c} ${u}`}}function Ps(t,e,n){return typeof t=="string"?t:w.transform(e+n*t)}function Jl(t,e,n){const s=Ps(e,t.x,t.width),i=Ps(n,t.y,t.height);return`${s} ${i}`}const Ql={offset:"stroke-dashoffset",array:"stroke-dasharray"},tc={offset:"strokeDashoffset",array:"strokeDasharray"};function ec(t,e,n=1,s=0,i=!0){t.pathLength=1;const o=i?Ql:tc;t[o.offset]=w.transform(-s);const r=w.transform(e),a=w.transform(n);t[o.array]=`${r} ${a}`}function un(t,{attrX:e,attrY:n,attrScale:s,originX:i,originY:o,pathLength:r,pathSpacing:a=1,pathOffset:l=0,...c},u,h){if(cn(t,c,h),u){t.style.viewBox&&(t.attrs.viewBox=t.style.viewBox);return}t.attrs=t.style,t.style={};const{attrs:d,style:m,dimensions:g}=t;d.transform&&(g&&(m.transform=d.transform),delete d.transform),g&&(i!==void 0||o!==void 0||m.transform)&&(m.transformOrigin=Jl(g,i!==void 0?i:.5,o!==void 0?o:.5)),e!==void 0&&(d.x=e),n!==void 0&&(d.y=n),s!==void 0&&(d.scale=s),r!==void 0&&ec(d,r,a,l,!1)}const hn=t=>typeof t=="string"&&t.toLowerCase()==="svg",nc={useVisualState:$i({scrapeMotionValuesFromProps:Wi,createRenderState:Hi,onMount:(t,e,{renderState:n,latestValues:s})=>{C.read(()=>{try{n.dimensions=typeof e.getBBox=="function"?e.getBBox():e.getBoundingClientRect()}catch{n.dimensions={x:0,y:0,width:0,height:0}}}),C.render(()=>{un(n,s,hn(e.tagName),t.transformTemplate),zi(e,n)})}})},sc={useVisualState:$i({scrapeMotionValuesFromProps:an,createRenderState:ln})};function Xi(t,e,n){for(const s in e)!L(e[s])&&!Ki(s,n)&&(t[s]=e[s])}function ic({transformTemplate:t},e){return b.useMemo(()=>{const n=ln();return cn(n,e,t),Object.assign({},n.vars,n.style)},[e])}function rc(t,e){const n=t.style||{},s={};return Xi(s,n,t),Object.assign(s,ic(t,e)),s}function oc(t,e){const n={},s=rc(t,e);return t.drag&&t.dragListener!==!1&&(n.draggable=!1,s.userSelect=s.WebkitUserSelect=s.WebkitTouchCallout="none",s.touchAction=t.drag===!0?"none":`pan-${t.drag==="x"?"y":"x"}`),t.tabIndex===void 0&&(t.onTap||t.onTapStart||t.whileTap)&&(n.tabIndex=0),n.style=s,n}const ac=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function Ht(t){return t.startsWith("while")||t.startsWith("drag")&&t!=="draggable"||t.startsWith("layout")||t.startsWith("onTap")||t.startsWith("onPan")||t.startsWith("onLayout")||ac.has(t)}let Yi=t=>!Ht(t);function lc(t){t&&(Yi=e=>e.startsWith("on")?!Ht(e):t(e))}try{lc(require("@emotion/is-prop-valid").default)}catch{}function cc(t,e,n){const s={};for(const i in t)i==="values"&&typeof t.values=="object"||(Yi(i)||n===!0&&Ht(i)||!e&&!Ht(i)||t.draggable&&i.startsWith("onDrag"))&&(s[i]=t[i]);return s}function uc(t,e,n,s){const i=b.useMemo(()=>{const o=Hi();return un(o,e,hn(s),t.transformTemplate),{...o.attrs,style:{...o.style}}},[e]);if(t.style){const o={};Xi(o,t.style,t),i.style={...o,...i.style}}return i}function hc(t=!1){return(n,s,i,{latestValues:o},r)=>{const l=(on(n)?uc:oc)(s,o,r,n),c=cc(s,typeof n=="string",t),u=n!==b.Fragment?{...c,...l,ref:i}:{},{children:h}=s,d=b.useMemo(()=>L(h)?h.get():h,[h]);return b.createElement(n,{...u,children:d})}}function dc(t,e){return function(s,{forwardMotionProps:i}={forwardMotionProps:!1}){const r={...on(s)?nc:sc,preloadedFeatures:t,useRender:hc(i),createVisualElement:e,Component:s};return Ul(r)}}const ke={current:null},qi={current:!1};function fc(){if(qi.current=!0,!!rn)if(window.matchMedia){const t=window.matchMedia("(prefers-reduced-motion)"),e=()=>ke.current=t.matches;t.addListener(e),e()}else ke.current=!1}function mc(t,e,n){for(const s in e){const i=e[s],o=n[s];if(L(i))t.addValue(s,i);else if(L(o))t.addValue(s,Mt(i,{owner:t}));else if(o!==i)if(t.hasValue(s)){const r=t.getValue(s);r.liveStyle===!0?r.jump(i):r.hasAnimated||r.set(i)}else{const r=t.getStaticValue(s);t.addValue(s,Mt(r!==void 0?r:i,{owner:t}))}}for(const s in n)e[s]===void 0&&t.removeValue(s);return e}const bs=new WeakMap,pc=[...Us,k,et],gc=t=>pc.find(_s(t)),Ss=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"];class yc{scrapeMotionValuesFromProps(e,n,s){return{}}constructor({parent:e,props:n,presenceContext:s,reducedMotionConfig:i,blockInitialAnimation:o,visualState:r},a={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.KeyframeResolver=Ue,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.renderScheduledAt=0,this.scheduleRender=()=>{const d=$.now();this.renderScheduledAt<d&&(this.renderScheduledAt=d,C.render(this.render,!1,!0))};const{latestValues:l,renderState:c}=r;this.latestValues=l,this.baseTarget={...l},this.initialValues=n.initial?{...l}:{},this.renderState=c,this.parent=e,this.props=n,this.presenceContext=s,this.depth=e?e.depth+1:0,this.reducedMotionConfig=i,this.options=a,this.blockInitialAnimation=!!o,this.isControllingVariants=Zt(n),this.isVariantNode=Oi(n),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(e&&e.current);const{willChange:u,...h}=this.scrapeMotionValuesFromProps(n,{},this);for(const d in h){const m=h[d];l[d]!==void 0&&L(m)&&m.set(l[d],!1)}}mount(e){this.current=e,bs.set(e,this),this.projection&&!this.projection.instance&&this.projection.mount(e),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((n,s)=>this.bindToMotionValue(s,n)),qi.current||fc(),this.shouldReduceMotion=this.reducedMotionConfig==="never"?!1:this.reducedMotionConfig==="always"?!0:ke.current,this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){bs.delete(this.current),this.projection&&this.projection.unmount(),tt(this.notifyUpdate),tt(this.render),this.valueSubscriptions.forEach(e=>e()),this.valueSubscriptions.clear(),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this);for(const e in this.events)this.events[e].clear();for(const e in this.features){const n=this.features[e];n&&(n.unmount(),n.isMounted=!1)}this.current=null}bindToMotionValue(e,n){this.valueSubscriptions.has(e)&&this.valueSubscriptions.get(e)();const s=lt.has(e),i=n.on("change",a=>{this.latestValues[e]=a,this.props.onUpdate&&C.preRender(this.notifyUpdate),s&&this.projection&&(this.projection.isTransformDirty=!0)}),o=n.on("renderRequest",this.scheduleRender);let r;window.MotionCheckAppearSync&&(r=window.MotionCheckAppearSync(this,e,n)),this.valueSubscriptions.set(e,()=>{i(),o(),r&&r(),n.owner&&n.stop()})}sortNodePosition(e){return!this.current||!this.sortInstanceNodePosition||this.type!==e.type?0:this.sortInstanceNodePosition(this.current,e.current)}updateFeatures(){let e="animation";for(e in yt){const n=yt[e];if(!n)continue;const{isEnabled:s,Feature:i}=n;if(!this.features[e]&&i&&s(this.props)&&(this.features[e]=new i(this)),this.features[e]){const o=this.features[e];o.isMounted?o.update():(o.mount(),o.isMounted=!0)}}}triggerBuild(){this.build(this.renderState,this.latestValues,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):E()}getStaticValue(e){return this.latestValues[e]}setStaticValue(e,n){this.latestValues[e]=n}update(e,n){(e.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=e,this.prevPresenceContext=this.presenceContext,this.presenceContext=n;for(let s=0;s<Ss.length;s++){const i=Ss[s];this.propEventSubscriptions[i]&&(this.propEventSubscriptions[i](),delete this.propEventSubscriptions[i]);const o="on"+i,r=e[o];r&&(this.propEventSubscriptions[i]=this.on(i,r))}this.prevMotionValues=mc(this,this.scrapeMotionValuesFromProps(e,this.prevProps,this),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue()}getProps(){return this.props}getVariant(e){return this.props.variants?this.props.variants[e]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}addVariantChild(e){const n=this.getClosestVariantNode();if(n)return n.variantChildren&&n.variantChildren.add(e),()=>n.variantChildren.delete(e)}addValue(e,n){const s=this.values.get(e);n!==s&&(s&&this.removeValue(e),this.bindToMotionValue(e,n),this.values.set(e,n),this.latestValues[e]=n.get())}removeValue(e){this.values.delete(e);const n=this.valueSubscriptions.get(e);n&&(n(),this.valueSubscriptions.delete(e)),delete this.latestValues[e],this.removeValueFromRenderState(e,this.renderState)}hasValue(e){return this.values.has(e)}getValue(e,n){if(this.props.values&&this.props.values[e])return this.props.values[e];let s=this.values.get(e);return s===void 0&&n!==void 0&&(s=Mt(n===null?void 0:n,{owner:this}),this.addValue(e,s)),s}readValue(e,n){var s;let i=this.latestValues[e]!==void 0||!this.current?this.latestValues[e]:(s=this.getBaseTargetFromProps(this.props,e))!==null&&s!==void 0?s:this.readValueFromInstance(this.current,e,this.options);return i!=null&&(typeof i=="string"&&(Ns(i)||Fs(i))?i=parseFloat(i):!gc(i)&&et.test(n)&&(i=Ys(e,n)),this.setBaseTarget(e,L(i)?i.get():i)),L(i)?i.get():i}setBaseTarget(e,n){this.baseTarget[e]=n}getBaseTarget(e){var n;const{initial:s}=this.props;let i;if(typeof s=="string"||typeof s=="object"){const r=Le(this.props,s,(n=this.presenceContext)===null||n===void 0?void 0:n.custom);r&&(i=r[e])}if(s&&i!==void 0)return i;const o=this.getBaseTargetFromProps(this.props,e);return o!==void 0&&!L(o)?o:this.initialValues[e]!==void 0&&i===void 0?void 0:this.baseTarget[e]}on(e,n){return this.events[e]||(this.events[e]=new tn),this.events[e].add(n)}notify(e,...n){this.events[e]&&this.events[e].notify(...n)}}class Zi extends yc{constructor(){super(...arguments),this.KeyframeResolver=qs}sortInstanceNodePosition(e,n){return e.compareDocumentPosition(n)&2?1:-1}getBaseTargetFromProps(e,n){return e.style?e.style[n]:void 0}removeValueFromRenderState(e,{vars:n,style:s}){delete n[e],delete s[e]}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);const{children:e}=this.props;L(e)&&(this.childSubscription=e.on("change",n=>{this.current&&(this.current.textContent=`${n}`)}))}}function vc(t){return window.getComputedStyle(t)}class xc extends Zi{constructor(){super(...arguments),this.type="html",this.renderInstance=_i}readValueFromInstance(e,n){if(lt.has(n)){const s=$e(n);return s&&s.default||0}else{const s=vc(e),i=(Is(n)?s.getPropertyValue(n):s[n])||0;return typeof i=="string"?i.trim():i}}measureInstanceViewportBox(e,{transformPagePoint:n}){return Pi(e,n)}build(e,n,s){cn(e,n,s.transformTemplate)}scrapeMotionValuesFromProps(e,n,s){return an(e,n,s)}}class wc extends Zi{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1,this.measureInstanceViewportBox=E}getBaseTargetFromProps(e,n){return e[n]}readValueFromInstance(e,n){if(lt.has(n)){const s=$e(n);return s&&s.default||0}return n=Ui.has(n)?n:en(n),e.getAttribute(n)}scrapeMotionValuesFromProps(e,n,s){return Wi(e,n,s)}build(e,n,s){un(e,n,this.isSVGTag,s.transformTemplate)}renderInstance(e,n,s,i){zi(e,n,s,i)}mount(e){this.isSVGTag=hn(e.tagName),super.mount(e)}}const Tc=(t,e)=>on(t)?new wc(e):new xc(e,{allowProjection:t!==b.Fragment}),Pc=dc({...oa,...jl,...wl,...Rl},Tc),Mc=sr(Pc),Ec=()=>f.jsx("footer",{className:"bg-neutral-900 text-white py-12",children:f.jsxs("div",{className:"container",children:[f.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-8",children:[f.jsxs("div",{children:[f.jsxs("div",{className:"text-2xl font-bold font-montserrat mb-4",children:[f.jsx("span",{className:"text-[#2ECC71]",children:"Free"}),f.jsx("span",{className:"text-[#FFC107]",children:"Energy"})]}),f.jsx("p",{className:"text-gray-400 mb-4",children:"Conectando você às melhores soluções de economia energética do Brasil."}),f.jsxs("div",{className:"flex space-x-4",children:[f.jsx("a",{href:"https://www.instagram.com/freeenergybr/",className:"text-gray-400 hover:text-white transition-colors",target:"_blank",rel:"noopener noreferrer","aria-label":"Instagram da Free Energy",onClick:t=>{window.open("https://www.instagram.com/freeenergybr/","_blank")},children:f.jsx(ee,{className:"h-5 w-5"})}),f.jsx("a",{href:"https://www.linkedin.com/company/free-energy-livre-pra-economizar",className:"text-gray-400 hover:text-white transition-colors",target:"_blank",rel:"noopener noreferrer","aria-label":"LinkedIn da Free Energy",onClick:t=>{window.open("https://www.linkedin.com/company/free-energy-livre-pra-economizar","_blank")},children:f.jsx(ne,{className:"h-5 w-5"})}),f.jsx("a",{href:"https://www.facebook.com/people/Free-Energy-Economize-Com-Energia-Limpa/61575075471555/",className:"text-gray-400 hover:text-white transition-colors",target:"_blank",rel:"noopener noreferrer","aria-label":"Facebook da Free Energy",onClick:t=>{window.open("https://www.facebook.com/people/Free-Energy-Economize-Com-Energia-Limpa/61575075471555/","_blank")},children:f.jsx(te,{className:"h-5 w-5"})})]})]}),f.jsxs("div",{children:[f.jsx("h3",{className:"text-lg font-semibold mb-4",children:"Soluções"}),f.jsxs("ul",{className:"space-y-2",children:[f.jsx("li",{children:f.jsx("a",{href:"#solucoes",className:"text-gray-400 hover:text-white transition-colors",children:"Para Residências"})}),f.jsx("li",{children:f.jsx("a",{href:"#solucoes",className:"text-gray-400 hover:text-white transition-colors",children:"Para Comércios"})}),f.jsx("li",{children:f.jsx("a",{href:"#solucoes",className:"text-gray-400 hover:text-white transition-colors",children:"Para Indústrias"})}),f.jsx("li",{children:f.jsx("a",{href:"#solucoes",className:"text-gray-400 hover:text-white transition-colors",children:"Energia Solar"})})]})]}),f.jsxs("div",{children:[f.jsx("h3",{className:"text-lg font-semibold mb-4",children:"Empresa"}),f.jsxs("ul",{className:"space-y-2",children:[f.jsx("li",{children:f.jsx("a",{href:"/solutions",className:"text-gray-400 hover:text-white transition-colors",children:"Sobre Nós"})}),f.jsx("li",{children:f.jsx("a",{href:"/solutions",className:"text-gray-400 hover:text-white transition-colors",children:"Blog"})}),f.jsx("li",{children:f.jsx("a",{href:"/solutions",className:"text-gray-400 hover:text-white transition-colors",children:"Parceiros"})}),f.jsx("li",{children:f.jsx("a",{href:"/solutions",className:"text-gray-400 hover:text-white transition-colors",children:"Contato"})})]})]}),f.jsxs("div",{children:[f.jsx("h3",{className:"text-lg font-semibold mb-4",children:"Fale Conosco"}),f.jsx("p",{className:"text-gray-400 mb-4",children:"Fique por dentro das novidades em energia limpa e sustentável. Entre em contato direto com nossa equipe."}),f.jsx("div",{children:f.jsxs("a",{href:"/solutions",target:"_self",rel:"noopener noreferrer",className:"inline-flex items-center gap-2 bg-[#2ECC71] hover:bg-green-600 text-white font-bold py-2 px-4 rounded-lg transition-all duration-300",children:["Entrar em contato ",f.jsx(ge,{className:"h-4 w-4"})]})}),f.jsxs("div",{className:"flex space-x-4 mt-4",children:[f.jsx("span",{className:"text-gray-400",children:"Siga-nos: "}),f.jsx("a",{href:"https://www.instagram.com/freeenergybr/",className:"text-gray-400 hover:text-white transition-colors",target:"_blank",rel:"noopener noreferrer",onClick:t=>{window.open("https://www.instagram.com/freeenergybr/","_blank")},children:f.jsx(ee,{className:"h-5 w-5"})}),f.jsx("a",{href:"https://www.linkedin.com/company/free-energy-livre-pra-economizar",className:"text-gray-400 hover:text-white transition-colors",target:"_blank",rel:"noopener noreferrer",onClick:t=>{window.open("https://www.linkedin.com/company/free-energy-livre-pra-economizar","_blank")},children:f.jsx(ne,{className:"h-5 w-5"})}),f.jsx("a",{href:"https://www.facebook.com/people/Free-Energy-Economize-Com-Energia-Limpa/61575075471555/",className:"text-gray-400 hover:text-white transition-colors",target:"_blank",rel:"noopener noreferrer",onClick:t=>{window.open("https://www.facebook.com/people/Free-Energy-Economize-Com-Energia-Limpa/61575075471555/","_blank")},children:f.jsx(te,{className:"h-5 w-5"})})]})]})]}),f.jsxs("div",{className:"mt-12 pt-6 border-t border-gray-800 text-center text-gray-500",children:[f.jsxs("div",{className:"mb-4",children:[f.jsx("a",{href:"/terms-of-use",className:"mx-3 hover:text-gray-300 transition-colors",children:"Termos de Uso"}),f.jsx("a",{href:"/privacy-policy",className:"mx-3 hover:text-gray-300 transition-colors",children:"Política de Privacidade"}),f.jsx("a",{href:"/faq",className:"mx-3 hover:text-gray-300 transition-colors",children:"FAQ"}),f.jsx("a",{href:"/blog",className:"mx-3 hover:text-gray-300 transition-colors",children:"Blog"})]}),f.jsxs("p",{children:["© ",new Date().getFullYear()," Free Energy. Todos os direitos reservados."]}),f.jsx("p",{className:"text-sm mt-1",children:"CNPJ: 59.856.201/0001-38"}),f.jsxs("div",{className:"flex justify-center space-x-4 mt-4",children:[f.jsx("a",{href:"https://www.instagram.com/freeenergybr/",className:"text-gray-500 hover:text-gray-300 transition-colors",target:"_blank",rel:"noopener noreferrer","aria-label":"Instagram da Free Energy",onClick:t=>{window.open("https://www.instagram.com/freeenergybr/","_blank")},children:f.jsx(ee,{className:"h-5 w-5"})}),f.jsx("a",{href:"https://www.linkedin.com/company/free-energy-livre-pra-economizar",className:"text-gray-500 hover:text-gray-300 transition-colors",target:"_blank",rel:"noopener noreferrer","aria-label":"LinkedIn da Free Energy",onClick:t=>{window.open("https://www.linkedin.com/company/free-energy-livre-pra-economizar","_blank")},children:f.jsx(ne,{className:"h-5 w-5"})}),f.jsx("a",{href:"https://www.facebook.com/people/Free-Energy-Economize-Com-Energia-Limpa/61575075471555/",className:"text-gray-500 hover:text-gray-300 transition-colors",target:"_blank",rel:"noopener noreferrer","aria-label":"Facebook da Free Energy",onClick:t=>{window.open("https://www.facebook.com/people/Free-Energy-Economize-Com-Energia-Limpa/61575075471555/","_blank")},children:f.jsx(te,{className:"h-5 w-5"})})]})]})]})});export{ge as A,te as F,Cc as H,ee as I,ne as L,I as S,er as T,Ec as a,Mc as m};
