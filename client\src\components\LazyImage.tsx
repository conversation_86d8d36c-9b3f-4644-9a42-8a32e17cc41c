import React, { useState, useEffect } from 'react';

interface LazyImageProps {
  src: string;
  alt: string;
  className?: string;
  fallbackSrc?: string;
  style?: React.CSSProperties;
  onError?: (e: React.SyntheticEvent<HTMLImageElement, Event>) => void;
  mobileSrc?: string; // Nova propriedade para imagens otimizadas para mobile
}

const LazyImage: React.FC<LazyImageProps> = ({
  src,
  alt,
  className = '',
  fallbackSrc = 'https://images.unsplash.com/photo-1497435334941-8c899ee9e8e9?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=70',
  style = {},
  onError,
  mobileSrc,
}) => {
  const [isLoading, setIsLoading] = useState(true);
  const [imgSrc, setImgSrc] = useState('data:image/gif;base64,R0lGODlhAQABAAAAACH5BAEKAAEALAAAAAABAAEAAAICTAEAOw=='); // Tiny transparent placeholder
  const [isMobile, setIsMobile] = useState(false);

  // Detectar se é dispositivo móvel
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);

    return () => {
      window.removeEventListener('resize', checkMobile);
    };
  }, []);

  // Determinar a URL da imagem com base no dispositivo
  const imageUrl = isMobile && mobileSrc ? mobileSrc : src;

  useEffect(() => {
    // Otimizar a URL da imagem para dispositivos móveis se não houver mobileSrc
    let optimizedSrc = imageUrl;

    if (isMobile && !mobileSrc && imageUrl.includes('unsplash.com')) {
      // Reduzir a qualidade e o tamanho para dispositivos móveis
      optimizedSrc = imageUrl
        .replace('&w=1000', '&w=600')
        .replace('&w=1350', '&w=600')
        .replace('&w=800', '&w=600')
        .replace('&q=80', '&q=70');
    }

    // Create a new image object to preload the image
    const img = new Image();
    img.src = optimizedSrc;

    img.onload = () => {
      setImgSrc(optimizedSrc);
      setIsLoading(false);
    };

    img.onerror = () => {
      if (onError) {
        onError({ currentTarget: img } as React.SyntheticEvent<HTMLImageElement, Event>);
      }
      setImgSrc(fallbackSrc);
      setIsLoading(false);
    };

    return () => {
      img.onload = null;
      img.onerror = null;
    };
  }, [imageUrl, fallbackSrc, onError, isMobile, mobileSrc]);

  const handleImageError = (e: React.SyntheticEvent<HTMLImageElement, Event>) => {
    if (onError) {
      onError(e);
    } else {
      const target = e.target as HTMLImageElement;
      target.src = fallbackSrc;
    }
  };

  return (
    <div
      className={`relative ${className}`}
      style={{
        ...style,
        overflow: 'hidden',
        border: 'none',
        outline: 'none',
        boxShadow: 'none'
      }}
    >
      {isLoading && (
        <div className="absolute inset-0 flex items-center justify-center bg-gray-100 bg-opacity-50">
          <div className="w-8 h-8 border-2 border-t-[#2ECC71] border-gray-200 rounded-full animate-spin"></div>
        </div>
      )}
      <img
        src={imgSrc}
        alt={alt}
        className={`w-full h-full object-cover transition-opacity duration-300 ${isLoading ? 'opacity-0' : 'opacity-100'}`}
        onError={handleImageError}
        loading="lazy"
        style={{
          transform: 'none !important',
          border: 'none',
          outline: 'none',
          boxShadow: 'none'
        }}
      />
    </div>
  );
};

export default LazyImage;
