const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/Home-BLQIGW27.js","assets/Footer-Cfjf8OaO.js","assets/index-CTb3Xjf5.js","assets/search-CtFVmVOx.js","assets/Home-D7qbVthd.css","assets/SolutionsPage-CqM2IPgi.js","assets/arrow-left-B_Q2v2Yq.js","assets/TermsOfUse-w2eVO6tq.js","assets/PrivacyPolicy-D6V83Y8R.js","assets/FAQ-DbRXZAMc.js","assets/Blog-MGj793nO.js","assets/calendar-CgC_-whW.js","assets/BlogPost-fit_wI4g.js"])))=>i.map(i=>d[i]);
var Zu=e=>{throw TypeError(e)};var ll=(e,t,n)=>t.has(e)||Zu("Cannot "+n);var E=(e,t,n)=>(ll(e,t,"read from private field"),n?n.call(e):t.get(e)),V=(e,t,n)=>t.has(e)?Zu("Cannot add the same private member more than once"):t instanceof WeakSet?t.add(e):t.set(e,n),F=(e,t,n,r)=>(ll(e,t,"write to private field"),r?r.call(e,n):t.set(e,n),n),he=(e,t,n)=>(ll(e,t,"access private method"),n);var Po=(e,t,n,r)=>({set _(o){F(e,t,o,n)},get _(){return E(e,t,r)}});function ch(e,t){for(var n=0;n<t.length;n++){const r=t[n];if(typeof r!="string"&&!Array.isArray(r)){for(const o in r)if(o!=="default"&&!(o in e)){const i=Object.getOwnPropertyDescriptor(r,o);i&&Object.defineProperty(e,o,i.get?i:{enumerable:!0,get:()=>r[o]})}}}return Object.freeze(Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}))}(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const o of document.querySelectorAll('link[rel="modulepreload"]'))r(o);new MutationObserver(o=>{for(const i of o)if(i.type==="childList")for(const l of i.addedNodes)l.tagName==="LINK"&&l.rel==="modulepreload"&&r(l)}).observe(document,{childList:!0,subtree:!0});function n(o){const i={};return o.integrity&&(i.integrity=o.integrity),o.referrerPolicy&&(i.referrerPolicy=o.referrerPolicy),o.crossOrigin==="use-credentials"?i.credentials="include":o.crossOrigin==="anonymous"?i.credentials="omit":i.credentials="same-origin",i}function r(o){if(o.ep)return;o.ep=!0;const i=n(o);fetch(o.href,i)}})();function $c(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var Uc={exports:{}},Fi={},bc={exports:{}},$={};/**
 * @license React
 * react.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var vo=Symbol.for("react.element"),dh=Symbol.for("react.portal"),fh=Symbol.for("react.fragment"),ph=Symbol.for("react.strict_mode"),hh=Symbol.for("react.profiler"),mh=Symbol.for("react.provider"),vh=Symbol.for("react.context"),yh=Symbol.for("react.forward_ref"),gh=Symbol.for("react.suspense"),wh=Symbol.for("react.memo"),xh=Symbol.for("react.lazy"),Ju=Symbol.iterator;function Sh(e){return e===null||typeof e!="object"?null:(e=Ju&&e[Ju]||e["@@iterator"],typeof e=="function"?e:null)}var Vc={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},Bc=Object.assign,Wc={};function mr(e,t,n){this.props=e,this.context=t,this.refs=Wc,this.updater=n||Vc}mr.prototype.isReactComponent={};mr.prototype.setState=function(e,t){if(typeof e!="object"&&typeof e!="function"&&e!=null)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")};mr.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")};function Hc(){}Hc.prototype=mr.prototype;function Ws(e,t,n){this.props=e,this.context=t,this.refs=Wc,this.updater=n||Vc}var Hs=Ws.prototype=new Hc;Hs.constructor=Ws;Bc(Hs,mr.prototype);Hs.isPureReactComponent=!0;var ea=Array.isArray,Qc=Object.prototype.hasOwnProperty,Qs={current:null},Kc={key:!0,ref:!0,__self:!0,__source:!0};function Gc(e,t,n){var r,o={},i=null,l=null;if(t!=null)for(r in t.ref!==void 0&&(l=t.ref),t.key!==void 0&&(i=""+t.key),t)Qc.call(t,r)&&!Kc.hasOwnProperty(r)&&(o[r]=t[r]);var s=arguments.length-2;if(s===1)o.children=n;else if(1<s){for(var u=Array(s),a=0;a<s;a++)u[a]=arguments[a+2];o.children=u}if(e&&e.defaultProps)for(r in s=e.defaultProps,s)o[r]===void 0&&(o[r]=s[r]);return{$$typeof:vo,type:e,key:i,ref:l,props:o,_owner:Qs.current}}function Eh(e,t){return{$$typeof:vo,type:e.type,key:t,ref:e.ref,props:e.props,_owner:e._owner}}function Ks(e){return typeof e=="object"&&e!==null&&e.$$typeof===vo}function Ch(e){var t={"=":"=0",":":"=2"};return"$"+e.replace(/[=:]/g,function(n){return t[n]})}var ta=/\/+/g;function sl(e,t){return typeof e=="object"&&e!==null&&e.key!=null?Ch(""+e.key):t.toString(36)}function Ko(e,t,n,r,o){var i=typeof e;(i==="undefined"||i==="boolean")&&(e=null);var l=!1;if(e===null)l=!0;else switch(i){case"string":case"number":l=!0;break;case"object":switch(e.$$typeof){case vo:case dh:l=!0}}if(l)return l=e,o=o(l),e=r===""?"."+sl(l,0):r,ea(o)?(n="",e!=null&&(n=e.replace(ta,"$&/")+"/"),Ko(o,t,n,"",function(a){return a})):o!=null&&(Ks(o)&&(o=Eh(o,n+(!o.key||l&&l.key===o.key?"":(""+o.key).replace(ta,"$&/")+"/")+e)),t.push(o)),1;if(l=0,r=r===""?".":r+":",ea(e))for(var s=0;s<e.length;s++){i=e[s];var u=r+sl(i,s);l+=Ko(i,t,n,u,o)}else if(u=Sh(e),typeof u=="function")for(e=u.call(e),s=0;!(i=e.next()).done;)i=i.value,u=r+sl(i,s++),l+=Ko(i,t,n,u,o);else if(i==="object")throw t=String(e),Error("Objects are not valid as a React child (found: "+(t==="[object Object]"?"object with keys {"+Object.keys(e).join(", ")+"}":t)+"). If you meant to render a collection of children, use an array instead.");return l}function To(e,t,n){if(e==null)return e;var r=[],o=0;return Ko(e,r,"","",function(i){return t.call(n,i,o++)}),r}function kh(e){if(e._status===-1){var t=e._result;t=t(),t.then(function(n){(e._status===0||e._status===-1)&&(e._status=1,e._result=n)},function(n){(e._status===0||e._status===-1)&&(e._status=2,e._result=n)}),e._status===-1&&(e._status=0,e._result=t)}if(e._status===1)return e._result.default;throw e._result}var ke={current:null},Go={transition:null},Ph={ReactCurrentDispatcher:ke,ReactCurrentBatchConfig:Go,ReactCurrentOwner:Qs};function Yc(){throw Error("act(...) is not supported in production builds of React.")}$.Children={map:To,forEach:function(e,t,n){To(e,function(){t.apply(this,arguments)},n)},count:function(e){var t=0;return To(e,function(){t++}),t},toArray:function(e){return To(e,function(t){return t})||[]},only:function(e){if(!Ks(e))throw Error("React.Children.only expected to receive a single React element child.");return e}};$.Component=mr;$.Fragment=fh;$.Profiler=hh;$.PureComponent=Ws;$.StrictMode=ph;$.Suspense=gh;$.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=Ph;$.act=Yc;$.cloneElement=function(e,t,n){if(e==null)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+e+".");var r=Bc({},e.props),o=e.key,i=e.ref,l=e._owner;if(t!=null){if(t.ref!==void 0&&(i=t.ref,l=Qs.current),t.key!==void 0&&(o=""+t.key),e.type&&e.type.defaultProps)var s=e.type.defaultProps;for(u in t)Qc.call(t,u)&&!Kc.hasOwnProperty(u)&&(r[u]=t[u]===void 0&&s!==void 0?s[u]:t[u])}var u=arguments.length-2;if(u===1)r.children=n;else if(1<u){s=Array(u);for(var a=0;a<u;a++)s[a]=arguments[a+2];r.children=s}return{$$typeof:vo,type:e.type,key:o,ref:i,props:r,_owner:l}};$.createContext=function(e){return e={$$typeof:vh,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null},e.Provider={$$typeof:mh,_context:e},e.Consumer=e};$.createElement=Gc;$.createFactory=function(e){var t=Gc.bind(null,e);return t.type=e,t};$.createRef=function(){return{current:null}};$.forwardRef=function(e){return{$$typeof:yh,render:e}};$.isValidElement=Ks;$.lazy=function(e){return{$$typeof:xh,_payload:{_status:-1,_result:e},_init:kh}};$.memo=function(e,t){return{$$typeof:wh,type:e,compare:t===void 0?null:t}};$.startTransition=function(e){var t=Go.transition;Go.transition={};try{e()}finally{Go.transition=t}};$.unstable_act=Yc;$.useCallback=function(e,t){return ke.current.useCallback(e,t)};$.useContext=function(e){return ke.current.useContext(e)};$.useDebugValue=function(){};$.useDeferredValue=function(e){return ke.current.useDeferredValue(e)};$.useEffect=function(e,t){return ke.current.useEffect(e,t)};$.useId=function(){return ke.current.useId()};$.useImperativeHandle=function(e,t,n){return ke.current.useImperativeHandle(e,t,n)};$.useInsertionEffect=function(e,t){return ke.current.useInsertionEffect(e,t)};$.useLayoutEffect=function(e,t){return ke.current.useLayoutEffect(e,t)};$.useMemo=function(e,t){return ke.current.useMemo(e,t)};$.useReducer=function(e,t,n){return ke.current.useReducer(e,t,n)};$.useRef=function(e){return ke.current.useRef(e)};$.useState=function(e){return ke.current.useState(e)};$.useSyncExternalStore=function(e,t,n){return ke.current.useSyncExternalStore(e,t,n)};$.useTransition=function(){return ke.current.useTransition()};$.version="18.3.1";bc.exports=$;var m=bc.exports;const zt=$c(m),Gw=ch({__proto__:null,default:zt},[m]);/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Th=m,Rh=Symbol.for("react.element"),_h=Symbol.for("react.fragment"),Nh=Object.prototype.hasOwnProperty,Lh=Th.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,Oh={key:!0,ref:!0,__self:!0,__source:!0};function Xc(e,t,n){var r,o={},i=null,l=null;n!==void 0&&(i=""+n),t.key!==void 0&&(i=""+t.key),t.ref!==void 0&&(l=t.ref);for(r in t)Nh.call(t,r)&&!Oh.hasOwnProperty(r)&&(o[r]=t[r]);if(e&&e.defaultProps)for(r in t=e.defaultProps,t)o[r]===void 0&&(o[r]=t[r]);return{$$typeof:Rh,type:e,key:i,ref:l,props:o,_owner:Lh.current}}Fi.Fragment=_h;Fi.jsx=Xc;Fi.jsxs=Xc;Uc.exports=Fi;var R=Uc.exports,qc={exports:{}},ze={},Zc={exports:{}},Jc={};/**
 * @license React
 * scheduler.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */(function(e){function t(N,I){var D=N.length;N.push(I);e:for(;0<D;){var U=D-1>>>1,ee=N[U];if(0<o(ee,I))N[U]=I,N[D]=ee,D=U;else break e}}function n(N){return N.length===0?null:N[0]}function r(N){if(N.length===0)return null;var I=N[0],D=N.pop();if(D!==I){N[0]=D;e:for(var U=0,ee=N.length,vt=ee>>>1;U<vt;){var rt=2*(U+1)-1,il=N[rt],dn=rt+1,ko=N[dn];if(0>o(il,D))dn<ee&&0>o(ko,il)?(N[U]=ko,N[dn]=D,U=dn):(N[U]=il,N[rt]=D,U=rt);else if(dn<ee&&0>o(ko,D))N[U]=ko,N[dn]=D,U=dn;else break e}}return I}function o(N,I){var D=N.sortIndex-I.sortIndex;return D!==0?D:N.id-I.id}if(typeof performance=="object"&&typeof performance.now=="function"){var i=performance;e.unstable_now=function(){return i.now()}}else{var l=Date,s=l.now();e.unstable_now=function(){return l.now()-s}}var u=[],a=[],f=1,p=null,v=3,g=!1,w=!1,y=!1,x=typeof setTimeout=="function"?setTimeout:null,d=typeof clearTimeout=="function"?clearTimeout:null,c=typeof setImmediate<"u"?setImmediate:null;typeof navigator<"u"&&navigator.scheduling!==void 0&&navigator.scheduling.isInputPending!==void 0&&navigator.scheduling.isInputPending.bind(navigator.scheduling);function h(N){for(var I=n(a);I!==null;){if(I.callback===null)r(a);else if(I.startTime<=N)r(a),I.sortIndex=I.expirationTime,t(u,I);else break;I=n(a)}}function S(N){if(y=!1,h(N),!w)if(n(u)!==null)w=!0,ce(C);else{var I=n(a);I!==null&&$e(S,I.startTime-N)}}function C(N,I){w=!1,y&&(y=!1,d(_),_=-1),g=!0;var D=v;try{for(h(I),p=n(u);p!==null&&(!(p.expirationTime>I)||N&&!H());){var U=p.callback;if(typeof U=="function"){p.callback=null,v=p.priorityLevel;var ee=U(p.expirationTime<=I);I=e.unstable_now(),typeof ee=="function"?p.callback=ee:p===n(u)&&r(u),h(I)}else r(u);p=n(u)}if(p!==null)var vt=!0;else{var rt=n(a);rt!==null&&$e(S,rt.startTime-I),vt=!1}return vt}finally{p=null,v=D,g=!1}}var T=!1,k=null,_=-1,L=5,M=-1;function H(){return!(e.unstable_now()-M<L)}function j(){if(k!==null){var N=e.unstable_now();M=N;var I=!0;try{I=k(!0,N)}finally{I?Oe():(T=!1,k=null)}}else T=!1}var Oe;if(typeof c=="function")Oe=function(){c(j)};else if(typeof MessageChannel<"u"){var A=new MessageChannel,ae=A.port2;A.port1.onmessage=j,Oe=function(){ae.postMessage(null)}}else Oe=function(){x(j,0)};function ce(N){k=N,T||(T=!0,Oe())}function $e(N,I){_=x(function(){N(e.unstable_now())},I)}e.unstable_IdlePriority=5,e.unstable_ImmediatePriority=1,e.unstable_LowPriority=4,e.unstable_NormalPriority=3,e.unstable_Profiling=null,e.unstable_UserBlockingPriority=2,e.unstable_cancelCallback=function(N){N.callback=null},e.unstable_continueExecution=function(){w||g||(w=!0,ce(C))},e.unstable_forceFrameRate=function(N){0>N||125<N?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):L=0<N?Math.floor(1e3/N):5},e.unstable_getCurrentPriorityLevel=function(){return v},e.unstable_getFirstCallbackNode=function(){return n(u)},e.unstable_next=function(N){switch(v){case 1:case 2:case 3:var I=3;break;default:I=v}var D=v;v=I;try{return N()}finally{v=D}},e.unstable_pauseExecution=function(){},e.unstable_requestPaint=function(){},e.unstable_runWithPriority=function(N,I){switch(N){case 1:case 2:case 3:case 4:case 5:break;default:N=3}var D=v;v=N;try{return I()}finally{v=D}},e.unstable_scheduleCallback=function(N,I,D){var U=e.unstable_now();switch(typeof D=="object"&&D!==null?(D=D.delay,D=typeof D=="number"&&0<D?U+D:U):D=U,N){case 1:var ee=-1;break;case 2:ee=250;break;case 5:ee=**********;break;case 4:ee=1e4;break;default:ee=5e3}return ee=D+ee,N={id:f++,callback:I,priorityLevel:N,startTime:D,expirationTime:ee,sortIndex:-1},D>U?(N.sortIndex=D,t(a,N),n(u)===null&&N===n(a)&&(y?(d(_),_=-1):y=!0,$e(S,D-U))):(N.sortIndex=ee,t(u,N),w||g||(w=!0,ce(C))),N},e.unstable_shouldYield=H,e.unstable_wrapCallback=function(N){var I=v;return function(){var D=v;v=I;try{return N.apply(this,arguments)}finally{v=D}}}})(Jc);Zc.exports=Jc;var Mh=Zc.exports;/**
 * @license React
 * react-dom.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Ih=m,Fe=Mh;function P(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,n=1;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var ed=new Set,Kr={};function Ln(e,t){ur(e,t),ur(e+"Capture",t)}function ur(e,t){for(Kr[e]=t,e=0;e<t.length;e++)ed.add(t[e])}var Rt=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),bl=Object.prototype.hasOwnProperty,Ah=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,na={},ra={};function Dh(e){return bl.call(ra,e)?!0:bl.call(na,e)?!1:Ah.test(e)?ra[e]=!0:(na[e]=!0,!1)}function Fh(e,t,n,r){if(n!==null&&n.type===0)return!1;switch(typeof t){case"function":case"symbol":return!0;case"boolean":return r?!1:n!==null?!n.acceptsBooleans:(e=e.toLowerCase().slice(0,5),e!=="data-"&&e!=="aria-");default:return!1}}function zh(e,t,n,r){if(t===null||typeof t>"u"||Fh(e,t,n,r))return!0;if(r)return!1;if(n!==null)switch(n.type){case 3:return!t;case 4:return t===!1;case 5:return isNaN(t);case 6:return isNaN(t)||1>t}return!1}function Pe(e,t,n,r,o,i,l){this.acceptsBooleans=t===2||t===3||t===4,this.attributeName=r,this.attributeNamespace=o,this.mustUseProperty=n,this.propertyName=e,this.type=t,this.sanitizeURL=i,this.removeEmptyString=l}var pe={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach(function(e){pe[e]=new Pe(e,0,!1,e,null,!1,!1)});[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach(function(e){var t=e[0];pe[t]=new Pe(t,1,!1,e[1],null,!1,!1)});["contentEditable","draggable","spellCheck","value"].forEach(function(e){pe[e]=new Pe(e,2,!1,e.toLowerCase(),null,!1,!1)});["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach(function(e){pe[e]=new Pe(e,2,!1,e,null,!1,!1)});"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach(function(e){pe[e]=new Pe(e,3,!1,e.toLowerCase(),null,!1,!1)});["checked","multiple","muted","selected"].forEach(function(e){pe[e]=new Pe(e,3,!0,e,null,!1,!1)});["capture","download"].forEach(function(e){pe[e]=new Pe(e,4,!1,e,null,!1,!1)});["cols","rows","size","span"].forEach(function(e){pe[e]=new Pe(e,6,!1,e,null,!1,!1)});["rowSpan","start"].forEach(function(e){pe[e]=new Pe(e,5,!1,e.toLowerCase(),null,!1,!1)});var Gs=/[\-:]([a-z])/g;function Ys(e){return e[1].toUpperCase()}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach(function(e){var t=e.replace(Gs,Ys);pe[t]=new Pe(t,1,!1,e,null,!1,!1)});"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach(function(e){var t=e.replace(Gs,Ys);pe[t]=new Pe(t,1,!1,e,"http://www.w3.org/1999/xlink",!1,!1)});["xml:base","xml:lang","xml:space"].forEach(function(e){var t=e.replace(Gs,Ys);pe[t]=new Pe(t,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)});["tabIndex","crossOrigin"].forEach(function(e){pe[e]=new Pe(e,1,!1,e.toLowerCase(),null,!1,!1)});pe.xlinkHref=new Pe("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1);["src","href","action","formAction"].forEach(function(e){pe[e]=new Pe(e,1,!1,e.toLowerCase(),null,!0,!0)});function Xs(e,t,n,r){var o=pe.hasOwnProperty(t)?pe[t]:null;(o!==null?o.type!==0:r||!(2<t.length)||t[0]!=="o"&&t[0]!=="O"||t[1]!=="n"&&t[1]!=="N")&&(zh(t,n,o,r)&&(n=null),r||o===null?Dh(t)&&(n===null?e.removeAttribute(t):e.setAttribute(t,""+n)):o.mustUseProperty?e[o.propertyName]=n===null?o.type===3?!1:"":n:(t=o.attributeName,r=o.attributeNamespace,n===null?e.removeAttribute(t):(o=o.type,n=o===3||o===4&&n===!0?"":""+n,r?e.setAttributeNS(r,t,n):e.setAttribute(t,n))))}var It=Ih.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,Ro=Symbol.for("react.element"),An=Symbol.for("react.portal"),Dn=Symbol.for("react.fragment"),qs=Symbol.for("react.strict_mode"),Vl=Symbol.for("react.profiler"),td=Symbol.for("react.provider"),nd=Symbol.for("react.context"),Zs=Symbol.for("react.forward_ref"),Bl=Symbol.for("react.suspense"),Wl=Symbol.for("react.suspense_list"),Js=Symbol.for("react.memo"),$t=Symbol.for("react.lazy"),rd=Symbol.for("react.offscreen"),oa=Symbol.iterator;function Er(e){return e===null||typeof e!="object"?null:(e=oa&&e[oa]||e["@@iterator"],typeof e=="function"?e:null)}var J=Object.assign,ul;function Ir(e){if(ul===void 0)try{throw Error()}catch(n){var t=n.stack.trim().match(/\n( *(at )?)/);ul=t&&t[1]||""}return`
`+ul+e}var al=!1;function cl(e,t){if(!e||al)return"";al=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(t)if(t=function(){throw Error()},Object.defineProperty(t.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(t,[])}catch(a){var r=a}Reflect.construct(e,[],t)}else{try{t.call()}catch(a){r=a}e.call(t.prototype)}else{try{throw Error()}catch(a){r=a}e()}}catch(a){if(a&&r&&typeof a.stack=="string"){for(var o=a.stack.split(`
`),i=r.stack.split(`
`),l=o.length-1,s=i.length-1;1<=l&&0<=s&&o[l]!==i[s];)s--;for(;1<=l&&0<=s;l--,s--)if(o[l]!==i[s]){if(l!==1||s!==1)do if(l--,s--,0>s||o[l]!==i[s]){var u=`
`+o[l].replace(" at new "," at ");return e.displayName&&u.includes("<anonymous>")&&(u=u.replace("<anonymous>",e.displayName)),u}while(1<=l&&0<=s);break}}}finally{al=!1,Error.prepareStackTrace=n}return(e=e?e.displayName||e.name:"")?Ir(e):""}function jh(e){switch(e.tag){case 5:return Ir(e.type);case 16:return Ir("Lazy");case 13:return Ir("Suspense");case 19:return Ir("SuspenseList");case 0:case 2:case 15:return e=cl(e.type,!1),e;case 11:return e=cl(e.type.render,!1),e;case 1:return e=cl(e.type,!0),e;default:return""}}function Hl(e){if(e==null)return null;if(typeof e=="function")return e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case Dn:return"Fragment";case An:return"Portal";case Vl:return"Profiler";case qs:return"StrictMode";case Bl:return"Suspense";case Wl:return"SuspenseList"}if(typeof e=="object")switch(e.$$typeof){case nd:return(e.displayName||"Context")+".Consumer";case td:return(e._context.displayName||"Context")+".Provider";case Zs:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case Js:return t=e.displayName||null,t!==null?t:Hl(e.type)||"Memo";case $t:t=e._payload,e=e._init;try{return Hl(e(t))}catch{}}return null}function $h(e){var t=e.type;switch(e.tag){case 24:return"Cache";case 9:return(t.displayName||"Context")+".Consumer";case 10:return(t._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return e=t.render,e=e.displayName||e.name||"",t.displayName||(e!==""?"ForwardRef("+e+")":"ForwardRef");case 7:return"Fragment";case 5:return t;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return Hl(t);case 8:return t===qs?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if(typeof t=="function")return t.displayName||t.name||null;if(typeof t=="string")return t}return null}function on(e){switch(typeof e){case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function od(e){var t=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(t==="checkbox"||t==="radio")}function Uh(e){var t=od(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),r=""+e[t];if(!e.hasOwnProperty(t)&&typeof n<"u"&&typeof n.get=="function"&&typeof n.set=="function"){var o=n.get,i=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return o.call(this)},set:function(l){r=""+l,i.call(this,l)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return r},setValue:function(l){r=""+l},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}function _o(e){e._valueTracker||(e._valueTracker=Uh(e))}function id(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),r="";return e&&(r=od(e)?e.checked?"true":"false":e.value),e=r,e!==n?(t.setValue(e),!0):!1}function ai(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}function Ql(e,t){var n=t.checked;return J({},t,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:n??e._wrapperState.initialChecked})}function ia(e,t){var n=t.defaultValue==null?"":t.defaultValue,r=t.checked!=null?t.checked:t.defaultChecked;n=on(t.value!=null?t.value:n),e._wrapperState={initialChecked:r,initialValue:n,controlled:t.type==="checkbox"||t.type==="radio"?t.checked!=null:t.value!=null}}function ld(e,t){t=t.checked,t!=null&&Xs(e,"checked",t,!1)}function Kl(e,t){ld(e,t);var n=on(t.value),r=t.type;if(n!=null)r==="number"?(n===0&&e.value===""||e.value!=n)&&(e.value=""+n):e.value!==""+n&&(e.value=""+n);else if(r==="submit"||r==="reset"){e.removeAttribute("value");return}t.hasOwnProperty("value")?Gl(e,t.type,n):t.hasOwnProperty("defaultValue")&&Gl(e,t.type,on(t.defaultValue)),t.checked==null&&t.defaultChecked!=null&&(e.defaultChecked=!!t.defaultChecked)}function la(e,t,n){if(t.hasOwnProperty("value")||t.hasOwnProperty("defaultValue")){var r=t.type;if(!(r!=="submit"&&r!=="reset"||t.value!==void 0&&t.value!==null))return;t=""+e._wrapperState.initialValue,n||t===e.value||(e.value=t),e.defaultValue=t}n=e.name,n!==""&&(e.name=""),e.defaultChecked=!!e._wrapperState.initialChecked,n!==""&&(e.name=n)}function Gl(e,t,n){(t!=="number"||ai(e.ownerDocument)!==e)&&(n==null?e.defaultValue=""+e._wrapperState.initialValue:e.defaultValue!==""+n&&(e.defaultValue=""+n))}var Ar=Array.isArray;function Qn(e,t,n,r){if(e=e.options,t){t={};for(var o=0;o<n.length;o++)t["$"+n[o]]=!0;for(n=0;n<e.length;n++)o=t.hasOwnProperty("$"+e[n].value),e[n].selected!==o&&(e[n].selected=o),o&&r&&(e[n].defaultSelected=!0)}else{for(n=""+on(n),t=null,o=0;o<e.length;o++){if(e[o].value===n){e[o].selected=!0,r&&(e[o].defaultSelected=!0);return}t!==null||e[o].disabled||(t=e[o])}t!==null&&(t.selected=!0)}}function Yl(e,t){if(t.dangerouslySetInnerHTML!=null)throw Error(P(91));return J({},t,{value:void 0,defaultValue:void 0,children:""+e._wrapperState.initialValue})}function sa(e,t){var n=t.value;if(n==null){if(n=t.children,t=t.defaultValue,n!=null){if(t!=null)throw Error(P(92));if(Ar(n)){if(1<n.length)throw Error(P(93));n=n[0]}t=n}t==null&&(t=""),n=t}e._wrapperState={initialValue:on(n)}}function sd(e,t){var n=on(t.value),r=on(t.defaultValue);n!=null&&(n=""+n,n!==e.value&&(e.value=n),t.defaultValue==null&&e.defaultValue!==n&&(e.defaultValue=n)),r!=null&&(e.defaultValue=""+r)}function ua(e){var t=e.textContent;t===e._wrapperState.initialValue&&t!==""&&t!==null&&(e.value=t)}function ud(e){switch(e){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function Xl(e,t){return e==null||e==="http://www.w3.org/1999/xhtml"?ud(t):e==="http://www.w3.org/2000/svg"&&t==="foreignObject"?"http://www.w3.org/1999/xhtml":e}var No,ad=function(e){return typeof MSApp<"u"&&MSApp.execUnsafeLocalFunction?function(t,n,r,o){MSApp.execUnsafeLocalFunction(function(){return e(t,n,r,o)})}:e}(function(e,t){if(e.namespaceURI!=="http://www.w3.org/2000/svg"||"innerHTML"in e)e.innerHTML=t;else{for(No=No||document.createElement("div"),No.innerHTML="<svg>"+t.valueOf().toString()+"</svg>",t=No.firstChild;e.firstChild;)e.removeChild(e.firstChild);for(;t.firstChild;)e.appendChild(t.firstChild)}});function Gr(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&n.nodeType===3){n.nodeValue=t;return}}e.textContent=t}var zr={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},bh=["Webkit","ms","Moz","O"];Object.keys(zr).forEach(function(e){bh.forEach(function(t){t=t+e.charAt(0).toUpperCase()+e.substring(1),zr[t]=zr[e]})});function cd(e,t,n){return t==null||typeof t=="boolean"||t===""?"":n||typeof t!="number"||t===0||zr.hasOwnProperty(e)&&zr[e]?(""+t).trim():t+"px"}function dd(e,t){e=e.style;for(var n in t)if(t.hasOwnProperty(n)){var r=n.indexOf("--")===0,o=cd(n,t[n],r);n==="float"&&(n="cssFloat"),r?e.setProperty(n,o):e[n]=o}}var Vh=J({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function ql(e,t){if(t){if(Vh[e]&&(t.children!=null||t.dangerouslySetInnerHTML!=null))throw Error(P(137,e));if(t.dangerouslySetInnerHTML!=null){if(t.children!=null)throw Error(P(60));if(typeof t.dangerouslySetInnerHTML!="object"||!("__html"in t.dangerouslySetInnerHTML))throw Error(P(61))}if(t.style!=null&&typeof t.style!="object")throw Error(P(62))}}function Zl(e,t){if(e.indexOf("-")===-1)return typeof t.is=="string";switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var Jl=null;function eu(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var es=null,Kn=null,Gn=null;function aa(e){if(e=wo(e)){if(typeof es!="function")throw Error(P(280));var t=e.stateNode;t&&(t=bi(t),es(e.stateNode,e.type,t))}}function fd(e){Kn?Gn?Gn.push(e):Gn=[e]:Kn=e}function pd(){if(Kn){var e=Kn,t=Gn;if(Gn=Kn=null,aa(e),t)for(e=0;e<t.length;e++)aa(t[e])}}function hd(e,t){return e(t)}function md(){}var dl=!1;function vd(e,t,n){if(dl)return e(t,n);dl=!0;try{return hd(e,t,n)}finally{dl=!1,(Kn!==null||Gn!==null)&&(md(),pd())}}function Yr(e,t){var n=e.stateNode;if(n===null)return null;var r=bi(n);if(r===null)return null;n=r[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(r=!r.disabled)||(e=e.type,r=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!r;break e;default:e=!1}if(e)return null;if(n&&typeof n!="function")throw Error(P(231,t,typeof n));return n}var ts=!1;if(Rt)try{var Cr={};Object.defineProperty(Cr,"passive",{get:function(){ts=!0}}),window.addEventListener("test",Cr,Cr),window.removeEventListener("test",Cr,Cr)}catch{ts=!1}function Bh(e,t,n,r,o,i,l,s,u){var a=Array.prototype.slice.call(arguments,3);try{t.apply(n,a)}catch(f){this.onError(f)}}var jr=!1,ci=null,di=!1,ns=null,Wh={onError:function(e){jr=!0,ci=e}};function Hh(e,t,n,r,o,i,l,s,u){jr=!1,ci=null,Bh.apply(Wh,arguments)}function Qh(e,t,n,r,o,i,l,s,u){if(Hh.apply(this,arguments),jr){if(jr){var a=ci;jr=!1,ci=null}else throw Error(P(198));di||(di=!0,ns=a)}}function On(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do t=e,t.flags&4098&&(n=t.return),e=t.return;while(e)}return t.tag===3?n:null}function yd(e){if(e.tag===13){var t=e.memoizedState;if(t===null&&(e=e.alternate,e!==null&&(t=e.memoizedState)),t!==null)return t.dehydrated}return null}function ca(e){if(On(e)!==e)throw Error(P(188))}function Kh(e){var t=e.alternate;if(!t){if(t=On(e),t===null)throw Error(P(188));return t!==e?null:e}for(var n=e,r=t;;){var o=n.return;if(o===null)break;var i=o.alternate;if(i===null){if(r=o.return,r!==null){n=r;continue}break}if(o.child===i.child){for(i=o.child;i;){if(i===n)return ca(o),e;if(i===r)return ca(o),t;i=i.sibling}throw Error(P(188))}if(n.return!==r.return)n=o,r=i;else{for(var l=!1,s=o.child;s;){if(s===n){l=!0,n=o,r=i;break}if(s===r){l=!0,r=o,n=i;break}s=s.sibling}if(!l){for(s=i.child;s;){if(s===n){l=!0,n=i,r=o;break}if(s===r){l=!0,r=i,n=o;break}s=s.sibling}if(!l)throw Error(P(189))}}if(n.alternate!==r)throw Error(P(190))}if(n.tag!==3)throw Error(P(188));return n.stateNode.current===n?e:t}function gd(e){return e=Kh(e),e!==null?wd(e):null}function wd(e){if(e.tag===5||e.tag===6)return e;for(e=e.child;e!==null;){var t=wd(e);if(t!==null)return t;e=e.sibling}return null}var xd=Fe.unstable_scheduleCallback,da=Fe.unstable_cancelCallback,Gh=Fe.unstable_shouldYield,Yh=Fe.unstable_requestPaint,re=Fe.unstable_now,Xh=Fe.unstable_getCurrentPriorityLevel,tu=Fe.unstable_ImmediatePriority,Sd=Fe.unstable_UserBlockingPriority,fi=Fe.unstable_NormalPriority,qh=Fe.unstable_LowPriority,Ed=Fe.unstable_IdlePriority,zi=null,ct=null;function Zh(e){if(ct&&typeof ct.onCommitFiberRoot=="function")try{ct.onCommitFiberRoot(zi,e,void 0,(e.current.flags&128)===128)}catch{}}var et=Math.clz32?Math.clz32:tm,Jh=Math.log,em=Math.LN2;function tm(e){return e>>>=0,e===0?32:31-(Jh(e)/em|0)|0}var Lo=64,Oo=4194304;function Dr(e){switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194240;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return e&130023424;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return e}}function pi(e,t){var n=e.pendingLanes;if(n===0)return 0;var r=0,o=e.suspendedLanes,i=e.pingedLanes,l=n&268435455;if(l!==0){var s=l&~o;s!==0?r=Dr(s):(i&=l,i!==0&&(r=Dr(i)))}else l=n&~o,l!==0?r=Dr(l):i!==0&&(r=Dr(i));if(r===0)return 0;if(t!==0&&t!==r&&!(t&o)&&(o=r&-r,i=t&-t,o>=i||o===16&&(i&4194240)!==0))return t;if(r&4&&(r|=n&16),t=e.entangledLanes,t!==0)for(e=e.entanglements,t&=r;0<t;)n=31-et(t),o=1<<n,r|=e[n],t&=~o;return r}function nm(e,t){switch(e){case 1:case 2:case 4:return t+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return-1;case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function rm(e,t){for(var n=e.suspendedLanes,r=e.pingedLanes,o=e.expirationTimes,i=e.pendingLanes;0<i;){var l=31-et(i),s=1<<l,u=o[l];u===-1?(!(s&n)||s&r)&&(o[l]=nm(s,t)):u<=t&&(e.expiredLanes|=s),i&=~s}}function rs(e){return e=e.pendingLanes&-1073741825,e!==0?e:e&1073741824?1073741824:0}function Cd(){var e=Lo;return Lo<<=1,!(Lo&4194240)&&(Lo=64),e}function fl(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function yo(e,t,n){e.pendingLanes|=t,t!==536870912&&(e.suspendedLanes=0,e.pingedLanes=0),e=e.eventTimes,t=31-et(t),e[t]=n}function om(e,t){var n=e.pendingLanes&~t;e.pendingLanes=t,e.suspendedLanes=0,e.pingedLanes=0,e.expiredLanes&=t,e.mutableReadLanes&=t,e.entangledLanes&=t,t=e.entanglements;var r=e.eventTimes;for(e=e.expirationTimes;0<n;){var o=31-et(n),i=1<<o;t[o]=0,r[o]=-1,e[o]=-1,n&=~i}}function nu(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var r=31-et(n),o=1<<r;o&t|e[r]&t&&(e[r]|=t),n&=~o}}var B=0;function kd(e){return e&=-e,1<e?4<e?e&268435455?16:536870912:4:1}var Pd,ru,Td,Rd,_d,os=!1,Mo=[],Xt=null,qt=null,Zt=null,Xr=new Map,qr=new Map,bt=[],im="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function fa(e,t){switch(e){case"focusin":case"focusout":Xt=null;break;case"dragenter":case"dragleave":qt=null;break;case"mouseover":case"mouseout":Zt=null;break;case"pointerover":case"pointerout":Xr.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":qr.delete(t.pointerId)}}function kr(e,t,n,r,o,i){return e===null||e.nativeEvent!==i?(e={blockedOn:t,domEventName:n,eventSystemFlags:r,nativeEvent:i,targetContainers:[o]},t!==null&&(t=wo(t),t!==null&&ru(t)),e):(e.eventSystemFlags|=r,t=e.targetContainers,o!==null&&t.indexOf(o)===-1&&t.push(o),e)}function lm(e,t,n,r,o){switch(t){case"focusin":return Xt=kr(Xt,e,t,n,r,o),!0;case"dragenter":return qt=kr(qt,e,t,n,r,o),!0;case"mouseover":return Zt=kr(Zt,e,t,n,r,o),!0;case"pointerover":var i=o.pointerId;return Xr.set(i,kr(Xr.get(i)||null,e,t,n,r,o)),!0;case"gotpointercapture":return i=o.pointerId,qr.set(i,kr(qr.get(i)||null,e,t,n,r,o)),!0}return!1}function Nd(e){var t=hn(e.target);if(t!==null){var n=On(t);if(n!==null){if(t=n.tag,t===13){if(t=yd(n),t!==null){e.blockedOn=t,_d(e.priority,function(){Td(n)});return}}else if(t===3&&n.stateNode.current.memoizedState.isDehydrated){e.blockedOn=n.tag===3?n.stateNode.containerInfo:null;return}}}e.blockedOn=null}function Yo(e){if(e.blockedOn!==null)return!1;for(var t=e.targetContainers;0<t.length;){var n=is(e.domEventName,e.eventSystemFlags,t[0],e.nativeEvent);if(n===null){n=e.nativeEvent;var r=new n.constructor(n.type,n);Jl=r,n.target.dispatchEvent(r),Jl=null}else return t=wo(n),t!==null&&ru(t),e.blockedOn=n,!1;t.shift()}return!0}function pa(e,t,n){Yo(e)&&n.delete(t)}function sm(){os=!1,Xt!==null&&Yo(Xt)&&(Xt=null),qt!==null&&Yo(qt)&&(qt=null),Zt!==null&&Yo(Zt)&&(Zt=null),Xr.forEach(pa),qr.forEach(pa)}function Pr(e,t){e.blockedOn===t&&(e.blockedOn=null,os||(os=!0,Fe.unstable_scheduleCallback(Fe.unstable_NormalPriority,sm)))}function Zr(e){function t(o){return Pr(o,e)}if(0<Mo.length){Pr(Mo[0],e);for(var n=1;n<Mo.length;n++){var r=Mo[n];r.blockedOn===e&&(r.blockedOn=null)}}for(Xt!==null&&Pr(Xt,e),qt!==null&&Pr(qt,e),Zt!==null&&Pr(Zt,e),Xr.forEach(t),qr.forEach(t),n=0;n<bt.length;n++)r=bt[n],r.blockedOn===e&&(r.blockedOn=null);for(;0<bt.length&&(n=bt[0],n.blockedOn===null);)Nd(n),n.blockedOn===null&&bt.shift()}var Yn=It.ReactCurrentBatchConfig,hi=!0;function um(e,t,n,r){var o=B,i=Yn.transition;Yn.transition=null;try{B=1,ou(e,t,n,r)}finally{B=o,Yn.transition=i}}function am(e,t,n,r){var o=B,i=Yn.transition;Yn.transition=null;try{B=4,ou(e,t,n,r)}finally{B=o,Yn.transition=i}}function ou(e,t,n,r){if(hi){var o=is(e,t,n,r);if(o===null)El(e,t,r,mi,n),fa(e,r);else if(lm(o,e,t,n,r))r.stopPropagation();else if(fa(e,r),t&4&&-1<im.indexOf(e)){for(;o!==null;){var i=wo(o);if(i!==null&&Pd(i),i=is(e,t,n,r),i===null&&El(e,t,r,mi,n),i===o)break;o=i}o!==null&&r.stopPropagation()}else El(e,t,r,null,n)}}var mi=null;function is(e,t,n,r){if(mi=null,e=eu(r),e=hn(e),e!==null)if(t=On(e),t===null)e=null;else if(n=t.tag,n===13){if(e=yd(t),e!==null)return e;e=null}else if(n===3){if(t.stateNode.current.memoizedState.isDehydrated)return t.tag===3?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null);return mi=e,null}function Ld(e){switch(e){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch(Xh()){case tu:return 1;case Sd:return 4;case fi:case qh:return 16;case Ed:return 536870912;default:return 16}default:return 16}}var Gt=null,iu=null,Xo=null;function Od(){if(Xo)return Xo;var e,t=iu,n=t.length,r,o="value"in Gt?Gt.value:Gt.textContent,i=o.length;for(e=0;e<n&&t[e]===o[e];e++);var l=n-e;for(r=1;r<=l&&t[n-r]===o[i-r];r++);return Xo=o.slice(e,1<r?1-r:void 0)}function qo(e){var t=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&t===13&&(e=13)):e=t,e===10&&(e=13),32<=e||e===13?e:0}function Io(){return!0}function ha(){return!1}function je(e){function t(n,r,o,i,l){this._reactName=n,this._targetInst=o,this.type=r,this.nativeEvent=i,this.target=l,this.currentTarget=null;for(var s in e)e.hasOwnProperty(s)&&(n=e[s],this[s]=n?n(i):i[s]);return this.isDefaultPrevented=(i.defaultPrevented!=null?i.defaultPrevented:i.returnValue===!1)?Io:ha,this.isPropagationStopped=ha,this}return J(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var n=this.nativeEvent;n&&(n.preventDefault?n.preventDefault():typeof n.returnValue!="unknown"&&(n.returnValue=!1),this.isDefaultPrevented=Io)},stopPropagation:function(){var n=this.nativeEvent;n&&(n.stopPropagation?n.stopPropagation():typeof n.cancelBubble!="unknown"&&(n.cancelBubble=!0),this.isPropagationStopped=Io)},persist:function(){},isPersistent:Io}),t}var vr={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},lu=je(vr),go=J({},vr,{view:0,detail:0}),cm=je(go),pl,hl,Tr,ji=J({},go,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:su,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==Tr&&(Tr&&e.type==="mousemove"?(pl=e.screenX-Tr.screenX,hl=e.screenY-Tr.screenY):hl=pl=0,Tr=e),pl)},movementY:function(e){return"movementY"in e?e.movementY:hl}}),ma=je(ji),dm=J({},ji,{dataTransfer:0}),fm=je(dm),pm=J({},go,{relatedTarget:0}),ml=je(pm),hm=J({},vr,{animationName:0,elapsedTime:0,pseudoElement:0}),mm=je(hm),vm=J({},vr,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),ym=je(vm),gm=J({},vr,{data:0}),va=je(gm),wm={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},xm={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},Sm={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function Em(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):(e=Sm[e])?!!t[e]:!1}function su(){return Em}var Cm=J({},go,{key:function(e){if(e.key){var t=wm[e.key]||e.key;if(t!=="Unidentified")return t}return e.type==="keypress"?(e=qo(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?xm[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:su,charCode:function(e){return e.type==="keypress"?qo(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?qo(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),km=je(Cm),Pm=J({},ji,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),ya=je(Pm),Tm=J({},go,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:su}),Rm=je(Tm),_m=J({},vr,{propertyName:0,elapsedTime:0,pseudoElement:0}),Nm=je(_m),Lm=J({},ji,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),Om=je(Lm),Mm=[9,13,27,32],uu=Rt&&"CompositionEvent"in window,$r=null;Rt&&"documentMode"in document&&($r=document.documentMode);var Im=Rt&&"TextEvent"in window&&!$r,Md=Rt&&(!uu||$r&&8<$r&&11>=$r),ga=" ",wa=!1;function Id(e,t){switch(e){case"keyup":return Mm.indexOf(t.keyCode)!==-1;case"keydown":return t.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function Ad(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var Fn=!1;function Am(e,t){switch(e){case"compositionend":return Ad(t);case"keypress":return t.which!==32?null:(wa=!0,ga);case"textInput":return e=t.data,e===ga&&wa?null:e;default:return null}}function Dm(e,t){if(Fn)return e==="compositionend"||!uu&&Id(e,t)?(e=Od(),Xo=iu=Gt=null,Fn=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return Md&&t.locale!=="ko"?null:t.data;default:return null}}var Fm={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function xa(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t==="input"?!!Fm[e.type]:t==="textarea"}function Dd(e,t,n,r){fd(r),t=vi(t,"onChange"),0<t.length&&(n=new lu("onChange","change",null,n,r),e.push({event:n,listeners:t}))}var Ur=null,Jr=null;function zm(e){Qd(e,0)}function $i(e){var t=$n(e);if(id(t))return e}function jm(e,t){if(e==="change")return t}var Fd=!1;if(Rt){var vl;if(Rt){var yl="oninput"in document;if(!yl){var Sa=document.createElement("div");Sa.setAttribute("oninput","return;"),yl=typeof Sa.oninput=="function"}vl=yl}else vl=!1;Fd=vl&&(!document.documentMode||9<document.documentMode)}function Ea(){Ur&&(Ur.detachEvent("onpropertychange",zd),Jr=Ur=null)}function zd(e){if(e.propertyName==="value"&&$i(Jr)){var t=[];Dd(t,Jr,e,eu(e)),vd(zm,t)}}function $m(e,t,n){e==="focusin"?(Ea(),Ur=t,Jr=n,Ur.attachEvent("onpropertychange",zd)):e==="focusout"&&Ea()}function Um(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return $i(Jr)}function bm(e,t){if(e==="click")return $i(t)}function Vm(e,t){if(e==="input"||e==="change")return $i(t)}function Bm(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var nt=typeof Object.is=="function"?Object.is:Bm;function eo(e,t){if(nt(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(r=0;r<n.length;r++){var o=n[r];if(!bl.call(t,o)||!nt(e[o],t[o]))return!1}return!0}function Ca(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function ka(e,t){var n=Ca(e);e=0;for(var r;n;){if(n.nodeType===3){if(r=e+n.textContent.length,e<=t&&r>=t)return{node:n,offset:t-e};e=r}e:{for(;n;){if(n.nextSibling){n=n.nextSibling;break e}n=n.parentNode}n=void 0}n=Ca(n)}}function jd(e,t){return e&&t?e===t?!0:e&&e.nodeType===3?!1:t&&t.nodeType===3?jd(e,t.parentNode):"contains"in e?e.contains(t):e.compareDocumentPosition?!!(e.compareDocumentPosition(t)&16):!1:!1}function $d(){for(var e=window,t=ai();t instanceof e.HTMLIFrameElement;){try{var n=typeof t.contentWindow.location.href=="string"}catch{n=!1}if(n)e=t.contentWindow;else break;t=ai(e.document)}return t}function au(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&(t==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||t==="textarea"||e.contentEditable==="true")}function Wm(e){var t=$d(),n=e.focusedElem,r=e.selectionRange;if(t!==n&&n&&n.ownerDocument&&jd(n.ownerDocument.documentElement,n)){if(r!==null&&au(n)){if(t=r.start,e=r.end,e===void 0&&(e=t),"selectionStart"in n)n.selectionStart=t,n.selectionEnd=Math.min(e,n.value.length);else if(e=(t=n.ownerDocument||document)&&t.defaultView||window,e.getSelection){e=e.getSelection();var o=n.textContent.length,i=Math.min(r.start,o);r=r.end===void 0?i:Math.min(r.end,o),!e.extend&&i>r&&(o=r,r=i,i=o),o=ka(n,i);var l=ka(n,r);o&&l&&(e.rangeCount!==1||e.anchorNode!==o.node||e.anchorOffset!==o.offset||e.focusNode!==l.node||e.focusOffset!==l.offset)&&(t=t.createRange(),t.setStart(o.node,o.offset),e.removeAllRanges(),i>r?(e.addRange(t),e.extend(l.node,l.offset)):(t.setEnd(l.node,l.offset),e.addRange(t)))}}for(t=[],e=n;e=e.parentNode;)e.nodeType===1&&t.push({element:e,left:e.scrollLeft,top:e.scrollTop});for(typeof n.focus=="function"&&n.focus(),n=0;n<t.length;n++)e=t[n],e.element.scrollLeft=e.left,e.element.scrollTop=e.top}}var Hm=Rt&&"documentMode"in document&&11>=document.documentMode,zn=null,ls=null,br=null,ss=!1;function Pa(e,t,n){var r=n.window===n?n.document:n.nodeType===9?n:n.ownerDocument;ss||zn==null||zn!==ai(r)||(r=zn,"selectionStart"in r&&au(r)?r={start:r.selectionStart,end:r.selectionEnd}:(r=(r.ownerDocument&&r.ownerDocument.defaultView||window).getSelection(),r={anchorNode:r.anchorNode,anchorOffset:r.anchorOffset,focusNode:r.focusNode,focusOffset:r.focusOffset}),br&&eo(br,r)||(br=r,r=vi(ls,"onSelect"),0<r.length&&(t=new lu("onSelect","select",null,t,n),e.push({event:t,listeners:r}),t.target=zn)))}function Ao(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var jn={animationend:Ao("Animation","AnimationEnd"),animationiteration:Ao("Animation","AnimationIteration"),animationstart:Ao("Animation","AnimationStart"),transitionend:Ao("Transition","TransitionEnd")},gl={},Ud={};Rt&&(Ud=document.createElement("div").style,"AnimationEvent"in window||(delete jn.animationend.animation,delete jn.animationiteration.animation,delete jn.animationstart.animation),"TransitionEvent"in window||delete jn.transitionend.transition);function Ui(e){if(gl[e])return gl[e];if(!jn[e])return e;var t=jn[e],n;for(n in t)if(t.hasOwnProperty(n)&&n in Ud)return gl[e]=t[n];return e}var bd=Ui("animationend"),Vd=Ui("animationiteration"),Bd=Ui("animationstart"),Wd=Ui("transitionend"),Hd=new Map,Ta="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function sn(e,t){Hd.set(e,t),Ln(t,[e])}for(var wl=0;wl<Ta.length;wl++){var xl=Ta[wl],Qm=xl.toLowerCase(),Km=xl[0].toUpperCase()+xl.slice(1);sn(Qm,"on"+Km)}sn(bd,"onAnimationEnd");sn(Vd,"onAnimationIteration");sn(Bd,"onAnimationStart");sn("dblclick","onDoubleClick");sn("focusin","onFocus");sn("focusout","onBlur");sn(Wd,"onTransitionEnd");ur("onMouseEnter",["mouseout","mouseover"]);ur("onMouseLeave",["mouseout","mouseover"]);ur("onPointerEnter",["pointerout","pointerover"]);ur("onPointerLeave",["pointerout","pointerover"]);Ln("onChange","change click focusin focusout input keydown keyup selectionchange".split(" "));Ln("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" "));Ln("onBeforeInput",["compositionend","keypress","textInput","paste"]);Ln("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" "));Ln("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" "));Ln("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var Fr="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),Gm=new Set("cancel close invalid load scroll toggle".split(" ").concat(Fr));function Ra(e,t,n){var r=e.type||"unknown-event";e.currentTarget=n,Qh(r,t,void 0,e),e.currentTarget=null}function Qd(e,t){t=(t&4)!==0;for(var n=0;n<e.length;n++){var r=e[n],o=r.event;r=r.listeners;e:{var i=void 0;if(t)for(var l=r.length-1;0<=l;l--){var s=r[l],u=s.instance,a=s.currentTarget;if(s=s.listener,u!==i&&o.isPropagationStopped())break e;Ra(o,s,a),i=u}else for(l=0;l<r.length;l++){if(s=r[l],u=s.instance,a=s.currentTarget,s=s.listener,u!==i&&o.isPropagationStopped())break e;Ra(o,s,a),i=u}}}if(di)throw e=ns,di=!1,ns=null,e}function K(e,t){var n=t[fs];n===void 0&&(n=t[fs]=new Set);var r=e+"__bubble";n.has(r)||(Kd(t,e,2,!1),n.add(r))}function Sl(e,t,n){var r=0;t&&(r|=4),Kd(n,e,r,t)}var Do="_reactListening"+Math.random().toString(36).slice(2);function to(e){if(!e[Do]){e[Do]=!0,ed.forEach(function(n){n!=="selectionchange"&&(Gm.has(n)||Sl(n,!1,e),Sl(n,!0,e))});var t=e.nodeType===9?e:e.ownerDocument;t===null||t[Do]||(t[Do]=!0,Sl("selectionchange",!1,t))}}function Kd(e,t,n,r){switch(Ld(t)){case 1:var o=um;break;case 4:o=am;break;default:o=ou}n=o.bind(null,t,n,e),o=void 0,!ts||t!=="touchstart"&&t!=="touchmove"&&t!=="wheel"||(o=!0),r?o!==void 0?e.addEventListener(t,n,{capture:!0,passive:o}):e.addEventListener(t,n,!0):o!==void 0?e.addEventListener(t,n,{passive:o}):e.addEventListener(t,n,!1)}function El(e,t,n,r,o){var i=r;if(!(t&1)&&!(t&2)&&r!==null)e:for(;;){if(r===null)return;var l=r.tag;if(l===3||l===4){var s=r.stateNode.containerInfo;if(s===o||s.nodeType===8&&s.parentNode===o)break;if(l===4)for(l=r.return;l!==null;){var u=l.tag;if((u===3||u===4)&&(u=l.stateNode.containerInfo,u===o||u.nodeType===8&&u.parentNode===o))return;l=l.return}for(;s!==null;){if(l=hn(s),l===null)return;if(u=l.tag,u===5||u===6){r=i=l;continue e}s=s.parentNode}}r=r.return}vd(function(){var a=i,f=eu(n),p=[];e:{var v=Hd.get(e);if(v!==void 0){var g=lu,w=e;switch(e){case"keypress":if(qo(n)===0)break e;case"keydown":case"keyup":g=km;break;case"focusin":w="focus",g=ml;break;case"focusout":w="blur",g=ml;break;case"beforeblur":case"afterblur":g=ml;break;case"click":if(n.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":g=ma;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":g=fm;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":g=Rm;break;case bd:case Vd:case Bd:g=mm;break;case Wd:g=Nm;break;case"scroll":g=cm;break;case"wheel":g=Om;break;case"copy":case"cut":case"paste":g=ym;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":g=ya}var y=(t&4)!==0,x=!y&&e==="scroll",d=y?v!==null?v+"Capture":null:v;y=[];for(var c=a,h;c!==null;){h=c;var S=h.stateNode;if(h.tag===5&&S!==null&&(h=S,d!==null&&(S=Yr(c,d),S!=null&&y.push(no(c,S,h)))),x)break;c=c.return}0<y.length&&(v=new g(v,w,null,n,f),p.push({event:v,listeners:y}))}}if(!(t&7)){e:{if(v=e==="mouseover"||e==="pointerover",g=e==="mouseout"||e==="pointerout",v&&n!==Jl&&(w=n.relatedTarget||n.fromElement)&&(hn(w)||w[_t]))break e;if((g||v)&&(v=f.window===f?f:(v=f.ownerDocument)?v.defaultView||v.parentWindow:window,g?(w=n.relatedTarget||n.toElement,g=a,w=w?hn(w):null,w!==null&&(x=On(w),w!==x||w.tag!==5&&w.tag!==6)&&(w=null)):(g=null,w=a),g!==w)){if(y=ma,S="onMouseLeave",d="onMouseEnter",c="mouse",(e==="pointerout"||e==="pointerover")&&(y=ya,S="onPointerLeave",d="onPointerEnter",c="pointer"),x=g==null?v:$n(g),h=w==null?v:$n(w),v=new y(S,c+"leave",g,n,f),v.target=x,v.relatedTarget=h,S=null,hn(f)===a&&(y=new y(d,c+"enter",w,n,f),y.target=h,y.relatedTarget=x,S=y),x=S,g&&w)t:{for(y=g,d=w,c=0,h=y;h;h=In(h))c++;for(h=0,S=d;S;S=In(S))h++;for(;0<c-h;)y=In(y),c--;for(;0<h-c;)d=In(d),h--;for(;c--;){if(y===d||d!==null&&y===d.alternate)break t;y=In(y),d=In(d)}y=null}else y=null;g!==null&&_a(p,v,g,y,!1),w!==null&&x!==null&&_a(p,x,w,y,!0)}}e:{if(v=a?$n(a):window,g=v.nodeName&&v.nodeName.toLowerCase(),g==="select"||g==="input"&&v.type==="file")var C=jm;else if(xa(v))if(Fd)C=Vm;else{C=Um;var T=$m}else(g=v.nodeName)&&g.toLowerCase()==="input"&&(v.type==="checkbox"||v.type==="radio")&&(C=bm);if(C&&(C=C(e,a))){Dd(p,C,n,f);break e}T&&T(e,v,a),e==="focusout"&&(T=v._wrapperState)&&T.controlled&&v.type==="number"&&Gl(v,"number",v.value)}switch(T=a?$n(a):window,e){case"focusin":(xa(T)||T.contentEditable==="true")&&(zn=T,ls=a,br=null);break;case"focusout":br=ls=zn=null;break;case"mousedown":ss=!0;break;case"contextmenu":case"mouseup":case"dragend":ss=!1,Pa(p,n,f);break;case"selectionchange":if(Hm)break;case"keydown":case"keyup":Pa(p,n,f)}var k;if(uu)e:{switch(e){case"compositionstart":var _="onCompositionStart";break e;case"compositionend":_="onCompositionEnd";break e;case"compositionupdate":_="onCompositionUpdate";break e}_=void 0}else Fn?Id(e,n)&&(_="onCompositionEnd"):e==="keydown"&&n.keyCode===229&&(_="onCompositionStart");_&&(Md&&n.locale!=="ko"&&(Fn||_!=="onCompositionStart"?_==="onCompositionEnd"&&Fn&&(k=Od()):(Gt=f,iu="value"in Gt?Gt.value:Gt.textContent,Fn=!0)),T=vi(a,_),0<T.length&&(_=new va(_,e,null,n,f),p.push({event:_,listeners:T}),k?_.data=k:(k=Ad(n),k!==null&&(_.data=k)))),(k=Im?Am(e,n):Dm(e,n))&&(a=vi(a,"onBeforeInput"),0<a.length&&(f=new va("onBeforeInput","beforeinput",null,n,f),p.push({event:f,listeners:a}),f.data=k))}Qd(p,t)})}function no(e,t,n){return{instance:e,listener:t,currentTarget:n}}function vi(e,t){for(var n=t+"Capture",r=[];e!==null;){var o=e,i=o.stateNode;o.tag===5&&i!==null&&(o=i,i=Yr(e,n),i!=null&&r.unshift(no(e,i,o)),i=Yr(e,t),i!=null&&r.push(no(e,i,o))),e=e.return}return r}function In(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5);return e||null}function _a(e,t,n,r,o){for(var i=t._reactName,l=[];n!==null&&n!==r;){var s=n,u=s.alternate,a=s.stateNode;if(u!==null&&u===r)break;s.tag===5&&a!==null&&(s=a,o?(u=Yr(n,i),u!=null&&l.unshift(no(n,u,s))):o||(u=Yr(n,i),u!=null&&l.push(no(n,u,s)))),n=n.return}l.length!==0&&e.push({event:t,listeners:l})}var Ym=/\r\n?/g,Xm=/\u0000|\uFFFD/g;function Na(e){return(typeof e=="string"?e:""+e).replace(Ym,`
`).replace(Xm,"")}function Fo(e,t,n){if(t=Na(t),Na(e)!==t&&n)throw Error(P(425))}function yi(){}var us=null,as=null;function cs(e,t){return e==="textarea"||e==="noscript"||typeof t.children=="string"||typeof t.children=="number"||typeof t.dangerouslySetInnerHTML=="object"&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}var ds=typeof setTimeout=="function"?setTimeout:void 0,qm=typeof clearTimeout=="function"?clearTimeout:void 0,La=typeof Promise=="function"?Promise:void 0,Zm=typeof queueMicrotask=="function"?queueMicrotask:typeof La<"u"?function(e){return La.resolve(null).then(e).catch(Jm)}:ds;function Jm(e){setTimeout(function(){throw e})}function Cl(e,t){var n=t,r=0;do{var o=n.nextSibling;if(e.removeChild(n),o&&o.nodeType===8)if(n=o.data,n==="/$"){if(r===0){e.removeChild(o),Zr(t);return}r--}else n!=="$"&&n!=="$?"&&n!=="$!"||r++;n=o}while(n);Zr(t)}function Jt(e){for(;e!=null;e=e.nextSibling){var t=e.nodeType;if(t===1||t===3)break;if(t===8){if(t=e.data,t==="$"||t==="$!"||t==="$?")break;if(t==="/$")return null}}return e}function Oa(e){e=e.previousSibling;for(var t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="$"||n==="$!"||n==="$?"){if(t===0)return e;t--}else n==="/$"&&t++}e=e.previousSibling}return null}var yr=Math.random().toString(36).slice(2),at="__reactFiber$"+yr,ro="__reactProps$"+yr,_t="__reactContainer$"+yr,fs="__reactEvents$"+yr,ev="__reactListeners$"+yr,tv="__reactHandles$"+yr;function hn(e){var t=e[at];if(t)return t;for(var n=e.parentNode;n;){if(t=n[_t]||n[at]){if(n=t.alternate,t.child!==null||n!==null&&n.child!==null)for(e=Oa(e);e!==null;){if(n=e[at])return n;e=Oa(e)}return t}e=n,n=e.parentNode}return null}function wo(e){return e=e[at]||e[_t],!e||e.tag!==5&&e.tag!==6&&e.tag!==13&&e.tag!==3?null:e}function $n(e){if(e.tag===5||e.tag===6)return e.stateNode;throw Error(P(33))}function bi(e){return e[ro]||null}var ps=[],Un=-1;function un(e){return{current:e}}function G(e){0>Un||(e.current=ps[Un],ps[Un]=null,Un--)}function W(e,t){Un++,ps[Un]=e.current,e.current=t}var ln={},we=un(ln),_e=un(!1),kn=ln;function ar(e,t){var n=e.type.contextTypes;if(!n)return ln;var r=e.stateNode;if(r&&r.__reactInternalMemoizedUnmaskedChildContext===t)return r.__reactInternalMemoizedMaskedChildContext;var o={},i;for(i in n)o[i]=t[i];return r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=t,e.__reactInternalMemoizedMaskedChildContext=o),o}function Ne(e){return e=e.childContextTypes,e!=null}function gi(){G(_e),G(we)}function Ma(e,t,n){if(we.current!==ln)throw Error(P(168));W(we,t),W(_e,n)}function Gd(e,t,n){var r=e.stateNode;if(t=t.childContextTypes,typeof r.getChildContext!="function")return n;r=r.getChildContext();for(var o in r)if(!(o in t))throw Error(P(108,$h(e)||"Unknown",o));return J({},n,r)}function wi(e){return e=(e=e.stateNode)&&e.__reactInternalMemoizedMergedChildContext||ln,kn=we.current,W(we,e),W(_e,_e.current),!0}function Ia(e,t,n){var r=e.stateNode;if(!r)throw Error(P(169));n?(e=Gd(e,t,kn),r.__reactInternalMemoizedMergedChildContext=e,G(_e),G(we),W(we,e)):G(_e),W(_e,n)}var St=null,Vi=!1,kl=!1;function Yd(e){St===null?St=[e]:St.push(e)}function nv(e){Vi=!0,Yd(e)}function an(){if(!kl&&St!==null){kl=!0;var e=0,t=B;try{var n=St;for(B=1;e<n.length;e++){var r=n[e];do r=r(!0);while(r!==null)}St=null,Vi=!1}catch(o){throw St!==null&&(St=St.slice(e+1)),xd(tu,an),o}finally{B=t,kl=!1}}return null}var bn=[],Vn=0,xi=null,Si=0,be=[],Ve=0,Pn=null,Ct=1,kt="";function fn(e,t){bn[Vn++]=Si,bn[Vn++]=xi,xi=e,Si=t}function Xd(e,t,n){be[Ve++]=Ct,be[Ve++]=kt,be[Ve++]=Pn,Pn=e;var r=Ct;e=kt;var o=32-et(r)-1;r&=~(1<<o),n+=1;var i=32-et(t)+o;if(30<i){var l=o-o%5;i=(r&(1<<l)-1).toString(32),r>>=l,o-=l,Ct=1<<32-et(t)+o|n<<o|r,kt=i+e}else Ct=1<<i|n<<o|r,kt=e}function cu(e){e.return!==null&&(fn(e,1),Xd(e,1,0))}function du(e){for(;e===xi;)xi=bn[--Vn],bn[Vn]=null,Si=bn[--Vn],bn[Vn]=null;for(;e===Pn;)Pn=be[--Ve],be[Ve]=null,kt=be[--Ve],be[Ve]=null,Ct=be[--Ve],be[Ve]=null}var De=null,Ae=null,Y=!1,Je=null;function qd(e,t){var n=Be(5,null,null,0);n.elementType="DELETED",n.stateNode=t,n.return=e,t=e.deletions,t===null?(e.deletions=[n],e.flags|=16):t.push(n)}function Aa(e,t){switch(e.tag){case 5:var n=e.type;return t=t.nodeType!==1||n.toLowerCase()!==t.nodeName.toLowerCase()?null:t,t!==null?(e.stateNode=t,De=e,Ae=Jt(t.firstChild),!0):!1;case 6:return t=e.pendingProps===""||t.nodeType!==3?null:t,t!==null?(e.stateNode=t,De=e,Ae=null,!0):!1;case 13:return t=t.nodeType!==8?null:t,t!==null?(n=Pn!==null?{id:Ct,overflow:kt}:null,e.memoizedState={dehydrated:t,treeContext:n,retryLane:1073741824},n=Be(18,null,null,0),n.stateNode=t,n.return=e,e.child=n,De=e,Ae=null,!0):!1;default:return!1}}function hs(e){return(e.mode&1)!==0&&(e.flags&128)===0}function ms(e){if(Y){var t=Ae;if(t){var n=t;if(!Aa(e,t)){if(hs(e))throw Error(P(418));t=Jt(n.nextSibling);var r=De;t&&Aa(e,t)?qd(r,n):(e.flags=e.flags&-4097|2,Y=!1,De=e)}}else{if(hs(e))throw Error(P(418));e.flags=e.flags&-4097|2,Y=!1,De=e}}}function Da(e){for(e=e.return;e!==null&&e.tag!==5&&e.tag!==3&&e.tag!==13;)e=e.return;De=e}function zo(e){if(e!==De)return!1;if(!Y)return Da(e),Y=!0,!1;var t;if((t=e.tag!==3)&&!(t=e.tag!==5)&&(t=e.type,t=t!=="head"&&t!=="body"&&!cs(e.type,e.memoizedProps)),t&&(t=Ae)){if(hs(e))throw Zd(),Error(P(418));for(;t;)qd(e,t),t=Jt(t.nextSibling)}if(Da(e),e.tag===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(P(317));e:{for(e=e.nextSibling,t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="/$"){if(t===0){Ae=Jt(e.nextSibling);break e}t--}else n!=="$"&&n!=="$!"&&n!=="$?"||t++}e=e.nextSibling}Ae=null}}else Ae=De?Jt(e.stateNode.nextSibling):null;return!0}function Zd(){for(var e=Ae;e;)e=Jt(e.nextSibling)}function cr(){Ae=De=null,Y=!1}function fu(e){Je===null?Je=[e]:Je.push(e)}var rv=It.ReactCurrentBatchConfig;function Rr(e,t,n){if(e=n.ref,e!==null&&typeof e!="function"&&typeof e!="object"){if(n._owner){if(n=n._owner,n){if(n.tag!==1)throw Error(P(309));var r=n.stateNode}if(!r)throw Error(P(147,e));var o=r,i=""+e;return t!==null&&t.ref!==null&&typeof t.ref=="function"&&t.ref._stringRef===i?t.ref:(t=function(l){var s=o.refs;l===null?delete s[i]:s[i]=l},t._stringRef=i,t)}if(typeof e!="string")throw Error(P(284));if(!n._owner)throw Error(P(290,e))}return e}function jo(e,t){throw e=Object.prototype.toString.call(t),Error(P(31,e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function Fa(e){var t=e._init;return t(e._payload)}function Jd(e){function t(d,c){if(e){var h=d.deletions;h===null?(d.deletions=[c],d.flags|=16):h.push(c)}}function n(d,c){if(!e)return null;for(;c!==null;)t(d,c),c=c.sibling;return null}function r(d,c){for(d=new Map;c!==null;)c.key!==null?d.set(c.key,c):d.set(c.index,c),c=c.sibling;return d}function o(d,c){return d=rn(d,c),d.index=0,d.sibling=null,d}function i(d,c,h){return d.index=h,e?(h=d.alternate,h!==null?(h=h.index,h<c?(d.flags|=2,c):h):(d.flags|=2,c)):(d.flags|=1048576,c)}function l(d){return e&&d.alternate===null&&(d.flags|=2),d}function s(d,c,h,S){return c===null||c.tag!==6?(c=Ol(h,d.mode,S),c.return=d,c):(c=o(c,h),c.return=d,c)}function u(d,c,h,S){var C=h.type;return C===Dn?f(d,c,h.props.children,S,h.key):c!==null&&(c.elementType===C||typeof C=="object"&&C!==null&&C.$$typeof===$t&&Fa(C)===c.type)?(S=o(c,h.props),S.ref=Rr(d,c,h),S.return=d,S):(S=oi(h.type,h.key,h.props,null,d.mode,S),S.ref=Rr(d,c,h),S.return=d,S)}function a(d,c,h,S){return c===null||c.tag!==4||c.stateNode.containerInfo!==h.containerInfo||c.stateNode.implementation!==h.implementation?(c=Ml(h,d.mode,S),c.return=d,c):(c=o(c,h.children||[]),c.return=d,c)}function f(d,c,h,S,C){return c===null||c.tag!==7?(c=Cn(h,d.mode,S,C),c.return=d,c):(c=o(c,h),c.return=d,c)}function p(d,c,h){if(typeof c=="string"&&c!==""||typeof c=="number")return c=Ol(""+c,d.mode,h),c.return=d,c;if(typeof c=="object"&&c!==null){switch(c.$$typeof){case Ro:return h=oi(c.type,c.key,c.props,null,d.mode,h),h.ref=Rr(d,null,c),h.return=d,h;case An:return c=Ml(c,d.mode,h),c.return=d,c;case $t:var S=c._init;return p(d,S(c._payload),h)}if(Ar(c)||Er(c))return c=Cn(c,d.mode,h,null),c.return=d,c;jo(d,c)}return null}function v(d,c,h,S){var C=c!==null?c.key:null;if(typeof h=="string"&&h!==""||typeof h=="number")return C!==null?null:s(d,c,""+h,S);if(typeof h=="object"&&h!==null){switch(h.$$typeof){case Ro:return h.key===C?u(d,c,h,S):null;case An:return h.key===C?a(d,c,h,S):null;case $t:return C=h._init,v(d,c,C(h._payload),S)}if(Ar(h)||Er(h))return C!==null?null:f(d,c,h,S,null);jo(d,h)}return null}function g(d,c,h,S,C){if(typeof S=="string"&&S!==""||typeof S=="number")return d=d.get(h)||null,s(c,d,""+S,C);if(typeof S=="object"&&S!==null){switch(S.$$typeof){case Ro:return d=d.get(S.key===null?h:S.key)||null,u(c,d,S,C);case An:return d=d.get(S.key===null?h:S.key)||null,a(c,d,S,C);case $t:var T=S._init;return g(d,c,h,T(S._payload),C)}if(Ar(S)||Er(S))return d=d.get(h)||null,f(c,d,S,C,null);jo(c,S)}return null}function w(d,c,h,S){for(var C=null,T=null,k=c,_=c=0,L=null;k!==null&&_<h.length;_++){k.index>_?(L=k,k=null):L=k.sibling;var M=v(d,k,h[_],S);if(M===null){k===null&&(k=L);break}e&&k&&M.alternate===null&&t(d,k),c=i(M,c,_),T===null?C=M:T.sibling=M,T=M,k=L}if(_===h.length)return n(d,k),Y&&fn(d,_),C;if(k===null){for(;_<h.length;_++)k=p(d,h[_],S),k!==null&&(c=i(k,c,_),T===null?C=k:T.sibling=k,T=k);return Y&&fn(d,_),C}for(k=r(d,k);_<h.length;_++)L=g(k,d,_,h[_],S),L!==null&&(e&&L.alternate!==null&&k.delete(L.key===null?_:L.key),c=i(L,c,_),T===null?C=L:T.sibling=L,T=L);return e&&k.forEach(function(H){return t(d,H)}),Y&&fn(d,_),C}function y(d,c,h,S){var C=Er(h);if(typeof C!="function")throw Error(P(150));if(h=C.call(h),h==null)throw Error(P(151));for(var T=C=null,k=c,_=c=0,L=null,M=h.next();k!==null&&!M.done;_++,M=h.next()){k.index>_?(L=k,k=null):L=k.sibling;var H=v(d,k,M.value,S);if(H===null){k===null&&(k=L);break}e&&k&&H.alternate===null&&t(d,k),c=i(H,c,_),T===null?C=H:T.sibling=H,T=H,k=L}if(M.done)return n(d,k),Y&&fn(d,_),C;if(k===null){for(;!M.done;_++,M=h.next())M=p(d,M.value,S),M!==null&&(c=i(M,c,_),T===null?C=M:T.sibling=M,T=M);return Y&&fn(d,_),C}for(k=r(d,k);!M.done;_++,M=h.next())M=g(k,d,_,M.value,S),M!==null&&(e&&M.alternate!==null&&k.delete(M.key===null?_:M.key),c=i(M,c,_),T===null?C=M:T.sibling=M,T=M);return e&&k.forEach(function(j){return t(d,j)}),Y&&fn(d,_),C}function x(d,c,h,S){if(typeof h=="object"&&h!==null&&h.type===Dn&&h.key===null&&(h=h.props.children),typeof h=="object"&&h!==null){switch(h.$$typeof){case Ro:e:{for(var C=h.key,T=c;T!==null;){if(T.key===C){if(C=h.type,C===Dn){if(T.tag===7){n(d,T.sibling),c=o(T,h.props.children),c.return=d,d=c;break e}}else if(T.elementType===C||typeof C=="object"&&C!==null&&C.$$typeof===$t&&Fa(C)===T.type){n(d,T.sibling),c=o(T,h.props),c.ref=Rr(d,T,h),c.return=d,d=c;break e}n(d,T);break}else t(d,T);T=T.sibling}h.type===Dn?(c=Cn(h.props.children,d.mode,S,h.key),c.return=d,d=c):(S=oi(h.type,h.key,h.props,null,d.mode,S),S.ref=Rr(d,c,h),S.return=d,d=S)}return l(d);case An:e:{for(T=h.key;c!==null;){if(c.key===T)if(c.tag===4&&c.stateNode.containerInfo===h.containerInfo&&c.stateNode.implementation===h.implementation){n(d,c.sibling),c=o(c,h.children||[]),c.return=d,d=c;break e}else{n(d,c);break}else t(d,c);c=c.sibling}c=Ml(h,d.mode,S),c.return=d,d=c}return l(d);case $t:return T=h._init,x(d,c,T(h._payload),S)}if(Ar(h))return w(d,c,h,S);if(Er(h))return y(d,c,h,S);jo(d,h)}return typeof h=="string"&&h!==""||typeof h=="number"?(h=""+h,c!==null&&c.tag===6?(n(d,c.sibling),c=o(c,h),c.return=d,d=c):(n(d,c),c=Ol(h,d.mode,S),c.return=d,d=c),l(d)):n(d,c)}return x}var dr=Jd(!0),ef=Jd(!1),Ei=un(null),Ci=null,Bn=null,pu=null;function hu(){pu=Bn=Ci=null}function mu(e){var t=Ei.current;G(Ei),e._currentValue=t}function vs(e,t,n){for(;e!==null;){var r=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,r!==null&&(r.childLanes|=t)):r!==null&&(r.childLanes&t)!==t&&(r.childLanes|=t),e===n)break;e=e.return}}function Xn(e,t){Ci=e,pu=Bn=null,e=e.dependencies,e!==null&&e.firstContext!==null&&(e.lanes&t&&(Re=!0),e.firstContext=null)}function He(e){var t=e._currentValue;if(pu!==e)if(e={context:e,memoizedValue:t,next:null},Bn===null){if(Ci===null)throw Error(P(308));Bn=e,Ci.dependencies={lanes:0,firstContext:e}}else Bn=Bn.next=e;return t}var mn=null;function vu(e){mn===null?mn=[e]:mn.push(e)}function tf(e,t,n,r){var o=t.interleaved;return o===null?(n.next=n,vu(t)):(n.next=o.next,o.next=n),t.interleaved=n,Nt(e,r)}function Nt(e,t){e.lanes|=t;var n=e.alternate;for(n!==null&&(n.lanes|=t),n=e,e=e.return;e!==null;)e.childLanes|=t,n=e.alternate,n!==null&&(n.childLanes|=t),n=e,e=e.return;return n.tag===3?n.stateNode:null}var Ut=!1;function yu(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function nf(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,effects:e.effects})}function Pt(e,t){return{eventTime:e,lane:t,tag:0,payload:null,callback:null,next:null}}function en(e,t,n){var r=e.updateQueue;if(r===null)return null;if(r=r.shared,b&2){var o=r.pending;return o===null?t.next=t:(t.next=o.next,o.next=t),r.pending=t,Nt(e,n)}return o=r.interleaved,o===null?(t.next=t,vu(r)):(t.next=o.next,o.next=t),r.interleaved=t,Nt(e,n)}function Zo(e,t,n){if(t=t.updateQueue,t!==null&&(t=t.shared,(n&4194240)!==0)){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,nu(e,n)}}function za(e,t){var n=e.updateQueue,r=e.alternate;if(r!==null&&(r=r.updateQueue,n===r)){var o=null,i=null;if(n=n.firstBaseUpdate,n!==null){do{var l={eventTime:n.eventTime,lane:n.lane,tag:n.tag,payload:n.payload,callback:n.callback,next:null};i===null?o=i=l:i=i.next=l,n=n.next}while(n!==null);i===null?o=i=t:i=i.next=t}else o=i=t;n={baseState:r.baseState,firstBaseUpdate:o,lastBaseUpdate:i,shared:r.shared,effects:r.effects},e.updateQueue=n;return}e=n.lastBaseUpdate,e===null?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}function ki(e,t,n,r){var o=e.updateQueue;Ut=!1;var i=o.firstBaseUpdate,l=o.lastBaseUpdate,s=o.shared.pending;if(s!==null){o.shared.pending=null;var u=s,a=u.next;u.next=null,l===null?i=a:l.next=a,l=u;var f=e.alternate;f!==null&&(f=f.updateQueue,s=f.lastBaseUpdate,s!==l&&(s===null?f.firstBaseUpdate=a:s.next=a,f.lastBaseUpdate=u))}if(i!==null){var p=o.baseState;l=0,f=a=u=null,s=i;do{var v=s.lane,g=s.eventTime;if((r&v)===v){f!==null&&(f=f.next={eventTime:g,lane:0,tag:s.tag,payload:s.payload,callback:s.callback,next:null});e:{var w=e,y=s;switch(v=t,g=n,y.tag){case 1:if(w=y.payload,typeof w=="function"){p=w.call(g,p,v);break e}p=w;break e;case 3:w.flags=w.flags&-65537|128;case 0:if(w=y.payload,v=typeof w=="function"?w.call(g,p,v):w,v==null)break e;p=J({},p,v);break e;case 2:Ut=!0}}s.callback!==null&&s.lane!==0&&(e.flags|=64,v=o.effects,v===null?o.effects=[s]:v.push(s))}else g={eventTime:g,lane:v,tag:s.tag,payload:s.payload,callback:s.callback,next:null},f===null?(a=f=g,u=p):f=f.next=g,l|=v;if(s=s.next,s===null){if(s=o.shared.pending,s===null)break;v=s,s=v.next,v.next=null,o.lastBaseUpdate=v,o.shared.pending=null}}while(!0);if(f===null&&(u=p),o.baseState=u,o.firstBaseUpdate=a,o.lastBaseUpdate=f,t=o.shared.interleaved,t!==null){o=t;do l|=o.lane,o=o.next;while(o!==t)}else i===null&&(o.shared.lanes=0);Rn|=l,e.lanes=l,e.memoizedState=p}}function ja(e,t,n){if(e=t.effects,t.effects=null,e!==null)for(t=0;t<e.length;t++){var r=e[t],o=r.callback;if(o!==null){if(r.callback=null,r=n,typeof o!="function")throw Error(P(191,o));o.call(r)}}}var xo={},dt=un(xo),oo=un(xo),io=un(xo);function vn(e){if(e===xo)throw Error(P(174));return e}function gu(e,t){switch(W(io,t),W(oo,e),W(dt,xo),e=t.nodeType,e){case 9:case 11:t=(t=t.documentElement)?t.namespaceURI:Xl(null,"");break;default:e=e===8?t.parentNode:t,t=e.namespaceURI||null,e=e.tagName,t=Xl(t,e)}G(dt),W(dt,t)}function fr(){G(dt),G(oo),G(io)}function rf(e){vn(io.current);var t=vn(dt.current),n=Xl(t,e.type);t!==n&&(W(oo,e),W(dt,n))}function wu(e){oo.current===e&&(G(dt),G(oo))}var X=un(0);function Pi(e){for(var t=e;t!==null;){if(t.tag===13){var n=t.memoizedState;if(n!==null&&(n=n.dehydrated,n===null||n.data==="$?"||n.data==="$!"))return t}else if(t.tag===19&&t.memoizedProps.revealOrder!==void 0){if(t.flags&128)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}var Pl=[];function xu(){for(var e=0;e<Pl.length;e++)Pl[e]._workInProgressVersionPrimary=null;Pl.length=0}var Jo=It.ReactCurrentDispatcher,Tl=It.ReactCurrentBatchConfig,Tn=0,q=null,ie=null,se=null,Ti=!1,Vr=!1,lo=0,ov=0;function me(){throw Error(P(321))}function Su(e,t){if(t===null)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!nt(e[n],t[n]))return!1;return!0}function Eu(e,t,n,r,o,i){if(Tn=i,q=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,Jo.current=e===null||e.memoizedState===null?uv:av,e=n(r,o),Vr){i=0;do{if(Vr=!1,lo=0,25<=i)throw Error(P(301));i+=1,se=ie=null,t.updateQueue=null,Jo.current=cv,e=n(r,o)}while(Vr)}if(Jo.current=Ri,t=ie!==null&&ie.next!==null,Tn=0,se=ie=q=null,Ti=!1,t)throw Error(P(300));return e}function Cu(){var e=lo!==0;return lo=0,e}function it(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return se===null?q.memoizedState=se=e:se=se.next=e,se}function Qe(){if(ie===null){var e=q.alternate;e=e!==null?e.memoizedState:null}else e=ie.next;var t=se===null?q.memoizedState:se.next;if(t!==null)se=t,ie=e;else{if(e===null)throw Error(P(310));ie=e,e={memoizedState:ie.memoizedState,baseState:ie.baseState,baseQueue:ie.baseQueue,queue:ie.queue,next:null},se===null?q.memoizedState=se=e:se=se.next=e}return se}function so(e,t){return typeof t=="function"?t(e):t}function Rl(e){var t=Qe(),n=t.queue;if(n===null)throw Error(P(311));n.lastRenderedReducer=e;var r=ie,o=r.baseQueue,i=n.pending;if(i!==null){if(o!==null){var l=o.next;o.next=i.next,i.next=l}r.baseQueue=o=i,n.pending=null}if(o!==null){i=o.next,r=r.baseState;var s=l=null,u=null,a=i;do{var f=a.lane;if((Tn&f)===f)u!==null&&(u=u.next={lane:0,action:a.action,hasEagerState:a.hasEagerState,eagerState:a.eagerState,next:null}),r=a.hasEagerState?a.eagerState:e(r,a.action);else{var p={lane:f,action:a.action,hasEagerState:a.hasEagerState,eagerState:a.eagerState,next:null};u===null?(s=u=p,l=r):u=u.next=p,q.lanes|=f,Rn|=f}a=a.next}while(a!==null&&a!==i);u===null?l=r:u.next=s,nt(r,t.memoizedState)||(Re=!0),t.memoizedState=r,t.baseState=l,t.baseQueue=u,n.lastRenderedState=r}if(e=n.interleaved,e!==null){o=e;do i=o.lane,q.lanes|=i,Rn|=i,o=o.next;while(o!==e)}else o===null&&(n.lanes=0);return[t.memoizedState,n.dispatch]}function _l(e){var t=Qe(),n=t.queue;if(n===null)throw Error(P(311));n.lastRenderedReducer=e;var r=n.dispatch,o=n.pending,i=t.memoizedState;if(o!==null){n.pending=null;var l=o=o.next;do i=e(i,l.action),l=l.next;while(l!==o);nt(i,t.memoizedState)||(Re=!0),t.memoizedState=i,t.baseQueue===null&&(t.baseState=i),n.lastRenderedState=i}return[i,r]}function of(){}function lf(e,t){var n=q,r=Qe(),o=t(),i=!nt(r.memoizedState,o);if(i&&(r.memoizedState=o,Re=!0),r=r.queue,ku(af.bind(null,n,r,e),[e]),r.getSnapshot!==t||i||se!==null&&se.memoizedState.tag&1){if(n.flags|=2048,uo(9,uf.bind(null,n,r,o,t),void 0,null),ue===null)throw Error(P(349));Tn&30||sf(n,t,o)}return o}function sf(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},t=q.updateQueue,t===null?(t={lastEffect:null,stores:null},q.updateQueue=t,t.stores=[e]):(n=t.stores,n===null?t.stores=[e]:n.push(e))}function uf(e,t,n,r){t.value=n,t.getSnapshot=r,cf(t)&&df(e)}function af(e,t,n){return n(function(){cf(t)&&df(e)})}function cf(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!nt(e,n)}catch{return!0}}function df(e){var t=Nt(e,1);t!==null&&tt(t,e,1,-1)}function $a(e){var t=it();return typeof e=="function"&&(e=e()),t.memoizedState=t.baseState=e,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:so,lastRenderedState:e},t.queue=e,e=e.dispatch=sv.bind(null,q,e),[t.memoizedState,e]}function uo(e,t,n,r){return e={tag:e,create:t,destroy:n,deps:r,next:null},t=q.updateQueue,t===null?(t={lastEffect:null,stores:null},q.updateQueue=t,t.lastEffect=e.next=e):(n=t.lastEffect,n===null?t.lastEffect=e.next=e:(r=n.next,n.next=e,e.next=r,t.lastEffect=e)),e}function ff(){return Qe().memoizedState}function ei(e,t,n,r){var o=it();q.flags|=e,o.memoizedState=uo(1|t,n,void 0,r===void 0?null:r)}function Bi(e,t,n,r){var o=Qe();r=r===void 0?null:r;var i=void 0;if(ie!==null){var l=ie.memoizedState;if(i=l.destroy,r!==null&&Su(r,l.deps)){o.memoizedState=uo(t,n,i,r);return}}q.flags|=e,o.memoizedState=uo(1|t,n,i,r)}function Ua(e,t){return ei(8390656,8,e,t)}function ku(e,t){return Bi(2048,8,e,t)}function pf(e,t){return Bi(4,2,e,t)}function hf(e,t){return Bi(4,4,e,t)}function mf(e,t){if(typeof t=="function")return e=e(),t(e),function(){t(null)};if(t!=null)return e=e(),t.current=e,function(){t.current=null}}function vf(e,t,n){return n=n!=null?n.concat([e]):null,Bi(4,4,mf.bind(null,t,e),n)}function Pu(){}function yf(e,t){var n=Qe();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&Su(t,r[1])?r[0]:(n.memoizedState=[e,t],e)}function gf(e,t){var n=Qe();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&Su(t,r[1])?r[0]:(e=e(),n.memoizedState=[e,t],e)}function wf(e,t,n){return Tn&21?(nt(n,t)||(n=Cd(),q.lanes|=n,Rn|=n,e.baseState=!0),t):(e.baseState&&(e.baseState=!1,Re=!0),e.memoizedState=n)}function iv(e,t){var n=B;B=n!==0&&4>n?n:4,e(!0);var r=Tl.transition;Tl.transition={};try{e(!1),t()}finally{B=n,Tl.transition=r}}function xf(){return Qe().memoizedState}function lv(e,t,n){var r=nn(e);if(n={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null},Sf(e))Ef(t,n);else if(n=tf(e,t,n,r),n!==null){var o=Ce();tt(n,e,r,o),Cf(n,t,r)}}function sv(e,t,n){var r=nn(e),o={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null};if(Sf(e))Ef(t,o);else{var i=e.alternate;if(e.lanes===0&&(i===null||i.lanes===0)&&(i=t.lastRenderedReducer,i!==null))try{var l=t.lastRenderedState,s=i(l,n);if(o.hasEagerState=!0,o.eagerState=s,nt(s,l)){var u=t.interleaved;u===null?(o.next=o,vu(t)):(o.next=u.next,u.next=o),t.interleaved=o;return}}catch{}finally{}n=tf(e,t,o,r),n!==null&&(o=Ce(),tt(n,e,r,o),Cf(n,t,r))}}function Sf(e){var t=e.alternate;return e===q||t!==null&&t===q}function Ef(e,t){Vr=Ti=!0;var n=e.pending;n===null?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function Cf(e,t,n){if(n&4194240){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,nu(e,n)}}var Ri={readContext:He,useCallback:me,useContext:me,useEffect:me,useImperativeHandle:me,useInsertionEffect:me,useLayoutEffect:me,useMemo:me,useReducer:me,useRef:me,useState:me,useDebugValue:me,useDeferredValue:me,useTransition:me,useMutableSource:me,useSyncExternalStore:me,useId:me,unstable_isNewReconciler:!1},uv={readContext:He,useCallback:function(e,t){return it().memoizedState=[e,t===void 0?null:t],e},useContext:He,useEffect:Ua,useImperativeHandle:function(e,t,n){return n=n!=null?n.concat([e]):null,ei(4194308,4,mf.bind(null,t,e),n)},useLayoutEffect:function(e,t){return ei(4194308,4,e,t)},useInsertionEffect:function(e,t){return ei(4,2,e,t)},useMemo:function(e,t){var n=it();return t=t===void 0?null:t,e=e(),n.memoizedState=[e,t],e},useReducer:function(e,t,n){var r=it();return t=n!==void 0?n(t):t,r.memoizedState=r.baseState=t,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:t},r.queue=e,e=e.dispatch=lv.bind(null,q,e),[r.memoizedState,e]},useRef:function(e){var t=it();return e={current:e},t.memoizedState=e},useState:$a,useDebugValue:Pu,useDeferredValue:function(e){return it().memoizedState=e},useTransition:function(){var e=$a(!1),t=e[0];return e=iv.bind(null,e[1]),it().memoizedState=e,[t,e]},useMutableSource:function(){},useSyncExternalStore:function(e,t,n){var r=q,o=it();if(Y){if(n===void 0)throw Error(P(407));n=n()}else{if(n=t(),ue===null)throw Error(P(349));Tn&30||sf(r,t,n)}o.memoizedState=n;var i={value:n,getSnapshot:t};return o.queue=i,Ua(af.bind(null,r,i,e),[e]),r.flags|=2048,uo(9,uf.bind(null,r,i,n,t),void 0,null),n},useId:function(){var e=it(),t=ue.identifierPrefix;if(Y){var n=kt,r=Ct;n=(r&~(1<<32-et(r)-1)).toString(32)+n,t=":"+t+"R"+n,n=lo++,0<n&&(t+="H"+n.toString(32)),t+=":"}else n=ov++,t=":"+t+"r"+n.toString(32)+":";return e.memoizedState=t},unstable_isNewReconciler:!1},av={readContext:He,useCallback:yf,useContext:He,useEffect:ku,useImperativeHandle:vf,useInsertionEffect:pf,useLayoutEffect:hf,useMemo:gf,useReducer:Rl,useRef:ff,useState:function(){return Rl(so)},useDebugValue:Pu,useDeferredValue:function(e){var t=Qe();return wf(t,ie.memoizedState,e)},useTransition:function(){var e=Rl(so)[0],t=Qe().memoizedState;return[e,t]},useMutableSource:of,useSyncExternalStore:lf,useId:xf,unstable_isNewReconciler:!1},cv={readContext:He,useCallback:yf,useContext:He,useEffect:ku,useImperativeHandle:vf,useInsertionEffect:pf,useLayoutEffect:hf,useMemo:gf,useReducer:_l,useRef:ff,useState:function(){return _l(so)},useDebugValue:Pu,useDeferredValue:function(e){var t=Qe();return ie===null?t.memoizedState=e:wf(t,ie.memoizedState,e)},useTransition:function(){var e=_l(so)[0],t=Qe().memoizedState;return[e,t]},useMutableSource:of,useSyncExternalStore:lf,useId:xf,unstable_isNewReconciler:!1};function Ge(e,t){if(e&&e.defaultProps){t=J({},t),e=e.defaultProps;for(var n in e)t[n]===void 0&&(t[n]=e[n]);return t}return t}function ys(e,t,n,r){t=e.memoizedState,n=n(r,t),n=n==null?t:J({},t,n),e.memoizedState=n,e.lanes===0&&(e.updateQueue.baseState=n)}var Wi={isMounted:function(e){return(e=e._reactInternals)?On(e)===e:!1},enqueueSetState:function(e,t,n){e=e._reactInternals;var r=Ce(),o=nn(e),i=Pt(r,o);i.payload=t,n!=null&&(i.callback=n),t=en(e,i,o),t!==null&&(tt(t,e,o,r),Zo(t,e,o))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var r=Ce(),o=nn(e),i=Pt(r,o);i.tag=1,i.payload=t,n!=null&&(i.callback=n),t=en(e,i,o),t!==null&&(tt(t,e,o,r),Zo(t,e,o))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=Ce(),r=nn(e),o=Pt(n,r);o.tag=2,t!=null&&(o.callback=t),t=en(e,o,r),t!==null&&(tt(t,e,r,n),Zo(t,e,r))}};function ba(e,t,n,r,o,i,l){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(r,i,l):t.prototype&&t.prototype.isPureReactComponent?!eo(n,r)||!eo(o,i):!0}function kf(e,t,n){var r=!1,o=ln,i=t.contextType;return typeof i=="object"&&i!==null?i=He(i):(o=Ne(t)?kn:we.current,r=t.contextTypes,i=(r=r!=null)?ar(e,o):ln),t=new t(n,i),e.memoizedState=t.state!==null&&t.state!==void 0?t.state:null,t.updater=Wi,e.stateNode=t,t._reactInternals=e,r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=o,e.__reactInternalMemoizedMaskedChildContext=i),t}function Va(e,t,n,r){e=t.state,typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(n,r),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(n,r),t.state!==e&&Wi.enqueueReplaceState(t,t.state,null)}function gs(e,t,n,r){var o=e.stateNode;o.props=n,o.state=e.memoizedState,o.refs={},yu(e);var i=t.contextType;typeof i=="object"&&i!==null?o.context=He(i):(i=Ne(t)?kn:we.current,o.context=ar(e,i)),o.state=e.memoizedState,i=t.getDerivedStateFromProps,typeof i=="function"&&(ys(e,t,i,n),o.state=e.memoizedState),typeof t.getDerivedStateFromProps=="function"||typeof o.getSnapshotBeforeUpdate=="function"||typeof o.UNSAFE_componentWillMount!="function"&&typeof o.componentWillMount!="function"||(t=o.state,typeof o.componentWillMount=="function"&&o.componentWillMount(),typeof o.UNSAFE_componentWillMount=="function"&&o.UNSAFE_componentWillMount(),t!==o.state&&Wi.enqueueReplaceState(o,o.state,null),ki(e,n,o,r),o.state=e.memoizedState),typeof o.componentDidMount=="function"&&(e.flags|=4194308)}function pr(e,t){try{var n="",r=t;do n+=jh(r),r=r.return;while(r);var o=n}catch(i){o=`
Error generating stack: `+i.message+`
`+i.stack}return{value:e,source:t,stack:o,digest:null}}function Nl(e,t,n){return{value:e,source:null,stack:n??null,digest:t??null}}function ws(e,t){try{console.error(t.value)}catch(n){setTimeout(function(){throw n})}}var dv=typeof WeakMap=="function"?WeakMap:Map;function Pf(e,t,n){n=Pt(-1,n),n.tag=3,n.payload={element:null};var r=t.value;return n.callback=function(){Ni||(Ni=!0,Ns=r),ws(e,t)},n}function Tf(e,t,n){n=Pt(-1,n),n.tag=3;var r=e.type.getDerivedStateFromError;if(typeof r=="function"){var o=t.value;n.payload=function(){return r(o)},n.callback=function(){ws(e,t)}}var i=e.stateNode;return i!==null&&typeof i.componentDidCatch=="function"&&(n.callback=function(){ws(e,t),typeof r!="function"&&(tn===null?tn=new Set([this]):tn.add(this));var l=t.stack;this.componentDidCatch(t.value,{componentStack:l!==null?l:""})}),n}function Ba(e,t,n){var r=e.pingCache;if(r===null){r=e.pingCache=new dv;var o=new Set;r.set(t,o)}else o=r.get(t),o===void 0&&(o=new Set,r.set(t,o));o.has(n)||(o.add(n),e=Pv.bind(null,e,t,n),t.then(e,e))}function Wa(e){do{var t;if((t=e.tag===13)&&(t=e.memoizedState,t=t!==null?t.dehydrated!==null:!0),t)return e;e=e.return}while(e!==null);return null}function Ha(e,t,n,r,o){return e.mode&1?(e.flags|=65536,e.lanes=o,e):(e===t?e.flags|=65536:(e.flags|=128,n.flags|=131072,n.flags&=-52805,n.tag===1&&(n.alternate===null?n.tag=17:(t=Pt(-1,1),t.tag=2,en(n,t,1))),n.lanes|=1),e)}var fv=It.ReactCurrentOwner,Re=!1;function Se(e,t,n,r){t.child=e===null?ef(t,null,n,r):dr(t,e.child,n,r)}function Qa(e,t,n,r,o){n=n.render;var i=t.ref;return Xn(t,o),r=Eu(e,t,n,r,i,o),n=Cu(),e!==null&&!Re?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~o,Lt(e,t,o)):(Y&&n&&cu(t),t.flags|=1,Se(e,t,r,o),t.child)}function Ka(e,t,n,r,o){if(e===null){var i=n.type;return typeof i=="function"&&!Iu(i)&&i.defaultProps===void 0&&n.compare===null&&n.defaultProps===void 0?(t.tag=15,t.type=i,Rf(e,t,i,r,o)):(e=oi(n.type,null,r,t,t.mode,o),e.ref=t.ref,e.return=t,t.child=e)}if(i=e.child,!(e.lanes&o)){var l=i.memoizedProps;if(n=n.compare,n=n!==null?n:eo,n(l,r)&&e.ref===t.ref)return Lt(e,t,o)}return t.flags|=1,e=rn(i,r),e.ref=t.ref,e.return=t,t.child=e}function Rf(e,t,n,r,o){if(e!==null){var i=e.memoizedProps;if(eo(i,r)&&e.ref===t.ref)if(Re=!1,t.pendingProps=r=i,(e.lanes&o)!==0)e.flags&131072&&(Re=!0);else return t.lanes=e.lanes,Lt(e,t,o)}return xs(e,t,n,r,o)}function _f(e,t,n){var r=t.pendingProps,o=r.children,i=e!==null?e.memoizedState:null;if(r.mode==="hidden")if(!(t.mode&1))t.memoizedState={baseLanes:0,cachePool:null,transitions:null},W(Hn,Me),Me|=n;else{if(!(n&1073741824))return e=i!==null?i.baseLanes|n:n,t.lanes=t.childLanes=1073741824,t.memoizedState={baseLanes:e,cachePool:null,transitions:null},t.updateQueue=null,W(Hn,Me),Me|=e,null;t.memoizedState={baseLanes:0,cachePool:null,transitions:null},r=i!==null?i.baseLanes:n,W(Hn,Me),Me|=r}else i!==null?(r=i.baseLanes|n,t.memoizedState=null):r=n,W(Hn,Me),Me|=r;return Se(e,t,o,n),t.child}function Nf(e,t){var n=t.ref;(e===null&&n!==null||e!==null&&e.ref!==n)&&(t.flags|=512,t.flags|=2097152)}function xs(e,t,n,r,o){var i=Ne(n)?kn:we.current;return i=ar(t,i),Xn(t,o),n=Eu(e,t,n,r,i,o),r=Cu(),e!==null&&!Re?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~o,Lt(e,t,o)):(Y&&r&&cu(t),t.flags|=1,Se(e,t,n,o),t.child)}function Ga(e,t,n,r,o){if(Ne(n)){var i=!0;wi(t)}else i=!1;if(Xn(t,o),t.stateNode===null)ti(e,t),kf(t,n,r),gs(t,n,r,o),r=!0;else if(e===null){var l=t.stateNode,s=t.memoizedProps;l.props=s;var u=l.context,a=n.contextType;typeof a=="object"&&a!==null?a=He(a):(a=Ne(n)?kn:we.current,a=ar(t,a));var f=n.getDerivedStateFromProps,p=typeof f=="function"||typeof l.getSnapshotBeforeUpdate=="function";p||typeof l.UNSAFE_componentWillReceiveProps!="function"&&typeof l.componentWillReceiveProps!="function"||(s!==r||u!==a)&&Va(t,l,r,a),Ut=!1;var v=t.memoizedState;l.state=v,ki(t,r,l,o),u=t.memoizedState,s!==r||v!==u||_e.current||Ut?(typeof f=="function"&&(ys(t,n,f,r),u=t.memoizedState),(s=Ut||ba(t,n,s,r,v,u,a))?(p||typeof l.UNSAFE_componentWillMount!="function"&&typeof l.componentWillMount!="function"||(typeof l.componentWillMount=="function"&&l.componentWillMount(),typeof l.UNSAFE_componentWillMount=="function"&&l.UNSAFE_componentWillMount()),typeof l.componentDidMount=="function"&&(t.flags|=4194308)):(typeof l.componentDidMount=="function"&&(t.flags|=4194308),t.memoizedProps=r,t.memoizedState=u),l.props=r,l.state=u,l.context=a,r=s):(typeof l.componentDidMount=="function"&&(t.flags|=4194308),r=!1)}else{l=t.stateNode,nf(e,t),s=t.memoizedProps,a=t.type===t.elementType?s:Ge(t.type,s),l.props=a,p=t.pendingProps,v=l.context,u=n.contextType,typeof u=="object"&&u!==null?u=He(u):(u=Ne(n)?kn:we.current,u=ar(t,u));var g=n.getDerivedStateFromProps;(f=typeof g=="function"||typeof l.getSnapshotBeforeUpdate=="function")||typeof l.UNSAFE_componentWillReceiveProps!="function"&&typeof l.componentWillReceiveProps!="function"||(s!==p||v!==u)&&Va(t,l,r,u),Ut=!1,v=t.memoizedState,l.state=v,ki(t,r,l,o);var w=t.memoizedState;s!==p||v!==w||_e.current||Ut?(typeof g=="function"&&(ys(t,n,g,r),w=t.memoizedState),(a=Ut||ba(t,n,a,r,v,w,u)||!1)?(f||typeof l.UNSAFE_componentWillUpdate!="function"&&typeof l.componentWillUpdate!="function"||(typeof l.componentWillUpdate=="function"&&l.componentWillUpdate(r,w,u),typeof l.UNSAFE_componentWillUpdate=="function"&&l.UNSAFE_componentWillUpdate(r,w,u)),typeof l.componentDidUpdate=="function"&&(t.flags|=4),typeof l.getSnapshotBeforeUpdate=="function"&&(t.flags|=1024)):(typeof l.componentDidUpdate!="function"||s===e.memoizedProps&&v===e.memoizedState||(t.flags|=4),typeof l.getSnapshotBeforeUpdate!="function"||s===e.memoizedProps&&v===e.memoizedState||(t.flags|=1024),t.memoizedProps=r,t.memoizedState=w),l.props=r,l.state=w,l.context=u,r=a):(typeof l.componentDidUpdate!="function"||s===e.memoizedProps&&v===e.memoizedState||(t.flags|=4),typeof l.getSnapshotBeforeUpdate!="function"||s===e.memoizedProps&&v===e.memoizedState||(t.flags|=1024),r=!1)}return Ss(e,t,n,r,i,o)}function Ss(e,t,n,r,o,i){Nf(e,t);var l=(t.flags&128)!==0;if(!r&&!l)return o&&Ia(t,n,!1),Lt(e,t,i);r=t.stateNode,fv.current=t;var s=l&&typeof n.getDerivedStateFromError!="function"?null:r.render();return t.flags|=1,e!==null&&l?(t.child=dr(t,e.child,null,i),t.child=dr(t,null,s,i)):Se(e,t,s,i),t.memoizedState=r.state,o&&Ia(t,n,!0),t.child}function Lf(e){var t=e.stateNode;t.pendingContext?Ma(e,t.pendingContext,t.pendingContext!==t.context):t.context&&Ma(e,t.context,!1),gu(e,t.containerInfo)}function Ya(e,t,n,r,o){return cr(),fu(o),t.flags|=256,Se(e,t,n,r),t.child}var Es={dehydrated:null,treeContext:null,retryLane:0};function Cs(e){return{baseLanes:e,cachePool:null,transitions:null}}function Of(e,t,n){var r=t.pendingProps,o=X.current,i=!1,l=(t.flags&128)!==0,s;if((s=l)||(s=e!==null&&e.memoizedState===null?!1:(o&2)!==0),s?(i=!0,t.flags&=-129):(e===null||e.memoizedState!==null)&&(o|=1),W(X,o&1),e===null)return ms(t),e=t.memoizedState,e!==null&&(e=e.dehydrated,e!==null)?(t.mode&1?e.data==="$!"?t.lanes=8:t.lanes=1073741824:t.lanes=1,null):(l=r.children,e=r.fallback,i?(r=t.mode,i=t.child,l={mode:"hidden",children:l},!(r&1)&&i!==null?(i.childLanes=0,i.pendingProps=l):i=Ki(l,r,0,null),e=Cn(e,r,n,null),i.return=t,e.return=t,i.sibling=e,t.child=i,t.child.memoizedState=Cs(n),t.memoizedState=Es,e):Tu(t,l));if(o=e.memoizedState,o!==null&&(s=o.dehydrated,s!==null))return pv(e,t,l,r,s,o,n);if(i){i=r.fallback,l=t.mode,o=e.child,s=o.sibling;var u={mode:"hidden",children:r.children};return!(l&1)&&t.child!==o?(r=t.child,r.childLanes=0,r.pendingProps=u,t.deletions=null):(r=rn(o,u),r.subtreeFlags=o.subtreeFlags&14680064),s!==null?i=rn(s,i):(i=Cn(i,l,n,null),i.flags|=2),i.return=t,r.return=t,r.sibling=i,t.child=r,r=i,i=t.child,l=e.child.memoizedState,l=l===null?Cs(n):{baseLanes:l.baseLanes|n,cachePool:null,transitions:l.transitions},i.memoizedState=l,i.childLanes=e.childLanes&~n,t.memoizedState=Es,r}return i=e.child,e=i.sibling,r=rn(i,{mode:"visible",children:r.children}),!(t.mode&1)&&(r.lanes=n),r.return=t,r.sibling=null,e!==null&&(n=t.deletions,n===null?(t.deletions=[e],t.flags|=16):n.push(e)),t.child=r,t.memoizedState=null,r}function Tu(e,t){return t=Ki({mode:"visible",children:t},e.mode,0,null),t.return=e,e.child=t}function $o(e,t,n,r){return r!==null&&fu(r),dr(t,e.child,null,n),e=Tu(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function pv(e,t,n,r,o,i,l){if(n)return t.flags&256?(t.flags&=-257,r=Nl(Error(P(422))),$o(e,t,l,r)):t.memoizedState!==null?(t.child=e.child,t.flags|=128,null):(i=r.fallback,o=t.mode,r=Ki({mode:"visible",children:r.children},o,0,null),i=Cn(i,o,l,null),i.flags|=2,r.return=t,i.return=t,r.sibling=i,t.child=r,t.mode&1&&dr(t,e.child,null,l),t.child.memoizedState=Cs(l),t.memoizedState=Es,i);if(!(t.mode&1))return $o(e,t,l,null);if(o.data==="$!"){if(r=o.nextSibling&&o.nextSibling.dataset,r)var s=r.dgst;return r=s,i=Error(P(419)),r=Nl(i,r,void 0),$o(e,t,l,r)}if(s=(l&e.childLanes)!==0,Re||s){if(r=ue,r!==null){switch(l&-l){case 4:o=2;break;case 16:o=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:o=32;break;case 536870912:o=268435456;break;default:o=0}o=o&(r.suspendedLanes|l)?0:o,o!==0&&o!==i.retryLane&&(i.retryLane=o,Nt(e,o),tt(r,e,o,-1))}return Mu(),r=Nl(Error(P(421))),$o(e,t,l,r)}return o.data==="$?"?(t.flags|=128,t.child=e.child,t=Tv.bind(null,e),o._reactRetry=t,null):(e=i.treeContext,Ae=Jt(o.nextSibling),De=t,Y=!0,Je=null,e!==null&&(be[Ve++]=Ct,be[Ve++]=kt,be[Ve++]=Pn,Ct=e.id,kt=e.overflow,Pn=t),t=Tu(t,r.children),t.flags|=4096,t)}function Xa(e,t,n){e.lanes|=t;var r=e.alternate;r!==null&&(r.lanes|=t),vs(e.return,t,n)}function Ll(e,t,n,r,o){var i=e.memoizedState;i===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:r,tail:n,tailMode:o}:(i.isBackwards=t,i.rendering=null,i.renderingStartTime=0,i.last=r,i.tail=n,i.tailMode=o)}function Mf(e,t,n){var r=t.pendingProps,o=r.revealOrder,i=r.tail;if(Se(e,t,r.children,n),r=X.current,r&2)r=r&1|2,t.flags|=128;else{if(e!==null&&e.flags&128)e:for(e=t.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&Xa(e,n,t);else if(e.tag===19)Xa(e,n,t);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;e.sibling===null;){if(e.return===null||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}r&=1}if(W(X,r),!(t.mode&1))t.memoizedState=null;else switch(o){case"forwards":for(n=t.child,o=null;n!==null;)e=n.alternate,e!==null&&Pi(e)===null&&(o=n),n=n.sibling;n=o,n===null?(o=t.child,t.child=null):(o=n.sibling,n.sibling=null),Ll(t,!1,o,n,i);break;case"backwards":for(n=null,o=t.child,t.child=null;o!==null;){if(e=o.alternate,e!==null&&Pi(e)===null){t.child=o;break}e=o.sibling,o.sibling=n,n=o,o=e}Ll(t,!0,n,null,i);break;case"together":Ll(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function ti(e,t){!(t.mode&1)&&e!==null&&(e.alternate=null,t.alternate=null,t.flags|=2)}function Lt(e,t,n){if(e!==null&&(t.dependencies=e.dependencies),Rn|=t.lanes,!(n&t.childLanes))return null;if(e!==null&&t.child!==e.child)throw Error(P(153));if(t.child!==null){for(e=t.child,n=rn(e,e.pendingProps),t.child=n,n.return=t;e.sibling!==null;)e=e.sibling,n=n.sibling=rn(e,e.pendingProps),n.return=t;n.sibling=null}return t.child}function hv(e,t,n){switch(t.tag){case 3:Lf(t),cr();break;case 5:rf(t);break;case 1:Ne(t.type)&&wi(t);break;case 4:gu(t,t.stateNode.containerInfo);break;case 10:var r=t.type._context,o=t.memoizedProps.value;W(Ei,r._currentValue),r._currentValue=o;break;case 13:if(r=t.memoizedState,r!==null)return r.dehydrated!==null?(W(X,X.current&1),t.flags|=128,null):n&t.child.childLanes?Of(e,t,n):(W(X,X.current&1),e=Lt(e,t,n),e!==null?e.sibling:null);W(X,X.current&1);break;case 19:if(r=(n&t.childLanes)!==0,e.flags&128){if(r)return Mf(e,t,n);t.flags|=128}if(o=t.memoizedState,o!==null&&(o.rendering=null,o.tail=null,o.lastEffect=null),W(X,X.current),r)break;return null;case 22:case 23:return t.lanes=0,_f(e,t,n)}return Lt(e,t,n)}var If,ks,Af,Df;If=function(e,t){for(var n=t.child;n!==null;){if(n.tag===5||n.tag===6)e.appendChild(n.stateNode);else if(n.tag!==4&&n.child!==null){n.child.return=n,n=n.child;continue}if(n===t)break;for(;n.sibling===null;){if(n.return===null||n.return===t)return;n=n.return}n.sibling.return=n.return,n=n.sibling}};ks=function(){};Af=function(e,t,n,r){var o=e.memoizedProps;if(o!==r){e=t.stateNode,vn(dt.current);var i=null;switch(n){case"input":o=Ql(e,o),r=Ql(e,r),i=[];break;case"select":o=J({},o,{value:void 0}),r=J({},r,{value:void 0}),i=[];break;case"textarea":o=Yl(e,o),r=Yl(e,r),i=[];break;default:typeof o.onClick!="function"&&typeof r.onClick=="function"&&(e.onclick=yi)}ql(n,r);var l;n=null;for(a in o)if(!r.hasOwnProperty(a)&&o.hasOwnProperty(a)&&o[a]!=null)if(a==="style"){var s=o[a];for(l in s)s.hasOwnProperty(l)&&(n||(n={}),n[l]="")}else a!=="dangerouslySetInnerHTML"&&a!=="children"&&a!=="suppressContentEditableWarning"&&a!=="suppressHydrationWarning"&&a!=="autoFocus"&&(Kr.hasOwnProperty(a)?i||(i=[]):(i=i||[]).push(a,null));for(a in r){var u=r[a];if(s=o!=null?o[a]:void 0,r.hasOwnProperty(a)&&u!==s&&(u!=null||s!=null))if(a==="style")if(s){for(l in s)!s.hasOwnProperty(l)||u&&u.hasOwnProperty(l)||(n||(n={}),n[l]="");for(l in u)u.hasOwnProperty(l)&&s[l]!==u[l]&&(n||(n={}),n[l]=u[l])}else n||(i||(i=[]),i.push(a,n)),n=u;else a==="dangerouslySetInnerHTML"?(u=u?u.__html:void 0,s=s?s.__html:void 0,u!=null&&s!==u&&(i=i||[]).push(a,u)):a==="children"?typeof u!="string"&&typeof u!="number"||(i=i||[]).push(a,""+u):a!=="suppressContentEditableWarning"&&a!=="suppressHydrationWarning"&&(Kr.hasOwnProperty(a)?(u!=null&&a==="onScroll"&&K("scroll",e),i||s===u||(i=[])):(i=i||[]).push(a,u))}n&&(i=i||[]).push("style",n);var a=i;(t.updateQueue=a)&&(t.flags|=4)}};Df=function(e,t,n,r){n!==r&&(t.flags|=4)};function _r(e,t){if(!Y)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;t!==null;)t.alternate!==null&&(n=t),t=t.sibling;n===null?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var r=null;n!==null;)n.alternate!==null&&(r=n),n=n.sibling;r===null?t||e.tail===null?e.tail=null:e.tail.sibling=null:r.sibling=null}}function ve(e){var t=e.alternate!==null&&e.alternate.child===e.child,n=0,r=0;if(t)for(var o=e.child;o!==null;)n|=o.lanes|o.childLanes,r|=o.subtreeFlags&14680064,r|=o.flags&14680064,o.return=e,o=o.sibling;else for(o=e.child;o!==null;)n|=o.lanes|o.childLanes,r|=o.subtreeFlags,r|=o.flags,o.return=e,o=o.sibling;return e.subtreeFlags|=r,e.childLanes=n,t}function mv(e,t,n){var r=t.pendingProps;switch(du(t),t.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return ve(t),null;case 1:return Ne(t.type)&&gi(),ve(t),null;case 3:return r=t.stateNode,fr(),G(_e),G(we),xu(),r.pendingContext&&(r.context=r.pendingContext,r.pendingContext=null),(e===null||e.child===null)&&(zo(t)?t.flags|=4:e===null||e.memoizedState.isDehydrated&&!(t.flags&256)||(t.flags|=1024,Je!==null&&(Ms(Je),Je=null))),ks(e,t),ve(t),null;case 5:wu(t);var o=vn(io.current);if(n=t.type,e!==null&&t.stateNode!=null)Af(e,t,n,r,o),e.ref!==t.ref&&(t.flags|=512,t.flags|=2097152);else{if(!r){if(t.stateNode===null)throw Error(P(166));return ve(t),null}if(e=vn(dt.current),zo(t)){r=t.stateNode,n=t.type;var i=t.memoizedProps;switch(r[at]=t,r[ro]=i,e=(t.mode&1)!==0,n){case"dialog":K("cancel",r),K("close",r);break;case"iframe":case"object":case"embed":K("load",r);break;case"video":case"audio":for(o=0;o<Fr.length;o++)K(Fr[o],r);break;case"source":K("error",r);break;case"img":case"image":case"link":K("error",r),K("load",r);break;case"details":K("toggle",r);break;case"input":ia(r,i),K("invalid",r);break;case"select":r._wrapperState={wasMultiple:!!i.multiple},K("invalid",r);break;case"textarea":sa(r,i),K("invalid",r)}ql(n,i),o=null;for(var l in i)if(i.hasOwnProperty(l)){var s=i[l];l==="children"?typeof s=="string"?r.textContent!==s&&(i.suppressHydrationWarning!==!0&&Fo(r.textContent,s,e),o=["children",s]):typeof s=="number"&&r.textContent!==""+s&&(i.suppressHydrationWarning!==!0&&Fo(r.textContent,s,e),o=["children",""+s]):Kr.hasOwnProperty(l)&&s!=null&&l==="onScroll"&&K("scroll",r)}switch(n){case"input":_o(r),la(r,i,!0);break;case"textarea":_o(r),ua(r);break;case"select":case"option":break;default:typeof i.onClick=="function"&&(r.onclick=yi)}r=o,t.updateQueue=r,r!==null&&(t.flags|=4)}else{l=o.nodeType===9?o:o.ownerDocument,e==="http://www.w3.org/1999/xhtml"&&(e=ud(n)),e==="http://www.w3.org/1999/xhtml"?n==="script"?(e=l.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild)):typeof r.is=="string"?e=l.createElement(n,{is:r.is}):(e=l.createElement(n),n==="select"&&(l=e,r.multiple?l.multiple=!0:r.size&&(l.size=r.size))):e=l.createElementNS(e,n),e[at]=t,e[ro]=r,If(e,t,!1,!1),t.stateNode=e;e:{switch(l=Zl(n,r),n){case"dialog":K("cancel",e),K("close",e),o=r;break;case"iframe":case"object":case"embed":K("load",e),o=r;break;case"video":case"audio":for(o=0;o<Fr.length;o++)K(Fr[o],e);o=r;break;case"source":K("error",e),o=r;break;case"img":case"image":case"link":K("error",e),K("load",e),o=r;break;case"details":K("toggle",e),o=r;break;case"input":ia(e,r),o=Ql(e,r),K("invalid",e);break;case"option":o=r;break;case"select":e._wrapperState={wasMultiple:!!r.multiple},o=J({},r,{value:void 0}),K("invalid",e);break;case"textarea":sa(e,r),o=Yl(e,r),K("invalid",e);break;default:o=r}ql(n,o),s=o;for(i in s)if(s.hasOwnProperty(i)){var u=s[i];i==="style"?dd(e,u):i==="dangerouslySetInnerHTML"?(u=u?u.__html:void 0,u!=null&&ad(e,u)):i==="children"?typeof u=="string"?(n!=="textarea"||u!=="")&&Gr(e,u):typeof u=="number"&&Gr(e,""+u):i!=="suppressContentEditableWarning"&&i!=="suppressHydrationWarning"&&i!=="autoFocus"&&(Kr.hasOwnProperty(i)?u!=null&&i==="onScroll"&&K("scroll",e):u!=null&&Xs(e,i,u,l))}switch(n){case"input":_o(e),la(e,r,!1);break;case"textarea":_o(e),ua(e);break;case"option":r.value!=null&&e.setAttribute("value",""+on(r.value));break;case"select":e.multiple=!!r.multiple,i=r.value,i!=null?Qn(e,!!r.multiple,i,!1):r.defaultValue!=null&&Qn(e,!!r.multiple,r.defaultValue,!0);break;default:typeof o.onClick=="function"&&(e.onclick=yi)}switch(n){case"button":case"input":case"select":case"textarea":r=!!r.autoFocus;break e;case"img":r=!0;break e;default:r=!1}}r&&(t.flags|=4)}t.ref!==null&&(t.flags|=512,t.flags|=2097152)}return ve(t),null;case 6:if(e&&t.stateNode!=null)Df(e,t,e.memoizedProps,r);else{if(typeof r!="string"&&t.stateNode===null)throw Error(P(166));if(n=vn(io.current),vn(dt.current),zo(t)){if(r=t.stateNode,n=t.memoizedProps,r[at]=t,(i=r.nodeValue!==n)&&(e=De,e!==null))switch(e.tag){case 3:Fo(r.nodeValue,n,(e.mode&1)!==0);break;case 5:e.memoizedProps.suppressHydrationWarning!==!0&&Fo(r.nodeValue,n,(e.mode&1)!==0)}i&&(t.flags|=4)}else r=(n.nodeType===9?n:n.ownerDocument).createTextNode(r),r[at]=t,t.stateNode=r}return ve(t),null;case 13:if(G(X),r=t.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(Y&&Ae!==null&&t.mode&1&&!(t.flags&128))Zd(),cr(),t.flags|=98560,i=!1;else if(i=zo(t),r!==null&&r.dehydrated!==null){if(e===null){if(!i)throw Error(P(318));if(i=t.memoizedState,i=i!==null?i.dehydrated:null,!i)throw Error(P(317));i[at]=t}else cr(),!(t.flags&128)&&(t.memoizedState=null),t.flags|=4;ve(t),i=!1}else Je!==null&&(Ms(Je),Je=null),i=!0;if(!i)return t.flags&65536?t:null}return t.flags&128?(t.lanes=n,t):(r=r!==null,r!==(e!==null&&e.memoizedState!==null)&&r&&(t.child.flags|=8192,t.mode&1&&(e===null||X.current&1?le===0&&(le=3):Mu())),t.updateQueue!==null&&(t.flags|=4),ve(t),null);case 4:return fr(),ks(e,t),e===null&&to(t.stateNode.containerInfo),ve(t),null;case 10:return mu(t.type._context),ve(t),null;case 17:return Ne(t.type)&&gi(),ve(t),null;case 19:if(G(X),i=t.memoizedState,i===null)return ve(t),null;if(r=(t.flags&128)!==0,l=i.rendering,l===null)if(r)_r(i,!1);else{if(le!==0||e!==null&&e.flags&128)for(e=t.child;e!==null;){if(l=Pi(e),l!==null){for(t.flags|=128,_r(i,!1),r=l.updateQueue,r!==null&&(t.updateQueue=r,t.flags|=4),t.subtreeFlags=0,r=n,n=t.child;n!==null;)i=n,e=r,i.flags&=14680066,l=i.alternate,l===null?(i.childLanes=0,i.lanes=e,i.child=null,i.subtreeFlags=0,i.memoizedProps=null,i.memoizedState=null,i.updateQueue=null,i.dependencies=null,i.stateNode=null):(i.childLanes=l.childLanes,i.lanes=l.lanes,i.child=l.child,i.subtreeFlags=0,i.deletions=null,i.memoizedProps=l.memoizedProps,i.memoizedState=l.memoizedState,i.updateQueue=l.updateQueue,i.type=l.type,e=l.dependencies,i.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext}),n=n.sibling;return W(X,X.current&1|2),t.child}e=e.sibling}i.tail!==null&&re()>hr&&(t.flags|=128,r=!0,_r(i,!1),t.lanes=4194304)}else{if(!r)if(e=Pi(l),e!==null){if(t.flags|=128,r=!0,n=e.updateQueue,n!==null&&(t.updateQueue=n,t.flags|=4),_r(i,!0),i.tail===null&&i.tailMode==="hidden"&&!l.alternate&&!Y)return ve(t),null}else 2*re()-i.renderingStartTime>hr&&n!==1073741824&&(t.flags|=128,r=!0,_r(i,!1),t.lanes=4194304);i.isBackwards?(l.sibling=t.child,t.child=l):(n=i.last,n!==null?n.sibling=l:t.child=l,i.last=l)}return i.tail!==null?(t=i.tail,i.rendering=t,i.tail=t.sibling,i.renderingStartTime=re(),t.sibling=null,n=X.current,W(X,r?n&1|2:n&1),t):(ve(t),null);case 22:case 23:return Ou(),r=t.memoizedState!==null,e!==null&&e.memoizedState!==null!==r&&(t.flags|=8192),r&&t.mode&1?Me&1073741824&&(ve(t),t.subtreeFlags&6&&(t.flags|=8192)):ve(t),null;case 24:return null;case 25:return null}throw Error(P(156,t.tag))}function vv(e,t){switch(du(t),t.tag){case 1:return Ne(t.type)&&gi(),e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 3:return fr(),G(_e),G(we),xu(),e=t.flags,e&65536&&!(e&128)?(t.flags=e&-65537|128,t):null;case 5:return wu(t),null;case 13:if(G(X),e=t.memoizedState,e!==null&&e.dehydrated!==null){if(t.alternate===null)throw Error(P(340));cr()}return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 19:return G(X),null;case 4:return fr(),null;case 10:return mu(t.type._context),null;case 22:case 23:return Ou(),null;case 24:return null;default:return null}}var Uo=!1,ge=!1,yv=typeof WeakSet=="function"?WeakSet:Set,O=null;function Wn(e,t){var n=e.ref;if(n!==null)if(typeof n=="function")try{n(null)}catch(r){ne(e,t,r)}else n.current=null}function Ps(e,t,n){try{n()}catch(r){ne(e,t,r)}}var qa=!1;function gv(e,t){if(us=hi,e=$d(),au(e)){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{n=(n=e.ownerDocument)&&n.defaultView||window;var r=n.getSelection&&n.getSelection();if(r&&r.rangeCount!==0){n=r.anchorNode;var o=r.anchorOffset,i=r.focusNode;r=r.focusOffset;try{n.nodeType,i.nodeType}catch{n=null;break e}var l=0,s=-1,u=-1,a=0,f=0,p=e,v=null;t:for(;;){for(var g;p!==n||o!==0&&p.nodeType!==3||(s=l+o),p!==i||r!==0&&p.nodeType!==3||(u=l+r),p.nodeType===3&&(l+=p.nodeValue.length),(g=p.firstChild)!==null;)v=p,p=g;for(;;){if(p===e)break t;if(v===n&&++a===o&&(s=l),v===i&&++f===r&&(u=l),(g=p.nextSibling)!==null)break;p=v,v=p.parentNode}p=g}n=s===-1||u===-1?null:{start:s,end:u}}else n=null}n=n||{start:0,end:0}}else n=null;for(as={focusedElem:e,selectionRange:n},hi=!1,O=t;O!==null;)if(t=O,e=t.child,(t.subtreeFlags&1028)!==0&&e!==null)e.return=t,O=e;else for(;O!==null;){t=O;try{var w=t.alternate;if(t.flags&1024)switch(t.tag){case 0:case 11:case 15:break;case 1:if(w!==null){var y=w.memoizedProps,x=w.memoizedState,d=t.stateNode,c=d.getSnapshotBeforeUpdate(t.elementType===t.type?y:Ge(t.type,y),x);d.__reactInternalSnapshotBeforeUpdate=c}break;case 3:var h=t.stateNode.containerInfo;h.nodeType===1?h.textContent="":h.nodeType===9&&h.documentElement&&h.removeChild(h.documentElement);break;case 5:case 6:case 4:case 17:break;default:throw Error(P(163))}}catch(S){ne(t,t.return,S)}if(e=t.sibling,e!==null){e.return=t.return,O=e;break}O=t.return}return w=qa,qa=!1,w}function Br(e,t,n){var r=t.updateQueue;if(r=r!==null?r.lastEffect:null,r!==null){var o=r=r.next;do{if((o.tag&e)===e){var i=o.destroy;o.destroy=void 0,i!==void 0&&Ps(t,n,i)}o=o.next}while(o!==r)}}function Hi(e,t){if(t=t.updateQueue,t=t!==null?t.lastEffect:null,t!==null){var n=t=t.next;do{if((n.tag&e)===e){var r=n.create;n.destroy=r()}n=n.next}while(n!==t)}}function Ts(e){var t=e.ref;if(t!==null){var n=e.stateNode;switch(e.tag){case 5:e=n;break;default:e=n}typeof t=="function"?t(e):t.current=e}}function Ff(e){var t=e.alternate;t!==null&&(e.alternate=null,Ff(t)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(t=e.stateNode,t!==null&&(delete t[at],delete t[ro],delete t[fs],delete t[ev],delete t[tv])),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}function zf(e){return e.tag===5||e.tag===3||e.tag===4}function Za(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||zf(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function Rs(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.nodeType===8?n.parentNode.insertBefore(e,t):n.insertBefore(e,t):(n.nodeType===8?(t=n.parentNode,t.insertBefore(e,n)):(t=n,t.appendChild(e)),n=n._reactRootContainer,n!=null||t.onclick!==null||(t.onclick=yi));else if(r!==4&&(e=e.child,e!==null))for(Rs(e,t,n),e=e.sibling;e!==null;)Rs(e,t,n),e=e.sibling}function _s(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(r!==4&&(e=e.child,e!==null))for(_s(e,t,n),e=e.sibling;e!==null;)_s(e,t,n),e=e.sibling}var de=null,Ze=!1;function At(e,t,n){for(n=n.child;n!==null;)jf(e,t,n),n=n.sibling}function jf(e,t,n){if(ct&&typeof ct.onCommitFiberUnmount=="function")try{ct.onCommitFiberUnmount(zi,n)}catch{}switch(n.tag){case 5:ge||Wn(n,t);case 6:var r=de,o=Ze;de=null,At(e,t,n),de=r,Ze=o,de!==null&&(Ze?(e=de,n=n.stateNode,e.nodeType===8?e.parentNode.removeChild(n):e.removeChild(n)):de.removeChild(n.stateNode));break;case 18:de!==null&&(Ze?(e=de,n=n.stateNode,e.nodeType===8?Cl(e.parentNode,n):e.nodeType===1&&Cl(e,n),Zr(e)):Cl(de,n.stateNode));break;case 4:r=de,o=Ze,de=n.stateNode.containerInfo,Ze=!0,At(e,t,n),de=r,Ze=o;break;case 0:case 11:case 14:case 15:if(!ge&&(r=n.updateQueue,r!==null&&(r=r.lastEffect,r!==null))){o=r=r.next;do{var i=o,l=i.destroy;i=i.tag,l!==void 0&&(i&2||i&4)&&Ps(n,t,l),o=o.next}while(o!==r)}At(e,t,n);break;case 1:if(!ge&&(Wn(n,t),r=n.stateNode,typeof r.componentWillUnmount=="function"))try{r.props=n.memoizedProps,r.state=n.memoizedState,r.componentWillUnmount()}catch(s){ne(n,t,s)}At(e,t,n);break;case 21:At(e,t,n);break;case 22:n.mode&1?(ge=(r=ge)||n.memoizedState!==null,At(e,t,n),ge=r):At(e,t,n);break;default:At(e,t,n)}}function Ja(e){var t=e.updateQueue;if(t!==null){e.updateQueue=null;var n=e.stateNode;n===null&&(n=e.stateNode=new yv),t.forEach(function(r){var o=Rv.bind(null,e,r);n.has(r)||(n.add(r),r.then(o,o))})}}function Ke(e,t){var n=t.deletions;if(n!==null)for(var r=0;r<n.length;r++){var o=n[r];try{var i=e,l=t,s=l;e:for(;s!==null;){switch(s.tag){case 5:de=s.stateNode,Ze=!1;break e;case 3:de=s.stateNode.containerInfo,Ze=!0;break e;case 4:de=s.stateNode.containerInfo,Ze=!0;break e}s=s.return}if(de===null)throw Error(P(160));jf(i,l,o),de=null,Ze=!1;var u=o.alternate;u!==null&&(u.return=null),o.return=null}catch(a){ne(o,t,a)}}if(t.subtreeFlags&12854)for(t=t.child;t!==null;)$f(t,e),t=t.sibling}function $f(e,t){var n=e.alternate,r=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:if(Ke(t,e),ot(e),r&4){try{Br(3,e,e.return),Hi(3,e)}catch(y){ne(e,e.return,y)}try{Br(5,e,e.return)}catch(y){ne(e,e.return,y)}}break;case 1:Ke(t,e),ot(e),r&512&&n!==null&&Wn(n,n.return);break;case 5:if(Ke(t,e),ot(e),r&512&&n!==null&&Wn(n,n.return),e.flags&32){var o=e.stateNode;try{Gr(o,"")}catch(y){ne(e,e.return,y)}}if(r&4&&(o=e.stateNode,o!=null)){var i=e.memoizedProps,l=n!==null?n.memoizedProps:i,s=e.type,u=e.updateQueue;if(e.updateQueue=null,u!==null)try{s==="input"&&i.type==="radio"&&i.name!=null&&ld(o,i),Zl(s,l);var a=Zl(s,i);for(l=0;l<u.length;l+=2){var f=u[l],p=u[l+1];f==="style"?dd(o,p):f==="dangerouslySetInnerHTML"?ad(o,p):f==="children"?Gr(o,p):Xs(o,f,p,a)}switch(s){case"input":Kl(o,i);break;case"textarea":sd(o,i);break;case"select":var v=o._wrapperState.wasMultiple;o._wrapperState.wasMultiple=!!i.multiple;var g=i.value;g!=null?Qn(o,!!i.multiple,g,!1):v!==!!i.multiple&&(i.defaultValue!=null?Qn(o,!!i.multiple,i.defaultValue,!0):Qn(o,!!i.multiple,i.multiple?[]:"",!1))}o[ro]=i}catch(y){ne(e,e.return,y)}}break;case 6:if(Ke(t,e),ot(e),r&4){if(e.stateNode===null)throw Error(P(162));o=e.stateNode,i=e.memoizedProps;try{o.nodeValue=i}catch(y){ne(e,e.return,y)}}break;case 3:if(Ke(t,e),ot(e),r&4&&n!==null&&n.memoizedState.isDehydrated)try{Zr(t.containerInfo)}catch(y){ne(e,e.return,y)}break;case 4:Ke(t,e),ot(e);break;case 13:Ke(t,e),ot(e),o=e.child,o.flags&8192&&(i=o.memoizedState!==null,o.stateNode.isHidden=i,!i||o.alternate!==null&&o.alternate.memoizedState!==null||(Nu=re())),r&4&&Ja(e);break;case 22:if(f=n!==null&&n.memoizedState!==null,e.mode&1?(ge=(a=ge)||f,Ke(t,e),ge=a):Ke(t,e),ot(e),r&8192){if(a=e.memoizedState!==null,(e.stateNode.isHidden=a)&&!f&&e.mode&1)for(O=e,f=e.child;f!==null;){for(p=O=f;O!==null;){switch(v=O,g=v.child,v.tag){case 0:case 11:case 14:case 15:Br(4,v,v.return);break;case 1:Wn(v,v.return);var w=v.stateNode;if(typeof w.componentWillUnmount=="function"){r=v,n=v.return;try{t=r,w.props=t.memoizedProps,w.state=t.memoizedState,w.componentWillUnmount()}catch(y){ne(r,n,y)}}break;case 5:Wn(v,v.return);break;case 22:if(v.memoizedState!==null){tc(p);continue}}g!==null?(g.return=v,O=g):tc(p)}f=f.sibling}e:for(f=null,p=e;;){if(p.tag===5){if(f===null){f=p;try{o=p.stateNode,a?(i=o.style,typeof i.setProperty=="function"?i.setProperty("display","none","important"):i.display="none"):(s=p.stateNode,u=p.memoizedProps.style,l=u!=null&&u.hasOwnProperty("display")?u.display:null,s.style.display=cd("display",l))}catch(y){ne(e,e.return,y)}}}else if(p.tag===6){if(f===null)try{p.stateNode.nodeValue=a?"":p.memoizedProps}catch(y){ne(e,e.return,y)}}else if((p.tag!==22&&p.tag!==23||p.memoizedState===null||p===e)&&p.child!==null){p.child.return=p,p=p.child;continue}if(p===e)break e;for(;p.sibling===null;){if(p.return===null||p.return===e)break e;f===p&&(f=null),p=p.return}f===p&&(f=null),p.sibling.return=p.return,p=p.sibling}}break;case 19:Ke(t,e),ot(e),r&4&&Ja(e);break;case 21:break;default:Ke(t,e),ot(e)}}function ot(e){var t=e.flags;if(t&2){try{e:{for(var n=e.return;n!==null;){if(zf(n)){var r=n;break e}n=n.return}throw Error(P(160))}switch(r.tag){case 5:var o=r.stateNode;r.flags&32&&(Gr(o,""),r.flags&=-33);var i=Za(e);_s(e,i,o);break;case 3:case 4:var l=r.stateNode.containerInfo,s=Za(e);Rs(e,s,l);break;default:throw Error(P(161))}}catch(u){ne(e,e.return,u)}e.flags&=-3}t&4096&&(e.flags&=-4097)}function wv(e,t,n){O=e,Uf(e)}function Uf(e,t,n){for(var r=(e.mode&1)!==0;O!==null;){var o=O,i=o.child;if(o.tag===22&&r){var l=o.memoizedState!==null||Uo;if(!l){var s=o.alternate,u=s!==null&&s.memoizedState!==null||ge;s=Uo;var a=ge;if(Uo=l,(ge=u)&&!a)for(O=o;O!==null;)l=O,u=l.child,l.tag===22&&l.memoizedState!==null?nc(o):u!==null?(u.return=l,O=u):nc(o);for(;i!==null;)O=i,Uf(i),i=i.sibling;O=o,Uo=s,ge=a}ec(e)}else o.subtreeFlags&8772&&i!==null?(i.return=o,O=i):ec(e)}}function ec(e){for(;O!==null;){var t=O;if(t.flags&8772){var n=t.alternate;try{if(t.flags&8772)switch(t.tag){case 0:case 11:case 15:ge||Hi(5,t);break;case 1:var r=t.stateNode;if(t.flags&4&&!ge)if(n===null)r.componentDidMount();else{var o=t.elementType===t.type?n.memoizedProps:Ge(t.type,n.memoizedProps);r.componentDidUpdate(o,n.memoizedState,r.__reactInternalSnapshotBeforeUpdate)}var i=t.updateQueue;i!==null&&ja(t,i,r);break;case 3:var l=t.updateQueue;if(l!==null){if(n=null,t.child!==null)switch(t.child.tag){case 5:n=t.child.stateNode;break;case 1:n=t.child.stateNode}ja(t,l,n)}break;case 5:var s=t.stateNode;if(n===null&&t.flags&4){n=s;var u=t.memoizedProps;switch(t.type){case"button":case"input":case"select":case"textarea":u.autoFocus&&n.focus();break;case"img":u.src&&(n.src=u.src)}}break;case 6:break;case 4:break;case 12:break;case 13:if(t.memoizedState===null){var a=t.alternate;if(a!==null){var f=a.memoizedState;if(f!==null){var p=f.dehydrated;p!==null&&Zr(p)}}}break;case 19:case 17:case 21:case 22:case 23:case 25:break;default:throw Error(P(163))}ge||t.flags&512&&Ts(t)}catch(v){ne(t,t.return,v)}}if(t===e){O=null;break}if(n=t.sibling,n!==null){n.return=t.return,O=n;break}O=t.return}}function tc(e){for(;O!==null;){var t=O;if(t===e){O=null;break}var n=t.sibling;if(n!==null){n.return=t.return,O=n;break}O=t.return}}function nc(e){for(;O!==null;){var t=O;try{switch(t.tag){case 0:case 11:case 15:var n=t.return;try{Hi(4,t)}catch(u){ne(t,n,u)}break;case 1:var r=t.stateNode;if(typeof r.componentDidMount=="function"){var o=t.return;try{r.componentDidMount()}catch(u){ne(t,o,u)}}var i=t.return;try{Ts(t)}catch(u){ne(t,i,u)}break;case 5:var l=t.return;try{Ts(t)}catch(u){ne(t,l,u)}}}catch(u){ne(t,t.return,u)}if(t===e){O=null;break}var s=t.sibling;if(s!==null){s.return=t.return,O=s;break}O=t.return}}var xv=Math.ceil,_i=It.ReactCurrentDispatcher,Ru=It.ReactCurrentOwner,We=It.ReactCurrentBatchConfig,b=0,ue=null,oe=null,fe=0,Me=0,Hn=un(0),le=0,ao=null,Rn=0,Qi=0,_u=0,Wr=null,Te=null,Nu=0,hr=1/0,xt=null,Ni=!1,Ns=null,tn=null,bo=!1,Yt=null,Li=0,Hr=0,Ls=null,ni=-1,ri=0;function Ce(){return b&6?re():ni!==-1?ni:ni=re()}function nn(e){return e.mode&1?b&2&&fe!==0?fe&-fe:rv.transition!==null?(ri===0&&(ri=Cd()),ri):(e=B,e!==0||(e=window.event,e=e===void 0?16:Ld(e.type)),e):1}function tt(e,t,n,r){if(50<Hr)throw Hr=0,Ls=null,Error(P(185));yo(e,n,r),(!(b&2)||e!==ue)&&(e===ue&&(!(b&2)&&(Qi|=n),le===4&&Vt(e,fe)),Le(e,r),n===1&&b===0&&!(t.mode&1)&&(hr=re()+500,Vi&&an()))}function Le(e,t){var n=e.callbackNode;rm(e,t);var r=pi(e,e===ue?fe:0);if(r===0)n!==null&&da(n),e.callbackNode=null,e.callbackPriority=0;else if(t=r&-r,e.callbackPriority!==t){if(n!=null&&da(n),t===1)e.tag===0?nv(rc.bind(null,e)):Yd(rc.bind(null,e)),Zm(function(){!(b&6)&&an()}),n=null;else{switch(kd(r)){case 1:n=tu;break;case 4:n=Sd;break;case 16:n=fi;break;case 536870912:n=Ed;break;default:n=fi}n=Gf(n,bf.bind(null,e))}e.callbackPriority=t,e.callbackNode=n}}function bf(e,t){if(ni=-1,ri=0,b&6)throw Error(P(327));var n=e.callbackNode;if(qn()&&e.callbackNode!==n)return null;var r=pi(e,e===ue?fe:0);if(r===0)return null;if(r&30||r&e.expiredLanes||t)t=Oi(e,r);else{t=r;var o=b;b|=2;var i=Bf();(ue!==e||fe!==t)&&(xt=null,hr=re()+500,En(e,t));do try{Cv();break}catch(s){Vf(e,s)}while(!0);hu(),_i.current=i,b=o,oe!==null?t=0:(ue=null,fe=0,t=le)}if(t!==0){if(t===2&&(o=rs(e),o!==0&&(r=o,t=Os(e,o))),t===1)throw n=ao,En(e,0),Vt(e,r),Le(e,re()),n;if(t===6)Vt(e,r);else{if(o=e.current.alternate,!(r&30)&&!Sv(o)&&(t=Oi(e,r),t===2&&(i=rs(e),i!==0&&(r=i,t=Os(e,i))),t===1))throw n=ao,En(e,0),Vt(e,r),Le(e,re()),n;switch(e.finishedWork=o,e.finishedLanes=r,t){case 0:case 1:throw Error(P(345));case 2:pn(e,Te,xt);break;case 3:if(Vt(e,r),(r&130023424)===r&&(t=Nu+500-re(),10<t)){if(pi(e,0)!==0)break;if(o=e.suspendedLanes,(o&r)!==r){Ce(),e.pingedLanes|=e.suspendedLanes&o;break}e.timeoutHandle=ds(pn.bind(null,e,Te,xt),t);break}pn(e,Te,xt);break;case 4:if(Vt(e,r),(r&4194240)===r)break;for(t=e.eventTimes,o=-1;0<r;){var l=31-et(r);i=1<<l,l=t[l],l>o&&(o=l),r&=~i}if(r=o,r=re()-r,r=(120>r?120:480>r?480:1080>r?1080:1920>r?1920:3e3>r?3e3:4320>r?4320:1960*xv(r/1960))-r,10<r){e.timeoutHandle=ds(pn.bind(null,e,Te,xt),r);break}pn(e,Te,xt);break;case 5:pn(e,Te,xt);break;default:throw Error(P(329))}}}return Le(e,re()),e.callbackNode===n?bf.bind(null,e):null}function Os(e,t){var n=Wr;return e.current.memoizedState.isDehydrated&&(En(e,t).flags|=256),e=Oi(e,t),e!==2&&(t=Te,Te=n,t!==null&&Ms(t)),e}function Ms(e){Te===null?Te=e:Te.push.apply(Te,e)}function Sv(e){for(var t=e;;){if(t.flags&16384){var n=t.updateQueue;if(n!==null&&(n=n.stores,n!==null))for(var r=0;r<n.length;r++){var o=n[r],i=o.getSnapshot;o=o.value;try{if(!nt(i(),o))return!1}catch{return!1}}}if(n=t.child,t.subtreeFlags&16384&&n!==null)n.return=t,t=n;else{if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function Vt(e,t){for(t&=~_u,t&=~Qi,e.suspendedLanes|=t,e.pingedLanes&=~t,e=e.expirationTimes;0<t;){var n=31-et(t),r=1<<n;e[n]=-1,t&=~r}}function rc(e){if(b&6)throw Error(P(327));qn();var t=pi(e,0);if(!(t&1))return Le(e,re()),null;var n=Oi(e,t);if(e.tag!==0&&n===2){var r=rs(e);r!==0&&(t=r,n=Os(e,r))}if(n===1)throw n=ao,En(e,0),Vt(e,t),Le(e,re()),n;if(n===6)throw Error(P(345));return e.finishedWork=e.current.alternate,e.finishedLanes=t,pn(e,Te,xt),Le(e,re()),null}function Lu(e,t){var n=b;b|=1;try{return e(t)}finally{b=n,b===0&&(hr=re()+500,Vi&&an())}}function _n(e){Yt!==null&&Yt.tag===0&&!(b&6)&&qn();var t=b;b|=1;var n=We.transition,r=B;try{if(We.transition=null,B=1,e)return e()}finally{B=r,We.transition=n,b=t,!(b&6)&&an()}}function Ou(){Me=Hn.current,G(Hn)}function En(e,t){e.finishedWork=null,e.finishedLanes=0;var n=e.timeoutHandle;if(n!==-1&&(e.timeoutHandle=-1,qm(n)),oe!==null)for(n=oe.return;n!==null;){var r=n;switch(du(r),r.tag){case 1:r=r.type.childContextTypes,r!=null&&gi();break;case 3:fr(),G(_e),G(we),xu();break;case 5:wu(r);break;case 4:fr();break;case 13:G(X);break;case 19:G(X);break;case 10:mu(r.type._context);break;case 22:case 23:Ou()}n=n.return}if(ue=e,oe=e=rn(e.current,null),fe=Me=t,le=0,ao=null,_u=Qi=Rn=0,Te=Wr=null,mn!==null){for(t=0;t<mn.length;t++)if(n=mn[t],r=n.interleaved,r!==null){n.interleaved=null;var o=r.next,i=n.pending;if(i!==null){var l=i.next;i.next=o,r.next=l}n.pending=r}mn=null}return e}function Vf(e,t){do{var n=oe;try{if(hu(),Jo.current=Ri,Ti){for(var r=q.memoizedState;r!==null;){var o=r.queue;o!==null&&(o.pending=null),r=r.next}Ti=!1}if(Tn=0,se=ie=q=null,Vr=!1,lo=0,Ru.current=null,n===null||n.return===null){le=1,ao=t,oe=null;break}e:{var i=e,l=n.return,s=n,u=t;if(t=fe,s.flags|=32768,u!==null&&typeof u=="object"&&typeof u.then=="function"){var a=u,f=s,p=f.tag;if(!(f.mode&1)&&(p===0||p===11||p===15)){var v=f.alternate;v?(f.updateQueue=v.updateQueue,f.memoizedState=v.memoizedState,f.lanes=v.lanes):(f.updateQueue=null,f.memoizedState=null)}var g=Wa(l);if(g!==null){g.flags&=-257,Ha(g,l,s,i,t),g.mode&1&&Ba(i,a,t),t=g,u=a;var w=t.updateQueue;if(w===null){var y=new Set;y.add(u),t.updateQueue=y}else w.add(u);break e}else{if(!(t&1)){Ba(i,a,t),Mu();break e}u=Error(P(426))}}else if(Y&&s.mode&1){var x=Wa(l);if(x!==null){!(x.flags&65536)&&(x.flags|=256),Ha(x,l,s,i,t),fu(pr(u,s));break e}}i=u=pr(u,s),le!==4&&(le=2),Wr===null?Wr=[i]:Wr.push(i),i=l;do{switch(i.tag){case 3:i.flags|=65536,t&=-t,i.lanes|=t;var d=Pf(i,u,t);za(i,d);break e;case 1:s=u;var c=i.type,h=i.stateNode;if(!(i.flags&128)&&(typeof c.getDerivedStateFromError=="function"||h!==null&&typeof h.componentDidCatch=="function"&&(tn===null||!tn.has(h)))){i.flags|=65536,t&=-t,i.lanes|=t;var S=Tf(i,s,t);za(i,S);break e}}i=i.return}while(i!==null)}Hf(n)}catch(C){t=C,oe===n&&n!==null&&(oe=n=n.return);continue}break}while(!0)}function Bf(){var e=_i.current;return _i.current=Ri,e===null?Ri:e}function Mu(){(le===0||le===3||le===2)&&(le=4),ue===null||!(Rn&268435455)&&!(Qi&268435455)||Vt(ue,fe)}function Oi(e,t){var n=b;b|=2;var r=Bf();(ue!==e||fe!==t)&&(xt=null,En(e,t));do try{Ev();break}catch(o){Vf(e,o)}while(!0);if(hu(),b=n,_i.current=r,oe!==null)throw Error(P(261));return ue=null,fe=0,le}function Ev(){for(;oe!==null;)Wf(oe)}function Cv(){for(;oe!==null&&!Gh();)Wf(oe)}function Wf(e){var t=Kf(e.alternate,e,Me);e.memoizedProps=e.pendingProps,t===null?Hf(e):oe=t,Ru.current=null}function Hf(e){var t=e;do{var n=t.alternate;if(e=t.return,t.flags&32768){if(n=vv(n,t),n!==null){n.flags&=32767,oe=n;return}if(e!==null)e.flags|=32768,e.subtreeFlags=0,e.deletions=null;else{le=6,oe=null;return}}else if(n=mv(n,t,Me),n!==null){oe=n;return}if(t=t.sibling,t!==null){oe=t;return}oe=t=e}while(t!==null);le===0&&(le=5)}function pn(e,t,n){var r=B,o=We.transition;try{We.transition=null,B=1,kv(e,t,n,r)}finally{We.transition=o,B=r}return null}function kv(e,t,n,r){do qn();while(Yt!==null);if(b&6)throw Error(P(327));n=e.finishedWork;var o=e.finishedLanes;if(n===null)return null;if(e.finishedWork=null,e.finishedLanes=0,n===e.current)throw Error(P(177));e.callbackNode=null,e.callbackPriority=0;var i=n.lanes|n.childLanes;if(om(e,i),e===ue&&(oe=ue=null,fe=0),!(n.subtreeFlags&2064)&&!(n.flags&2064)||bo||(bo=!0,Gf(fi,function(){return qn(),null})),i=(n.flags&15990)!==0,n.subtreeFlags&15990||i){i=We.transition,We.transition=null;var l=B;B=1;var s=b;b|=4,Ru.current=null,gv(e,n),$f(n,e),Wm(as),hi=!!us,as=us=null,e.current=n,wv(n),Yh(),b=s,B=l,We.transition=i}else e.current=n;if(bo&&(bo=!1,Yt=e,Li=o),i=e.pendingLanes,i===0&&(tn=null),Zh(n.stateNode),Le(e,re()),t!==null)for(r=e.onRecoverableError,n=0;n<t.length;n++)o=t[n],r(o.value,{componentStack:o.stack,digest:o.digest});if(Ni)throw Ni=!1,e=Ns,Ns=null,e;return Li&1&&e.tag!==0&&qn(),i=e.pendingLanes,i&1?e===Ls?Hr++:(Hr=0,Ls=e):Hr=0,an(),null}function qn(){if(Yt!==null){var e=kd(Li),t=We.transition,n=B;try{if(We.transition=null,B=16>e?16:e,Yt===null)var r=!1;else{if(e=Yt,Yt=null,Li=0,b&6)throw Error(P(331));var o=b;for(b|=4,O=e.current;O!==null;){var i=O,l=i.child;if(O.flags&16){var s=i.deletions;if(s!==null){for(var u=0;u<s.length;u++){var a=s[u];for(O=a;O!==null;){var f=O;switch(f.tag){case 0:case 11:case 15:Br(8,f,i)}var p=f.child;if(p!==null)p.return=f,O=p;else for(;O!==null;){f=O;var v=f.sibling,g=f.return;if(Ff(f),f===a){O=null;break}if(v!==null){v.return=g,O=v;break}O=g}}}var w=i.alternate;if(w!==null){var y=w.child;if(y!==null){w.child=null;do{var x=y.sibling;y.sibling=null,y=x}while(y!==null)}}O=i}}if(i.subtreeFlags&2064&&l!==null)l.return=i,O=l;else e:for(;O!==null;){if(i=O,i.flags&2048)switch(i.tag){case 0:case 11:case 15:Br(9,i,i.return)}var d=i.sibling;if(d!==null){d.return=i.return,O=d;break e}O=i.return}}var c=e.current;for(O=c;O!==null;){l=O;var h=l.child;if(l.subtreeFlags&2064&&h!==null)h.return=l,O=h;else e:for(l=c;O!==null;){if(s=O,s.flags&2048)try{switch(s.tag){case 0:case 11:case 15:Hi(9,s)}}catch(C){ne(s,s.return,C)}if(s===l){O=null;break e}var S=s.sibling;if(S!==null){S.return=s.return,O=S;break e}O=s.return}}if(b=o,an(),ct&&typeof ct.onPostCommitFiberRoot=="function")try{ct.onPostCommitFiberRoot(zi,e)}catch{}r=!0}return r}finally{B=n,We.transition=t}}return!1}function oc(e,t,n){t=pr(n,t),t=Pf(e,t,1),e=en(e,t,1),t=Ce(),e!==null&&(yo(e,1,t),Le(e,t))}function ne(e,t,n){if(e.tag===3)oc(e,e,n);else for(;t!==null;){if(t.tag===3){oc(t,e,n);break}else if(t.tag===1){var r=t.stateNode;if(typeof t.type.getDerivedStateFromError=="function"||typeof r.componentDidCatch=="function"&&(tn===null||!tn.has(r))){e=pr(n,e),e=Tf(t,e,1),t=en(t,e,1),e=Ce(),t!==null&&(yo(t,1,e),Le(t,e));break}}t=t.return}}function Pv(e,t,n){var r=e.pingCache;r!==null&&r.delete(t),t=Ce(),e.pingedLanes|=e.suspendedLanes&n,ue===e&&(fe&n)===n&&(le===4||le===3&&(fe&130023424)===fe&&500>re()-Nu?En(e,0):_u|=n),Le(e,t)}function Qf(e,t){t===0&&(e.mode&1?(t=Oo,Oo<<=1,!(Oo&130023424)&&(Oo=4194304)):t=1);var n=Ce();e=Nt(e,t),e!==null&&(yo(e,t,n),Le(e,n))}function Tv(e){var t=e.memoizedState,n=0;t!==null&&(n=t.retryLane),Qf(e,n)}function Rv(e,t){var n=0;switch(e.tag){case 13:var r=e.stateNode,o=e.memoizedState;o!==null&&(n=o.retryLane);break;case 19:r=e.stateNode;break;default:throw Error(P(314))}r!==null&&r.delete(t),Qf(e,n)}var Kf;Kf=function(e,t,n){if(e!==null)if(e.memoizedProps!==t.pendingProps||_e.current)Re=!0;else{if(!(e.lanes&n)&&!(t.flags&128))return Re=!1,hv(e,t,n);Re=!!(e.flags&131072)}else Re=!1,Y&&t.flags&1048576&&Xd(t,Si,t.index);switch(t.lanes=0,t.tag){case 2:var r=t.type;ti(e,t),e=t.pendingProps;var o=ar(t,we.current);Xn(t,n),o=Eu(null,t,r,e,o,n);var i=Cu();return t.flags|=1,typeof o=="object"&&o!==null&&typeof o.render=="function"&&o.$$typeof===void 0?(t.tag=1,t.memoizedState=null,t.updateQueue=null,Ne(r)?(i=!0,wi(t)):i=!1,t.memoizedState=o.state!==null&&o.state!==void 0?o.state:null,yu(t),o.updater=Wi,t.stateNode=o,o._reactInternals=t,gs(t,r,e,n),t=Ss(null,t,r,!0,i,n)):(t.tag=0,Y&&i&&cu(t),Se(null,t,o,n),t=t.child),t;case 16:r=t.elementType;e:{switch(ti(e,t),e=t.pendingProps,o=r._init,r=o(r._payload),t.type=r,o=t.tag=Nv(r),e=Ge(r,e),o){case 0:t=xs(null,t,r,e,n);break e;case 1:t=Ga(null,t,r,e,n);break e;case 11:t=Qa(null,t,r,e,n);break e;case 14:t=Ka(null,t,r,Ge(r.type,e),n);break e}throw Error(P(306,r,""))}return t;case 0:return r=t.type,o=t.pendingProps,o=t.elementType===r?o:Ge(r,o),xs(e,t,r,o,n);case 1:return r=t.type,o=t.pendingProps,o=t.elementType===r?o:Ge(r,o),Ga(e,t,r,o,n);case 3:e:{if(Lf(t),e===null)throw Error(P(387));r=t.pendingProps,i=t.memoizedState,o=i.element,nf(e,t),ki(t,r,null,n);var l=t.memoizedState;if(r=l.element,i.isDehydrated)if(i={element:r,isDehydrated:!1,cache:l.cache,pendingSuspenseBoundaries:l.pendingSuspenseBoundaries,transitions:l.transitions},t.updateQueue.baseState=i,t.memoizedState=i,t.flags&256){o=pr(Error(P(423)),t),t=Ya(e,t,r,n,o);break e}else if(r!==o){o=pr(Error(P(424)),t),t=Ya(e,t,r,n,o);break e}else for(Ae=Jt(t.stateNode.containerInfo.firstChild),De=t,Y=!0,Je=null,n=ef(t,null,r,n),t.child=n;n;)n.flags=n.flags&-3|4096,n=n.sibling;else{if(cr(),r===o){t=Lt(e,t,n);break e}Se(e,t,r,n)}t=t.child}return t;case 5:return rf(t),e===null&&ms(t),r=t.type,o=t.pendingProps,i=e!==null?e.memoizedProps:null,l=o.children,cs(r,o)?l=null:i!==null&&cs(r,i)&&(t.flags|=32),Nf(e,t),Se(e,t,l,n),t.child;case 6:return e===null&&ms(t),null;case 13:return Of(e,t,n);case 4:return gu(t,t.stateNode.containerInfo),r=t.pendingProps,e===null?t.child=dr(t,null,r,n):Se(e,t,r,n),t.child;case 11:return r=t.type,o=t.pendingProps,o=t.elementType===r?o:Ge(r,o),Qa(e,t,r,o,n);case 7:return Se(e,t,t.pendingProps,n),t.child;case 8:return Se(e,t,t.pendingProps.children,n),t.child;case 12:return Se(e,t,t.pendingProps.children,n),t.child;case 10:e:{if(r=t.type._context,o=t.pendingProps,i=t.memoizedProps,l=o.value,W(Ei,r._currentValue),r._currentValue=l,i!==null)if(nt(i.value,l)){if(i.children===o.children&&!_e.current){t=Lt(e,t,n);break e}}else for(i=t.child,i!==null&&(i.return=t);i!==null;){var s=i.dependencies;if(s!==null){l=i.child;for(var u=s.firstContext;u!==null;){if(u.context===r){if(i.tag===1){u=Pt(-1,n&-n),u.tag=2;var a=i.updateQueue;if(a!==null){a=a.shared;var f=a.pending;f===null?u.next=u:(u.next=f.next,f.next=u),a.pending=u}}i.lanes|=n,u=i.alternate,u!==null&&(u.lanes|=n),vs(i.return,n,t),s.lanes|=n;break}u=u.next}}else if(i.tag===10)l=i.type===t.type?null:i.child;else if(i.tag===18){if(l=i.return,l===null)throw Error(P(341));l.lanes|=n,s=l.alternate,s!==null&&(s.lanes|=n),vs(l,n,t),l=i.sibling}else l=i.child;if(l!==null)l.return=i;else for(l=i;l!==null;){if(l===t){l=null;break}if(i=l.sibling,i!==null){i.return=l.return,l=i;break}l=l.return}i=l}Se(e,t,o.children,n),t=t.child}return t;case 9:return o=t.type,r=t.pendingProps.children,Xn(t,n),o=He(o),r=r(o),t.flags|=1,Se(e,t,r,n),t.child;case 14:return r=t.type,o=Ge(r,t.pendingProps),o=Ge(r.type,o),Ka(e,t,r,o,n);case 15:return Rf(e,t,t.type,t.pendingProps,n);case 17:return r=t.type,o=t.pendingProps,o=t.elementType===r?o:Ge(r,o),ti(e,t),t.tag=1,Ne(r)?(e=!0,wi(t)):e=!1,Xn(t,n),kf(t,r,o),gs(t,r,o,n),Ss(null,t,r,!0,e,n);case 19:return Mf(e,t,n);case 22:return _f(e,t,n)}throw Error(P(156,t.tag))};function Gf(e,t){return xd(e,t)}function _v(e,t,n,r){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function Be(e,t,n,r){return new _v(e,t,n,r)}function Iu(e){return e=e.prototype,!(!e||!e.isReactComponent)}function Nv(e){if(typeof e=="function")return Iu(e)?1:0;if(e!=null){if(e=e.$$typeof,e===Zs)return 11;if(e===Js)return 14}return 2}function rn(e,t){var n=e.alternate;return n===null?(n=Be(e.tag,t,e.key,e.mode),n.elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=e.flags&14680064,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n}function oi(e,t,n,r,o,i){var l=2;if(r=e,typeof e=="function")Iu(e)&&(l=1);else if(typeof e=="string")l=5;else e:switch(e){case Dn:return Cn(n.children,o,i,t);case qs:l=8,o|=8;break;case Vl:return e=Be(12,n,t,o|2),e.elementType=Vl,e.lanes=i,e;case Bl:return e=Be(13,n,t,o),e.elementType=Bl,e.lanes=i,e;case Wl:return e=Be(19,n,t,o),e.elementType=Wl,e.lanes=i,e;case rd:return Ki(n,o,i,t);default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case td:l=10;break e;case nd:l=9;break e;case Zs:l=11;break e;case Js:l=14;break e;case $t:l=16,r=null;break e}throw Error(P(130,e==null?e:typeof e,""))}return t=Be(l,n,t,o),t.elementType=e,t.type=r,t.lanes=i,t}function Cn(e,t,n,r){return e=Be(7,e,r,t),e.lanes=n,e}function Ki(e,t,n,r){return e=Be(22,e,r,t),e.elementType=rd,e.lanes=n,e.stateNode={isHidden:!1},e}function Ol(e,t,n){return e=Be(6,e,null,t),e.lanes=n,e}function Ml(e,t,n){return t=Be(4,e.children!==null?e.children:[],e.key,t),t.lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}function Lv(e,t,n,r,o){this.tag=t,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=fl(0),this.expirationTimes=fl(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=fl(0),this.identifierPrefix=r,this.onRecoverableError=o,this.mutableSourceEagerHydrationData=null}function Au(e,t,n,r,o,i,l,s,u){return e=new Lv(e,t,n,s,u),t===1?(t=1,i===!0&&(t|=8)):t=0,i=Be(3,null,null,t),e.current=i,i.stateNode=e,i.memoizedState={element:r,isDehydrated:n,cache:null,transitions:null,pendingSuspenseBoundaries:null},yu(i),e}function Ov(e,t,n){var r=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:An,key:r==null?null:""+r,children:e,containerInfo:t,implementation:n}}function Yf(e){if(!e)return ln;e=e._reactInternals;e:{if(On(e)!==e||e.tag!==1)throw Error(P(170));var t=e;do{switch(t.tag){case 3:t=t.stateNode.context;break e;case 1:if(Ne(t.type)){t=t.stateNode.__reactInternalMemoizedMergedChildContext;break e}}t=t.return}while(t!==null);throw Error(P(171))}if(e.tag===1){var n=e.type;if(Ne(n))return Gd(e,n,t)}return t}function Xf(e,t,n,r,o,i,l,s,u){return e=Au(n,r,!0,e,o,i,l,s,u),e.context=Yf(null),n=e.current,r=Ce(),o=nn(n),i=Pt(r,o),i.callback=t??null,en(n,i,o),e.current.lanes=o,yo(e,o,r),Le(e,r),e}function Gi(e,t,n,r){var o=t.current,i=Ce(),l=nn(o);return n=Yf(n),t.context===null?t.context=n:t.pendingContext=n,t=Pt(i,l),t.payload={element:e},r=r===void 0?null:r,r!==null&&(t.callback=r),e=en(o,t,l),e!==null&&(tt(e,o,l,i),Zo(e,o,l)),l}function Mi(e){if(e=e.current,!e.child)return null;switch(e.child.tag){case 5:return e.child.stateNode;default:return e.child.stateNode}}function ic(e,t){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var n=e.retryLane;e.retryLane=n!==0&&n<t?n:t}}function Du(e,t){ic(e,t),(e=e.alternate)&&ic(e,t)}function Mv(){return null}var qf=typeof reportError=="function"?reportError:function(e){console.error(e)};function Fu(e){this._internalRoot=e}Yi.prototype.render=Fu.prototype.render=function(e){var t=this._internalRoot;if(t===null)throw Error(P(409));Gi(e,t,null,null)};Yi.prototype.unmount=Fu.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var t=e.containerInfo;_n(function(){Gi(null,e,null,null)}),t[_t]=null}};function Yi(e){this._internalRoot=e}Yi.prototype.unstable_scheduleHydration=function(e){if(e){var t=Rd();e={blockedOn:null,target:e,priority:t};for(var n=0;n<bt.length&&t!==0&&t<bt[n].priority;n++);bt.splice(n,0,e),n===0&&Nd(e)}};function zu(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function Xi(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11&&(e.nodeType!==8||e.nodeValue!==" react-mount-point-unstable "))}function lc(){}function Iv(e,t,n,r,o){if(o){if(typeof r=="function"){var i=r;r=function(){var a=Mi(l);i.call(a)}}var l=Xf(t,r,e,0,null,!1,!1,"",lc);return e._reactRootContainer=l,e[_t]=l.current,to(e.nodeType===8?e.parentNode:e),_n(),l}for(;o=e.lastChild;)e.removeChild(o);if(typeof r=="function"){var s=r;r=function(){var a=Mi(u);s.call(a)}}var u=Au(e,0,!1,null,null,!1,!1,"",lc);return e._reactRootContainer=u,e[_t]=u.current,to(e.nodeType===8?e.parentNode:e),_n(function(){Gi(t,u,n,r)}),u}function qi(e,t,n,r,o){var i=n._reactRootContainer;if(i){var l=i;if(typeof o=="function"){var s=o;o=function(){var u=Mi(l);s.call(u)}}Gi(t,l,e,o)}else l=Iv(n,t,e,o,r);return Mi(l)}Pd=function(e){switch(e.tag){case 3:var t=e.stateNode;if(t.current.memoizedState.isDehydrated){var n=Dr(t.pendingLanes);n!==0&&(nu(t,n|1),Le(t,re()),!(b&6)&&(hr=re()+500,an()))}break;case 13:_n(function(){var r=Nt(e,1);if(r!==null){var o=Ce();tt(r,e,1,o)}}),Du(e,1)}};ru=function(e){if(e.tag===13){var t=Nt(e,134217728);if(t!==null){var n=Ce();tt(t,e,134217728,n)}Du(e,134217728)}};Td=function(e){if(e.tag===13){var t=nn(e),n=Nt(e,t);if(n!==null){var r=Ce();tt(n,e,t,r)}Du(e,t)}};Rd=function(){return B};_d=function(e,t){var n=B;try{return B=e,t()}finally{B=n}};es=function(e,t,n){switch(t){case"input":if(Kl(e,n),t=n.name,n.type==="radio"&&t!=null){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll("input[name="+JSON.stringify(""+t)+'][type="radio"]'),t=0;t<n.length;t++){var r=n[t];if(r!==e&&r.form===e.form){var o=bi(r);if(!o)throw Error(P(90));id(r),Kl(r,o)}}}break;case"textarea":sd(e,n);break;case"select":t=n.value,t!=null&&Qn(e,!!n.multiple,t,!1)}};hd=Lu;md=_n;var Av={usingClientEntryPoint:!1,Events:[wo,$n,bi,fd,pd,Lu]},Nr={findFiberByHostInstance:hn,bundleType:0,version:"18.3.1",rendererPackageName:"react-dom"},Dv={bundleType:Nr.bundleType,version:Nr.version,rendererPackageName:Nr.rendererPackageName,rendererConfig:Nr.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:It.ReactCurrentDispatcher,findHostInstanceByFiber:function(e){return e=gd(e),e===null?null:e.stateNode},findFiberByHostInstance:Nr.findFiberByHostInstance||Mv,findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.3.1-next-f1338f8080-20240426"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var Vo=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!Vo.isDisabled&&Vo.supportsFiber)try{zi=Vo.inject(Dv),ct=Vo}catch{}}ze.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=Av;ze.createPortal=function(e,t){var n=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!zu(t))throw Error(P(200));return Ov(e,t,null,n)};ze.createRoot=function(e,t){if(!zu(e))throw Error(P(299));var n=!1,r="",o=qf;return t!=null&&(t.unstable_strictMode===!0&&(n=!0),t.identifierPrefix!==void 0&&(r=t.identifierPrefix),t.onRecoverableError!==void 0&&(o=t.onRecoverableError)),t=Au(e,1,!1,null,null,n,!1,r,o),e[_t]=t.current,to(e.nodeType===8?e.parentNode:e),new Fu(t)};ze.findDOMNode=function(e){if(e==null)return null;if(e.nodeType===1)return e;var t=e._reactInternals;if(t===void 0)throw typeof e.render=="function"?Error(P(188)):(e=Object.keys(e).join(","),Error(P(268,e)));return e=gd(t),e=e===null?null:e.stateNode,e};ze.flushSync=function(e){return _n(e)};ze.hydrate=function(e,t,n){if(!Xi(t))throw Error(P(200));return qi(null,e,t,!0,n)};ze.hydrateRoot=function(e,t,n){if(!zu(e))throw Error(P(405));var r=n!=null&&n.hydratedSources||null,o=!1,i="",l=qf;if(n!=null&&(n.unstable_strictMode===!0&&(o=!0),n.identifierPrefix!==void 0&&(i=n.identifierPrefix),n.onRecoverableError!==void 0&&(l=n.onRecoverableError)),t=Xf(t,null,e,1,n??null,o,!1,i,l),e[_t]=t.current,to(e),r)for(e=0;e<r.length;e++)n=r[e],o=n._getVersion,o=o(n._source),t.mutableSourceEagerHydrationData==null?t.mutableSourceEagerHydrationData=[n,o]:t.mutableSourceEagerHydrationData.push(n,o);return new Yi(t)};ze.render=function(e,t,n){if(!Xi(t))throw Error(P(200));return qi(null,e,t,!1,n)};ze.unmountComponentAtNode=function(e){if(!Xi(e))throw Error(P(40));return e._reactRootContainer?(_n(function(){qi(null,null,e,!1,function(){e._reactRootContainer=null,e[_t]=null})}),!0):!1};ze.unstable_batchedUpdates=Lu;ze.unstable_renderSubtreeIntoContainer=function(e,t,n,r){if(!Xi(n))throw Error(P(200));if(e==null||e._reactInternals===void 0)throw Error(P(38));return qi(e,t,n,!1,r)};ze.version="18.3.1-next-f1338f8080-20240426";function Zf(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(Zf)}catch(e){console.error(e)}}Zf(),qc.exports=ze;var Zi=qc.exports;const Fv=$c(Zi);var Jf,sc=Zi;Jf=sc.createRoot,sc.hydrateRoot;const zv="modulepreload",jv=function(e){return"/"+e},uc={},cn=function(t,n,r){let o=Promise.resolve();if(n&&n.length>0){document.getElementsByTagName("link");const l=document.querySelector("meta[property=csp-nonce]"),s=(l==null?void 0:l.nonce)||(l==null?void 0:l.getAttribute("nonce"));o=Promise.allSettled(n.map(u=>{if(u=jv(u),u in uc)return;uc[u]=!0;const a=u.endsWith(".css"),f=a?'[rel="stylesheet"]':"";if(document.querySelector(`link[href="${u}"]${f}`))return;const p=document.createElement("link");if(p.rel=a?"stylesheet":zv,a||(p.as="script"),p.crossOrigin="",p.href=u,s&&p.setAttribute("nonce",s),document.head.appendChild(p),a)return new Promise((v,g)=>{p.addEventListener("load",v),p.addEventListener("error",()=>g(new Error(`Unable to preload CSS for ${u}`)))})}))}function i(l){const s=new Event("vite:preloadError",{cancelable:!0});if(s.payload=l,window.dispatchEvent(s),!s.defaultPrevented)throw l}return o.then(l=>{for(const s of l||[])s.status==="rejected"&&i(s.reason);return t().catch(i)})};var ju={};Object.defineProperty(ju,"__esModule",{value:!0});ju.parse=Hv;ju.serialize=Qv;const $v=/^[\u0021-\u003A\u003C\u003E-\u007E]+$/,Uv=/^[\u0021-\u003A\u003C-\u007E]*$/,bv=/^([.]?[a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)([.][a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)*$/i,Vv=/^[\u0020-\u003A\u003D-\u007E]*$/,Bv=Object.prototype.toString,Wv=(()=>{const e=function(){};return e.prototype=Object.create(null),e})();function Hv(e,t){const n=new Wv,r=e.length;if(r<2)return n;const o=(t==null?void 0:t.decode)||Kv;let i=0;do{const l=e.indexOf("=",i);if(l===-1)break;const s=e.indexOf(";",i),u=s===-1?r:s;if(l>u){i=e.lastIndexOf(";",l-1)+1;continue}const a=ac(e,i,l),f=cc(e,l,a),p=e.slice(a,f);if(n[p]===void 0){let v=ac(e,l+1,u),g=cc(e,u,v);const w=o(e.slice(v,g));n[p]=w}i=u+1}while(i<r);return n}function ac(e,t,n){do{const r=e.charCodeAt(t);if(r!==32&&r!==9)return t}while(++t<n);return n}function cc(e,t,n){for(;t>n;){const r=e.charCodeAt(--t);if(r!==32&&r!==9)return t+1}return n}function Qv(e,t,n){const r=(n==null?void 0:n.encode)||encodeURIComponent;if(!$v.test(e))throw new TypeError(`argument name is invalid: ${e}`);const o=r(t);if(!Uv.test(o))throw new TypeError(`argument val is invalid: ${t}`);let i=e+"="+o;if(!n)return i;if(n.maxAge!==void 0){if(!Number.isInteger(n.maxAge))throw new TypeError(`option maxAge is invalid: ${n.maxAge}`);i+="; Max-Age="+n.maxAge}if(n.domain){if(!bv.test(n.domain))throw new TypeError(`option domain is invalid: ${n.domain}`);i+="; Domain="+n.domain}if(n.path){if(!Vv.test(n.path))throw new TypeError(`option path is invalid: ${n.path}`);i+="; Path="+n.path}if(n.expires){if(!Gv(n.expires)||!Number.isFinite(n.expires.valueOf()))throw new TypeError(`option expires is invalid: ${n.expires}`);i+="; Expires="+n.expires.toUTCString()}if(n.httpOnly&&(i+="; HttpOnly"),n.secure&&(i+="; Secure"),n.partitioned&&(i+="; Partitioned"),n.priority)switch(typeof n.priority=="string"?n.priority.toLowerCase():void 0){case"low":i+="; Priority=Low";break;case"medium":i+="; Priority=Medium";break;case"high":i+="; Priority=High";break;default:throw new TypeError(`option priority is invalid: ${n.priority}`)}if(n.sameSite)switch(typeof n.sameSite=="string"?n.sameSite.toLowerCase():n.sameSite){case!0:case"strict":i+="; SameSite=Strict";break;case"lax":i+="; SameSite=Lax";break;case"none":i+="; SameSite=None";break;default:throw new TypeError(`option sameSite is invalid: ${n.sameSite}`)}return i}function Kv(e){if(e.indexOf("%")===-1)return e;try{return decodeURIComponent(e)}catch{return e}}function Gv(e){return Bv.call(e)==="[object Date]"}/**
 * react-router v7.5.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */var dc="popstate";function Yv(e={}){function t(r,o){let{pathname:i,search:l,hash:s}=r.location;return Is("",{pathname:i,search:l,hash:s},o.state&&o.state.usr||null,o.state&&o.state.key||"default")}function n(r,o){return typeof o=="string"?o:co(o)}return qv(t,n,null,e)}function Z(e,t){if(e===!1||e===null||typeof e>"u")throw new Error(t)}function ft(e,t){if(!e){typeof console<"u"&&console.warn(t);try{throw new Error(t)}catch{}}}function Xv(){return Math.random().toString(36).substring(2,10)}function fc(e,t){return{usr:e.state,key:e.key,idx:t}}function Is(e,t,n=null,r){return{pathname:typeof e=="string"?e:e.pathname,search:"",hash:"",...typeof t=="string"?gr(t):t,state:n,key:t&&t.key||r||Xv()}}function co({pathname:e="/",search:t="",hash:n=""}){return t&&t!=="?"&&(e+=t.charAt(0)==="?"?t:"?"+t),n&&n!=="#"&&(e+=n.charAt(0)==="#"?n:"#"+n),e}function gr(e){let t={};if(e){let n=e.indexOf("#");n>=0&&(t.hash=e.substring(n),e=e.substring(0,n));let r=e.indexOf("?");r>=0&&(t.search=e.substring(r),e=e.substring(0,r)),e&&(t.pathname=e)}return t}function qv(e,t,n,r={}){let{window:o=document.defaultView,v5Compat:i=!1}=r,l=o.history,s="POP",u=null,a=f();a==null&&(a=0,l.replaceState({...l.state,idx:a},""));function f(){return(l.state||{idx:null}).idx}function p(){s="POP";let x=f(),d=x==null?null:x-a;a=x,u&&u({action:s,location:y.location,delta:d})}function v(x,d){s="PUSH";let c=Is(y.location,x,d);a=f()+1;let h=fc(c,a),S=y.createHref(c);try{l.pushState(h,"",S)}catch(C){if(C instanceof DOMException&&C.name==="DataCloneError")throw C;o.location.assign(S)}i&&u&&u({action:s,location:y.location,delta:1})}function g(x,d){s="REPLACE";let c=Is(y.location,x,d);a=f();let h=fc(c,a),S=y.createHref(c);l.replaceState(h,"",S),i&&u&&u({action:s,location:y.location,delta:0})}function w(x){let d=o.location.origin!=="null"?o.location.origin:o.location.href,c=typeof x=="string"?x:co(x);return c=c.replace(/ $/,"%20"),Z(d,`No window.location.(origin|href) available to create URL for href: ${c}`),new URL(c,d)}let y={get action(){return s},get location(){return e(o,l)},listen(x){if(u)throw new Error("A history only accepts one active listener");return o.addEventListener(dc,p),u=x,()=>{o.removeEventListener(dc,p),u=null}},createHref(x){return t(o,x)},createURL:w,encodeLocation(x){let d=w(x);return{pathname:d.pathname,search:d.search,hash:d.hash}},push:v,replace:g,go(x){return l.go(x)}};return y}function ep(e,t,n="/"){return Zv(e,t,n,!1)}function Zv(e,t,n,r){let o=typeof t=="string"?gr(t):t,i=Ot(o.pathname||"/",n);if(i==null)return null;let l=tp(e);Jv(l);let s=null;for(let u=0;s==null&&u<l.length;++u){let a=cy(i);s=uy(l[u],a,r)}return s}function tp(e,t=[],n=[],r=""){let o=(i,l,s)=>{let u={relativePath:s===void 0?i.path||"":s,caseSensitive:i.caseSensitive===!0,childrenIndex:l,route:i};u.relativePath.startsWith("/")&&(Z(u.relativePath.startsWith(r),`Absolute route path "${u.relativePath}" nested under path "${r}" is not valid. An absolute child route path must start with the combined path of all its parent routes.`),u.relativePath=u.relativePath.slice(r.length));let a=Tt([r,u.relativePath]),f=n.concat(u);i.children&&i.children.length>0&&(Z(i.index!==!0,`Index routes must not have child routes. Please remove all child routes from route path "${a}".`),tp(i.children,t,f,a)),!(i.path==null&&!i.index)&&t.push({path:a,score:ly(a,i.index),routesMeta:f})};return e.forEach((i,l)=>{var s;if(i.path===""||!((s=i.path)!=null&&s.includes("?")))o(i,l);else for(let u of np(i.path))o(i,l,u)}),t}function np(e){let t=e.split("/");if(t.length===0)return[];let[n,...r]=t,o=n.endsWith("?"),i=n.replace(/\?$/,"");if(r.length===0)return o?[i,""]:[i];let l=np(r.join("/")),s=[];return s.push(...l.map(u=>u===""?i:[i,u].join("/"))),o&&s.push(...l),s.map(u=>e.startsWith("/")&&u===""?"/":u)}function Jv(e){e.sort((t,n)=>t.score!==n.score?n.score-t.score:sy(t.routesMeta.map(r=>r.childrenIndex),n.routesMeta.map(r=>r.childrenIndex)))}var ey=/^:[\w-]+$/,ty=3,ny=2,ry=1,oy=10,iy=-2,pc=e=>e==="*";function ly(e,t){let n=e.split("/"),r=n.length;return n.some(pc)&&(r+=iy),t&&(r+=ny),n.filter(o=>!pc(o)).reduce((o,i)=>o+(ey.test(i)?ty:i===""?ry:oy),r)}function sy(e,t){return e.length===t.length&&e.slice(0,-1).every((r,o)=>r===t[o])?e[e.length-1]-t[t.length-1]:0}function uy(e,t,n=!1){let{routesMeta:r}=e,o={},i="/",l=[];for(let s=0;s<r.length;++s){let u=r[s],a=s===r.length-1,f=i==="/"?t:t.slice(i.length)||"/",p=Ii({path:u.relativePath,caseSensitive:u.caseSensitive,end:a},f),v=u.route;if(!p&&a&&n&&!r[r.length-1].route.index&&(p=Ii({path:u.relativePath,caseSensitive:u.caseSensitive,end:!1},f)),!p)return null;Object.assign(o,p.params),l.push({params:o,pathname:Tt([i,p.pathname]),pathnameBase:hy(Tt([i,p.pathnameBase])),route:v}),p.pathnameBase!=="/"&&(i=Tt([i,p.pathnameBase]))}return l}function Ii(e,t){typeof e=="string"&&(e={path:e,caseSensitive:!1,end:!0});let[n,r]=ay(e.path,e.caseSensitive,e.end),o=t.match(n);if(!o)return null;let i=o[0],l=i.replace(/(.)\/+$/,"$1"),s=o.slice(1);return{params:r.reduce((a,{paramName:f,isOptional:p},v)=>{if(f==="*"){let w=s[v]||"";l=i.slice(0,i.length-w.length).replace(/(.)\/+$/,"$1")}const g=s[v];return p&&!g?a[f]=void 0:a[f]=(g||"").replace(/%2F/g,"/"),a},{}),pathname:i,pathnameBase:l,pattern:e}}function ay(e,t=!1,n=!0){ft(e==="*"||!e.endsWith("*")||e.endsWith("/*"),`Route path "${e}" will be treated as if it were "${e.replace(/\*$/,"/*")}" because the \`*\` character must always follow a \`/\` in the pattern. To get rid of this warning, please change the route path to "${e.replace(/\*$/,"/*")}".`);let r=[],o="^"+e.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,(l,s,u)=>(r.push({paramName:s,isOptional:u!=null}),u?"/?([^\\/]+)?":"/([^\\/]+)"));return e.endsWith("*")?(r.push({paramName:"*"}),o+=e==="*"||e==="/*"?"(.*)$":"(?:\\/(.+)|\\/*)$"):n?o+="\\/*$":e!==""&&e!=="/"&&(o+="(?:(?=\\/|$))"),[new RegExp(o,t?void 0:"i"),r]}function cy(e){try{return e.split("/").map(t=>decodeURIComponent(t).replace(/\//g,"%2F")).join("/")}catch(t){return ft(!1,`The URL path "${e}" could not be decoded because it is a malformed URL segment. This is probably due to a bad percent encoding (${t}).`),e}}function Ot(e,t){if(t==="/")return e;if(!e.toLowerCase().startsWith(t.toLowerCase()))return null;let n=t.endsWith("/")?t.length-1:t.length,r=e.charAt(n);return r&&r!=="/"?null:e.slice(n)||"/"}function dy(e,t="/"){let{pathname:n,search:r="",hash:o=""}=typeof e=="string"?gr(e):e;return{pathname:n?n.startsWith("/")?n:fy(n,t):t,search:my(r),hash:vy(o)}}function fy(e,t){let n=t.replace(/\/+$/,"").split("/");return e.split("/").forEach(o=>{o===".."?n.length>1&&n.pop():o!=="."&&n.push(o)}),n.length>1?n.join("/"):"/"}function Il(e,t,n,r){return`Cannot include a '${e}' character in a manually specified \`to.${t}\` field [${JSON.stringify(r)}].  Please separate it out to the \`to.${n}\` field. Alternatively you may provide the full path as a string in <Link to="..."> and the router will parse it for you.`}function py(e){return e.filter((t,n)=>n===0||t.route.path&&t.route.path.length>0)}function rp(e){let t=py(e);return t.map((n,r)=>r===t.length-1?n.pathname:n.pathnameBase)}function op(e,t,n,r=!1){let o;typeof e=="string"?o=gr(e):(o={...e},Z(!o.pathname||!o.pathname.includes("?"),Il("?","pathname","search",o)),Z(!o.pathname||!o.pathname.includes("#"),Il("#","pathname","hash",o)),Z(!o.search||!o.search.includes("#"),Il("#","search","hash",o)));let i=e===""||o.pathname==="",l=i?"/":o.pathname,s;if(l==null)s=n;else{let p=t.length-1;if(!r&&l.startsWith("..")){let v=l.split("/");for(;v[0]==="..";)v.shift(),p-=1;o.pathname=v.join("/")}s=p>=0?t[p]:"/"}let u=dy(o,s),a=l&&l!=="/"&&l.endsWith("/"),f=(i||l===".")&&n.endsWith("/");return!u.pathname.endsWith("/")&&(a||f)&&(u.pathname+="/"),u}var Tt=e=>e.join("/").replace(/\/\/+/g,"/"),hy=e=>e.replace(/\/+$/,"").replace(/^\/*/,"/"),my=e=>!e||e==="?"?"":e.startsWith("?")?e:"?"+e,vy=e=>!e||e==="#"?"":e.startsWith("#")?e:"#"+e;function yy(e){return e!=null&&typeof e.status=="number"&&typeof e.statusText=="string"&&typeof e.internal=="boolean"&&"data"in e}var ip=["POST","PUT","PATCH","DELETE"];new Set(ip);var gy=["GET",...ip];new Set(gy);var wr=m.createContext(null);wr.displayName="DataRouter";var Ji=m.createContext(null);Ji.displayName="DataRouterState";var lp=m.createContext({isTransitioning:!1});lp.displayName="ViewTransition";var wy=m.createContext(new Map);wy.displayName="Fetchers";var xy=m.createContext(null);xy.displayName="Await";var pt=m.createContext(null);pt.displayName="Navigation";var So=m.createContext(null);So.displayName="Location";var ht=m.createContext({outlet:null,matches:[],isDataRoute:!1});ht.displayName="Route";var $u=m.createContext(null);$u.displayName="RouteError";function Sy(e,{relative:t}={}){Z(Eo(),"useHref() may be used only in the context of a <Router> component.");let{basename:n,navigator:r}=m.useContext(pt),{hash:o,pathname:i,search:l}=Co(e,{relative:t}),s=i;return n!=="/"&&(s=i==="/"?n:Tt([n,i])),r.createHref({pathname:s,search:l,hash:o})}function Eo(){return m.useContext(So)!=null}function Mn(){return Z(Eo(),"useLocation() may be used only in the context of a <Router> component."),m.useContext(So).location}var sp="You should call navigate() in a React.useEffect(), not when your component is first rendered.";function up(e){m.useContext(pt).static||m.useLayoutEffect(e)}function Ey(){let{isDataRoute:e}=m.useContext(ht);return e?Dy():Cy()}function Cy(){Z(Eo(),"useNavigate() may be used only in the context of a <Router> component.");let e=m.useContext(wr),{basename:t,navigator:n}=m.useContext(pt),{matches:r}=m.useContext(ht),{pathname:o}=Mn(),i=JSON.stringify(rp(r)),l=m.useRef(!1);return up(()=>{l.current=!0}),m.useCallback((u,a={})=>{if(ft(l.current,sp),!l.current)return;if(typeof u=="number"){n.go(u);return}let f=op(u,JSON.parse(i),o,a.relative==="path");e==null&&t!=="/"&&(f.pathname=f.pathname==="/"?t:Tt([t,f.pathname])),(a.replace?n.replace:n.push)(f,a.state,a)},[t,n,i,o,e])}m.createContext(null);function Yw(){let{matches:e}=m.useContext(ht),t=e[e.length-1];return t?t.params:{}}function Co(e,{relative:t}={}){let{matches:n}=m.useContext(ht),{pathname:r}=Mn(),o=JSON.stringify(rp(n));return m.useMemo(()=>op(e,JSON.parse(o),r,t==="path"),[e,o,r,t])}function ky(e,t){return ap(e,t)}function ap(e,t,n,r){var c;Z(Eo(),"useRoutes() may be used only in the context of a <Router> component.");let{navigator:o,static:i}=m.useContext(pt),{matches:l}=m.useContext(ht),s=l[l.length-1],u=s?s.params:{},a=s?s.pathname:"/",f=s?s.pathnameBase:"/",p=s&&s.route;{let h=p&&p.path||"";cp(a,!p||h.endsWith("*")||h.endsWith("*?"),`You rendered descendant <Routes> (or called \`useRoutes()\`) at "${a}" (under <Route path="${h}">) but the parent route path has no trailing "*". This means if you navigate deeper, the parent won't match anymore and therefore the child routes will never render.

Please change the parent <Route path="${h}"> to <Route path="${h==="/"?"*":`${h}/*`}">.`)}let v=Mn(),g;if(t){let h=typeof t=="string"?gr(t):t;Z(f==="/"||((c=h.pathname)==null?void 0:c.startsWith(f)),`When overriding the location using \`<Routes location>\` or \`useRoutes(routes, location)\`, the location pathname must begin with the portion of the URL pathname that was matched by all parent routes. The current pathname base is "${f}" but pathname "${h.pathname}" was given in the \`location\` prop.`),g=h}else g=v;let w=g.pathname||"/",y=w;if(f!=="/"){let h=f.replace(/^\//,"").split("/");y="/"+w.replace(/^\//,"").split("/").slice(h.length).join("/")}let x=!i&&n&&n.matches&&n.matches.length>0?n.matches:ep(e,{pathname:y});ft(p||x!=null,`No routes matched location "${g.pathname}${g.search}${g.hash}" `),ft(x==null||x[x.length-1].route.element!==void 0||x[x.length-1].route.Component!==void 0||x[x.length-1].route.lazy!==void 0,`Matched leaf route at location "${g.pathname}${g.search}${g.hash}" does not have an element or Component. This means it will render an <Outlet /> with a null value by default resulting in an "empty" page.`);let d=Ny(x&&x.map(h=>Object.assign({},h,{params:Object.assign({},u,h.params),pathname:Tt([f,o.encodeLocation?o.encodeLocation(h.pathname).pathname:h.pathname]),pathnameBase:h.pathnameBase==="/"?f:Tt([f,o.encodeLocation?o.encodeLocation(h.pathnameBase).pathname:h.pathnameBase])})),l,n,r);return t&&d?m.createElement(So.Provider,{value:{location:{pathname:"/",search:"",hash:"",state:null,key:"default",...g},navigationType:"POP"}},d):d}function Py(){let e=Ay(),t=yy(e)?`${e.status} ${e.statusText}`:e instanceof Error?e.message:JSON.stringify(e),n=e instanceof Error?e.stack:null,r="rgba(200,200,200, 0.5)",o={padding:"0.5rem",backgroundColor:r},i={padding:"2px 4px",backgroundColor:r},l=null;return console.error("Error handled by React Router default ErrorBoundary:",e),l=m.createElement(m.Fragment,null,m.createElement("p",null,"💿 Hey developer 👋"),m.createElement("p",null,"You can provide a way better UX than this when your app throws errors by providing your own ",m.createElement("code",{style:i},"ErrorBoundary")," or"," ",m.createElement("code",{style:i},"errorElement")," prop on your route.")),m.createElement(m.Fragment,null,m.createElement("h2",null,"Unexpected Application Error!"),m.createElement("h3",{style:{fontStyle:"italic"}},t),n?m.createElement("pre",{style:o},n):null,l)}var Ty=m.createElement(Py,null),Ry=class extends m.Component{constructor(e){super(e),this.state={location:e.location,revalidation:e.revalidation,error:e.error}}static getDerivedStateFromError(e){return{error:e}}static getDerivedStateFromProps(e,t){return t.location!==e.location||t.revalidation!=="idle"&&e.revalidation==="idle"?{error:e.error,location:e.location,revalidation:e.revalidation}:{error:e.error!==void 0?e.error:t.error,location:t.location,revalidation:e.revalidation||t.revalidation}}componentDidCatch(e,t){console.error("React Router caught the following error during render",e,t)}render(){return this.state.error!==void 0?m.createElement(ht.Provider,{value:this.props.routeContext},m.createElement($u.Provider,{value:this.state.error,children:this.props.component})):this.props.children}};function _y({routeContext:e,match:t,children:n}){let r=m.useContext(wr);return r&&r.static&&r.staticContext&&(t.route.errorElement||t.route.ErrorBoundary)&&(r.staticContext._deepestRenderedBoundaryId=t.route.id),m.createElement(ht.Provider,{value:e},n)}function Ny(e,t=[],n=null,r=null){if(e==null){if(!n)return null;if(n.errors)e=n.matches;else if(t.length===0&&!n.initialized&&n.matches.length>0)e=n.matches;else return null}let o=e,i=n==null?void 0:n.errors;if(i!=null){let u=o.findIndex(a=>a.route.id&&(i==null?void 0:i[a.route.id])!==void 0);Z(u>=0,`Could not find a matching route for errors on route IDs: ${Object.keys(i).join(",")}`),o=o.slice(0,Math.min(o.length,u+1))}let l=!1,s=-1;if(n)for(let u=0;u<o.length;u++){let a=o[u];if((a.route.HydrateFallback||a.route.hydrateFallbackElement)&&(s=u),a.route.id){let{loaderData:f,errors:p}=n,v=a.route.loader&&!f.hasOwnProperty(a.route.id)&&(!p||p[a.route.id]===void 0);if(a.route.lazy||v){l=!0,s>=0?o=o.slice(0,s+1):o=[o[0]];break}}}return o.reduceRight((u,a,f)=>{let p,v=!1,g=null,w=null;n&&(p=i&&a.route.id?i[a.route.id]:void 0,g=a.route.errorElement||Ty,l&&(s<0&&f===0?(cp("route-fallback",!1,"No `HydrateFallback` element provided to render during initial hydration"),v=!0,w=null):s===f&&(v=!0,w=a.route.hydrateFallbackElement||null)));let y=t.concat(o.slice(0,f+1)),x=()=>{let d;return p?d=g:v?d=w:a.route.Component?d=m.createElement(a.route.Component,null):a.route.element?d=a.route.element:d=u,m.createElement(_y,{match:a,routeContext:{outlet:u,matches:y,isDataRoute:n!=null},children:d})};return n&&(a.route.ErrorBoundary||a.route.errorElement||f===0)?m.createElement(Ry,{location:n.location,revalidation:n.revalidation,component:g,error:p,children:x(),routeContext:{outlet:null,matches:y,isDataRoute:!0}}):x()},null)}function Uu(e){return`${e} must be used within a data router.  See https://reactrouter.com/en/main/routers/picking-a-router.`}function Ly(e){let t=m.useContext(wr);return Z(t,Uu(e)),t}function Oy(e){let t=m.useContext(Ji);return Z(t,Uu(e)),t}function My(e){let t=m.useContext(ht);return Z(t,Uu(e)),t}function bu(e){let t=My(e),n=t.matches[t.matches.length-1];return Z(n.route.id,`${e} can only be used on routes that contain a unique "id"`),n.route.id}function Iy(){return bu("useRouteId")}function Ay(){var r;let e=m.useContext($u),t=Oy("useRouteError"),n=bu("useRouteError");return e!==void 0?e:(r=t.errors)==null?void 0:r[n]}function Dy(){let{router:e}=Ly("useNavigate"),t=bu("useNavigate"),n=m.useRef(!1);return up(()=>{n.current=!0}),m.useCallback(async(o,i={})=>{ft(n.current,sp),n.current&&(typeof o=="number"?e.navigate(o):await e.navigate(o,{fromRouteId:t,...i}))},[e,t])}var hc={};function cp(e,t,n){!t&&!hc[e]&&(hc[e]=!0,ft(!1,n))}m.memo(Fy);function Fy({routes:e,future:t,state:n}){return ap(e,void 0,n,t)}function gt(e){Z(!1,"A <Route> is only ever to be used as the child of <Routes> element, never rendered directly. Please wrap your <Route> in a <Routes>.")}function zy({basename:e="/",children:t=null,location:n,navigationType:r="POP",navigator:o,static:i=!1}){Z(!Eo(),"You cannot render a <Router> inside another <Router>. You should never have more than one in your app.");let l=e.replace(/^\/*/,"/"),s=m.useMemo(()=>({basename:l,navigator:o,static:i,future:{}}),[l,o,i]);typeof n=="string"&&(n=gr(n));let{pathname:u="/",search:a="",hash:f="",state:p=null,key:v="default"}=n,g=m.useMemo(()=>{let w=Ot(u,l);return w==null?null:{location:{pathname:w,search:a,hash:f,state:p,key:v},navigationType:r}},[l,u,a,f,p,v,r]);return ft(g!=null,`<Router basename="${l}"> is not able to match the URL "${u}${a}${f}" because it does not start with the basename, so the <Router> won't render anything.`),g==null?null:m.createElement(pt.Provider,{value:s},m.createElement(So.Provider,{children:t,value:g}))}function jy({children:e,location:t}){return ky(As(e),t)}function As(e,t=[]){let n=[];return m.Children.forEach(e,(r,o)=>{if(!m.isValidElement(r))return;let i=[...t,o];if(r.type===m.Fragment){n.push.apply(n,As(r.props.children,i));return}Z(r.type===gt,`[${typeof r.type=="string"?r.type:r.type.name}] is not a <Route> component. All component children of <Routes> must be a <Route> or <React.Fragment>`),Z(!r.props.index||!r.props.children,"An index route cannot have child routes.");let l={id:r.props.id||i.join("-"),caseSensitive:r.props.caseSensitive,element:r.props.element,Component:r.props.Component,index:r.props.index,path:r.props.path,loader:r.props.loader,action:r.props.action,hydrateFallbackElement:r.props.hydrateFallbackElement,HydrateFallback:r.props.HydrateFallback,errorElement:r.props.errorElement,ErrorBoundary:r.props.ErrorBoundary,hasErrorBoundary:r.props.hasErrorBoundary===!0||r.props.ErrorBoundary!=null||r.props.errorElement!=null,shouldRevalidate:r.props.shouldRevalidate,handle:r.props.handle,lazy:r.props.lazy};r.props.children&&(l.children=As(r.props.children,i)),n.push(l)}),n}var ii="get",li="application/x-www-form-urlencoded";function el(e){return e!=null&&typeof e.tagName=="string"}function $y(e){return el(e)&&e.tagName.toLowerCase()==="button"}function Uy(e){return el(e)&&e.tagName.toLowerCase()==="form"}function by(e){return el(e)&&e.tagName.toLowerCase()==="input"}function Vy(e){return!!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)}function By(e,t){return e.button===0&&(!t||t==="_self")&&!Vy(e)}var Bo=null;function Wy(){if(Bo===null)try{new FormData(document.createElement("form"),0),Bo=!1}catch{Bo=!0}return Bo}var Hy=new Set(["application/x-www-form-urlencoded","multipart/form-data","text/plain"]);function Al(e){return e!=null&&!Hy.has(e)?(ft(!1,`"${e}" is not a valid \`encType\` for \`<Form>\`/\`<fetcher.Form>\` and will default to "${li}"`),null):e}function Qy(e,t){let n,r,o,i,l;if(Uy(e)){let s=e.getAttribute("action");r=s?Ot(s,t):null,n=e.getAttribute("method")||ii,o=Al(e.getAttribute("enctype"))||li,i=new FormData(e)}else if($y(e)||by(e)&&(e.type==="submit"||e.type==="image")){let s=e.form;if(s==null)throw new Error('Cannot submit a <button> or <input type="submit"> without a <form>');let u=e.getAttribute("formaction")||s.getAttribute("action");if(r=u?Ot(u,t):null,n=e.getAttribute("formmethod")||s.getAttribute("method")||ii,o=Al(e.getAttribute("formenctype"))||Al(s.getAttribute("enctype"))||li,i=new FormData(s,e),!Wy()){let{name:a,type:f,value:p}=e;if(f==="image"){let v=a?`${a}.`:"";i.append(`${v}x`,"0"),i.append(`${v}y`,"0")}else a&&i.append(a,p)}}else{if(el(e))throw new Error('Cannot submit element that is not <form>, <button>, or <input type="submit|image">');n=ii,r=null,o=li,l=e}return i&&o==="text/plain"&&(l=i,i=void 0),{action:r,method:n.toLowerCase(),encType:o,formData:i,body:l}}function Vu(e,t){if(e===!1||e===null||typeof e>"u")throw new Error(t)}async function Ky(e,t){if(e.id in t)return t[e.id];try{let n=await import(e.module);return t[e.id]=n,n}catch(n){return console.error(`Error loading route module \`${e.module}\`, reloading page...`),console.error(n),window.__reactRouterContext&&window.__reactRouterContext.isSpaMode,window.location.reload(),new Promise(()=>{})}}function Gy(e){return e==null?!1:e.href==null?e.rel==="preload"&&typeof e.imageSrcSet=="string"&&typeof e.imageSizes=="string":typeof e.rel=="string"&&typeof e.href=="string"}async function Yy(e,t,n){let r=await Promise.all(e.map(async o=>{let i=t.routes[o.route.id];if(i){let l=await Ky(i,n);return l.links?l.links():[]}return[]}));return Jy(r.flat(1).filter(Gy).filter(o=>o.rel==="stylesheet"||o.rel==="preload").map(o=>o.rel==="stylesheet"?{...o,rel:"prefetch",as:"style"}:{...o,rel:"prefetch"}))}function mc(e,t,n,r,o,i){let l=(u,a)=>n[a]?u.route.id!==n[a].route.id:!0,s=(u,a)=>{var f;return n[a].pathname!==u.pathname||((f=n[a].route.path)==null?void 0:f.endsWith("*"))&&n[a].params["*"]!==u.params["*"]};return i==="assets"?t.filter((u,a)=>l(u,a)||s(u,a)):i==="data"?t.filter((u,a)=>{var p;let f=r.routes[u.route.id];if(!f||!f.hasLoader)return!1;if(l(u,a)||s(u,a))return!0;if(u.route.shouldRevalidate){let v=u.route.shouldRevalidate({currentUrl:new URL(o.pathname+o.search+o.hash,window.origin),currentParams:((p=n[0])==null?void 0:p.params)||{},nextUrl:new URL(e,window.origin),nextParams:u.params,defaultShouldRevalidate:!0});if(typeof v=="boolean")return v}return!0}):[]}function Xy(e,t,{includeHydrateFallback:n}={}){return qy(e.map(r=>{let o=t.routes[r.route.id];if(!o)return[];let i=[o.module];return o.clientActionModule&&(i=i.concat(o.clientActionModule)),o.clientLoaderModule&&(i=i.concat(o.clientLoaderModule)),n&&o.hydrateFallbackModule&&(i=i.concat(o.hydrateFallbackModule)),o.imports&&(i=i.concat(o.imports)),i}).flat(1))}function qy(e){return[...new Set(e)]}function Zy(e){let t={},n=Object.keys(e).sort();for(let r of n)t[r]=e[r];return t}function Jy(e,t){let n=new Set;return new Set(t),e.reduce((r,o)=>{let i=JSON.stringify(Zy(o));return n.has(i)||(n.add(i),r.push({key:i,link:o})),r},[])}function eg(e,t){let n=typeof e=="string"?new URL(e,typeof window>"u"?"server://singlefetch/":window.location.origin):e;return n.pathname==="/"?n.pathname="_root.data":t&&Ot(n.pathname,t)==="/"?n.pathname=`${t.replace(/\/$/,"")}/_root.data`:n.pathname=`${n.pathname.replace(/\/$/,"")}.data`,n}function dp(){let e=m.useContext(wr);return Vu(e,"You must render this element inside a <DataRouterContext.Provider> element"),e}function tg(){let e=m.useContext(Ji);return Vu(e,"You must render this element inside a <DataRouterStateContext.Provider> element"),e}var Bu=m.createContext(void 0);Bu.displayName="FrameworkContext";function fp(){let e=m.useContext(Bu);return Vu(e,"You must render this element inside a <HydratedRouter> element"),e}function ng(e,t){let n=m.useContext(Bu),[r,o]=m.useState(!1),[i,l]=m.useState(!1),{onFocus:s,onBlur:u,onMouseEnter:a,onMouseLeave:f,onTouchStart:p}=t,v=m.useRef(null);m.useEffect(()=>{if(e==="render"&&l(!0),e==="viewport"){let y=d=>{d.forEach(c=>{l(c.isIntersecting)})},x=new IntersectionObserver(y,{threshold:.5});return v.current&&x.observe(v.current),()=>{x.disconnect()}}},[e]),m.useEffect(()=>{if(r){let y=setTimeout(()=>{l(!0)},100);return()=>{clearTimeout(y)}}},[r]);let g=()=>{o(!0)},w=()=>{o(!1),l(!1)};return n?e!=="intent"?[i,v,{}]:[i,v,{onFocus:Lr(s,g),onBlur:Lr(u,w),onMouseEnter:Lr(a,g),onMouseLeave:Lr(f,w),onTouchStart:Lr(p,g)}]:[!1,v,{}]}function Lr(e,t){return n=>{e&&e(n),n.defaultPrevented||t(n)}}function rg({page:e,...t}){let{router:n}=dp(),r=m.useMemo(()=>ep(n.routes,e,n.basename),[n.routes,e,n.basename]);return r?m.createElement(ig,{page:e,matches:r,...t}):null}function og(e){let{manifest:t,routeModules:n}=fp(),[r,o]=m.useState([]);return m.useEffect(()=>{let i=!1;return Yy(e,t,n).then(l=>{i||o(l)}),()=>{i=!0}},[e,t,n]),r}function ig({page:e,matches:t,...n}){let r=Mn(),{manifest:o,routeModules:i}=fp(),{basename:l}=dp(),{loaderData:s,matches:u}=tg(),a=m.useMemo(()=>mc(e,t,u,o,r,"data"),[e,t,u,o,r]),f=m.useMemo(()=>mc(e,t,u,o,r,"assets"),[e,t,u,o,r]),p=m.useMemo(()=>{if(e===r.pathname+r.search+r.hash)return[];let w=new Set,y=!1;if(t.forEach(d=>{var h;let c=o.routes[d.route.id];!c||!c.hasLoader||(!a.some(S=>S.route.id===d.route.id)&&d.route.id in s&&((h=i[d.route.id])!=null&&h.shouldRevalidate)||c.hasClientLoader?y=!0:w.add(d.route.id))}),w.size===0)return[];let x=eg(e,l);return y&&w.size>0&&x.searchParams.set("_routes",t.filter(d=>w.has(d.route.id)).map(d=>d.route.id).join(",")),[x.pathname+x.search]},[l,s,r,o,a,t,e,i]),v=m.useMemo(()=>Xy(f,o),[f,o]),g=og(f);return m.createElement(m.Fragment,null,p.map(w=>m.createElement("link",{key:w,rel:"prefetch",as:"fetch",href:w,...n})),v.map(w=>m.createElement("link",{key:w,rel:"modulepreload",href:w,...n})),g.map(({key:w,link:y})=>m.createElement("link",{key:w,...y})))}function lg(...e){return t=>{e.forEach(n=>{typeof n=="function"?n(t):n!=null&&(n.current=t)})}}var pp=typeof window<"u"&&typeof window.document<"u"&&typeof window.document.createElement<"u";try{pp&&(window.__reactRouterVersion="7.5.0")}catch{}function sg({basename:e,children:t,window:n}){let r=m.useRef();r.current==null&&(r.current=Yv({window:n,v5Compat:!0}));let o=r.current,[i,l]=m.useState({action:o.action,location:o.location}),s=m.useCallback(u=>{m.startTransition(()=>l(u))},[l]);return m.useLayoutEffect(()=>o.listen(s),[o,s]),m.createElement(zy,{basename:e,children:t,location:i.location,navigationType:i.action,navigator:o})}var hp=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,mp=m.forwardRef(function({onClick:t,discover:n="render",prefetch:r="none",relative:o,reloadDocument:i,replace:l,state:s,target:u,to:a,preventScrollReset:f,viewTransition:p,...v},g){let{basename:w}=m.useContext(pt),y=typeof a=="string"&&hp.test(a),x,d=!1;if(typeof a=="string"&&y&&(x=a,pp))try{let L=new URL(window.location.href),M=a.startsWith("//")?new URL(L.protocol+a):new URL(a),H=Ot(M.pathname,w);M.origin===L.origin&&H!=null?a=H+M.search+M.hash:d=!0}catch{ft(!1,`<Link to="${a}"> contains an invalid URL which will probably break when clicked - please update to a valid URL path.`)}let c=Sy(a,{relative:o}),[h,S,C]=ng(r,v),T=dg(a,{replace:l,state:s,target:u,preventScrollReset:f,relative:o,viewTransition:p});function k(L){t&&t(L),L.defaultPrevented||T(L)}let _=m.createElement("a",{...v,...C,href:x||c,onClick:d||i?t:k,ref:lg(g,S),target:u,"data-discover":!y&&n==="render"?"true":void 0});return h&&!y?m.createElement(m.Fragment,null,_,m.createElement(rg,{page:c})):_});mp.displayName="Link";var ug=m.forwardRef(function({"aria-current":t="page",caseSensitive:n=!1,className:r="",end:o=!1,style:i,to:l,viewTransition:s,children:u,...a},f){let p=Co(l,{relative:a.relative}),v=Mn(),g=m.useContext(Ji),{navigator:w,basename:y}=m.useContext(pt),x=g!=null&&vg(p)&&s===!0,d=w.encodeLocation?w.encodeLocation(p).pathname:p.pathname,c=v.pathname,h=g&&g.navigation&&g.navigation.location?g.navigation.location.pathname:null;n||(c=c.toLowerCase(),h=h?h.toLowerCase():null,d=d.toLowerCase()),h&&y&&(h=Ot(h,y)||h);const S=d!=="/"&&d.endsWith("/")?d.length-1:d.length;let C=c===d||!o&&c.startsWith(d)&&c.charAt(S)==="/",T=h!=null&&(h===d||!o&&h.startsWith(d)&&h.charAt(d.length)==="/"),k={isActive:C,isPending:T,isTransitioning:x},_=C?t:void 0,L;typeof r=="function"?L=r(k):L=[r,C?"active":null,T?"pending":null,x?"transitioning":null].filter(Boolean).join(" ");let M=typeof i=="function"?i(k):i;return m.createElement(mp,{...a,"aria-current":_,className:L,ref:f,style:M,to:l,viewTransition:s},typeof u=="function"?u(k):u)});ug.displayName="NavLink";var ag=m.forwardRef(({discover:e="render",fetcherKey:t,navigate:n,reloadDocument:r,replace:o,state:i,method:l=ii,action:s,onSubmit:u,relative:a,preventScrollReset:f,viewTransition:p,...v},g)=>{let w=hg(),y=mg(s,{relative:a}),x=l.toLowerCase()==="get"?"get":"post",d=typeof s=="string"&&hp.test(s),c=h=>{if(u&&u(h),h.defaultPrevented)return;h.preventDefault();let S=h.nativeEvent.submitter,C=(S==null?void 0:S.getAttribute("formmethod"))||l;w(S||h.currentTarget,{fetcherKey:t,method:C,navigate:n,replace:o,state:i,relative:a,preventScrollReset:f,viewTransition:p})};return m.createElement("form",{ref:g,method:x,action:y,onSubmit:r?u:c,...v,"data-discover":!d&&e==="render"?"true":void 0})});ag.displayName="Form";function cg(e){return`${e} must be used within a data router.  See https://reactrouter.com/en/main/routers/picking-a-router.`}function vp(e){let t=m.useContext(wr);return Z(t,cg(e)),t}function dg(e,{target:t,replace:n,state:r,preventScrollReset:o,relative:i,viewTransition:l}={}){let s=Ey(),u=Mn(),a=Co(e,{relative:i});return m.useCallback(f=>{if(By(f,t)){f.preventDefault();let p=n!==void 0?n:co(u)===co(a);s(e,{replace:p,state:r,preventScrollReset:o,relative:i,viewTransition:l})}},[u,s,a,n,r,t,e,o,i,l])}var fg=0,pg=()=>`__${String(++fg)}__`;function hg(){let{router:e}=vp("useSubmit"),{basename:t}=m.useContext(pt),n=Iy();return m.useCallback(async(r,o={})=>{let{action:i,method:l,encType:s,formData:u,body:a}=Qy(r,t);if(o.navigate===!1){let f=o.fetcherKey||pg();await e.fetch(f,n,o.action||i,{preventScrollReset:o.preventScrollReset,formData:u,body:a,formMethod:o.method||l,formEncType:o.encType||s,flushSync:o.flushSync})}else await e.navigate(o.action||i,{preventScrollReset:o.preventScrollReset,formData:u,body:a,formMethod:o.method||l,formEncType:o.encType||s,replace:o.replace,state:o.state,fromRouteId:n,flushSync:o.flushSync,viewTransition:o.viewTransition})},[e,t,n])}function mg(e,{relative:t}={}){let{basename:n}=m.useContext(pt),r=m.useContext(ht);Z(r,"useFormAction must be used inside a RouteContext");let[o]=r.matches.slice(-1),i={...Co(e||".",{relative:t})},l=Mn();if(e==null){i.search=l.search;let s=new URLSearchParams(i.search),u=s.getAll("index");if(u.some(f=>f==="")){s.delete("index"),u.filter(p=>p).forEach(p=>s.append("index",p));let f=s.toString();i.search=f?`?${f}`:""}}return(!e||e===".")&&o.route.index&&(i.search=i.search?i.search.replace(/^\?/,"?index&"):"?index"),n!=="/"&&(i.pathname=i.pathname==="/"?n:Tt([n,i.pathname])),co(i)}function vg(e,t={}){let n=m.useContext(lp);Z(n!=null,"`useViewTransitionState` must be used within `react-router-dom`'s `RouterProvider`.  Did you accidentally import `RouterProvider` from `react-router`?");let{basename:r}=vp("useViewTransitionState"),o=Co(e,{relative:t.relative});if(!n.isTransitioning)return!1;let i=Ot(n.currentLocation.pathname,r)||n.currentLocation.pathname,l=Ot(n.nextLocation.pathname,r)||n.nextLocation.pathname;return Ii(o.pathname,l)!=null||Ii(o.pathname,i)!=null}new TextEncoder;const yg=1,gg=1e6;let Dl=0;function wg(){return Dl=(Dl+1)%Number.MAX_SAFE_INTEGER,Dl.toString()}const Fl=new Map,vc=e=>{if(Fl.has(e))return;const t=setTimeout(()=>{Fl.delete(e),Qr({type:"REMOVE_TOAST",toastId:e})},gg);Fl.set(e,t)},xg=(e,t)=>{switch(t.type){case"ADD_TOAST":return{...e,toasts:[t.toast,...e.toasts].slice(0,yg)};case"UPDATE_TOAST":return{...e,toasts:e.toasts.map(n=>n.id===t.toast.id?{...n,...t.toast}:n)};case"DISMISS_TOAST":{const{toastId:n}=t;return n?vc(n):e.toasts.forEach(r=>{vc(r.id)}),{...e,toasts:e.toasts.map(r=>r.id===n||n===void 0?{...r,open:!1}:r)}}case"REMOVE_TOAST":return t.toastId===void 0?{...e,toasts:[]}:{...e,toasts:e.toasts.filter(n=>n.id!==t.toastId)}}},si=[];let ui={toasts:[]};function Qr(e){ui=xg(ui,e),si.forEach(t=>{t(ui)})}function Sg({...e}){const t=wg(),n=o=>Qr({type:"UPDATE_TOAST",toast:{...o,id:t}}),r=()=>Qr({type:"DISMISS_TOAST",toastId:t});return Qr({type:"ADD_TOAST",toast:{...e,id:t,open:!0,onOpenChange:o=>{o||r()}}}),{id:t,dismiss:r,update:n}}function Eg(){const[e,t]=m.useState(ui);return m.useEffect(()=>(si.push(t),()=>{const n=si.indexOf(t);n>-1&&si.splice(n,1)}),[e]),{...e,toast:Sg,dismiss:n=>Qr({type:"DISMISS_TOAST",toastId:n})}}function Ie(e,t,{checkForDefaultPrevented:n=!0}={}){return function(o){if(e==null||e(o),n===!1||!o.defaultPrevented)return t==null?void 0:t(o)}}function Cg(e,t){typeof e=="function"?e(t):e!=null&&(e.current=t)}function Wu(...e){return t=>e.forEach(n=>Cg(n,t))}function Nn(...e){return m.useCallback(Wu(...e),e)}function kg(e,t=[]){let n=[];function r(i,l){const s=m.createContext(l),u=n.length;n=[...n,l];function a(p){const{scope:v,children:g,...w}=p,y=(v==null?void 0:v[e][u])||s,x=m.useMemo(()=>w,Object.values(w));return R.jsx(y.Provider,{value:x,children:g})}function f(p,v){const g=(v==null?void 0:v[e][u])||s,w=m.useContext(g);if(w)return w;if(l!==void 0)return l;throw new Error(`\`${p}\` must be used within \`${i}\``)}return a.displayName=i+"Provider",[a,f]}const o=()=>{const i=n.map(l=>m.createContext(l));return function(s){const u=(s==null?void 0:s[e])||i;return m.useMemo(()=>({[`__scope${e}`]:{...s,[e]:u}}),[s,u])}};return o.scopeName=e,[r,Pg(o,...t)]}function Pg(...e){const t=e[0];if(e.length===1)return t;const n=()=>{const r=e.map(o=>({useScope:o(),scopeName:o.scopeName}));return function(i){const l=r.reduce((s,{useScope:u,scopeName:a})=>{const p=u(i)[`__scope${a}`];return{...s,...p}},{});return m.useMemo(()=>({[`__scope${t.scopeName}`]:l}),[l])}};return n.scopeName=t.scopeName,n}var Ds=m.forwardRef((e,t)=>{const{children:n,...r}=e,o=m.Children.toArray(n),i=o.find(Rg);if(i){const l=i.props.children,s=o.map(u=>u===i?m.Children.count(l)>1?m.Children.only(null):m.isValidElement(l)?l.props.children:null:u);return R.jsx(Fs,{...r,ref:t,children:m.isValidElement(l)?m.cloneElement(l,void 0,s):null})}return R.jsx(Fs,{...r,ref:t,children:n})});Ds.displayName="Slot";var Fs=m.forwardRef((e,t)=>{const{children:n,...r}=e;if(m.isValidElement(n)){const o=Ng(n);return m.cloneElement(n,{..._g(r,n.props),ref:t?Wu(t,o):o})}return m.Children.count(n)>1?m.Children.only(null):null});Fs.displayName="SlotClone";var Tg=({children:e})=>R.jsx(R.Fragment,{children:e});function Rg(e){return m.isValidElement(e)&&e.type===Tg}function _g(e,t){const n={...t};for(const r in t){const o=e[r],i=t[r];/^on[A-Z]/.test(r)?o&&i?n[r]=(...s)=>{i(...s),o(...s)}:o&&(n[r]=o):r==="style"?n[r]={...o,...i}:r==="className"&&(n[r]=[o,i].filter(Boolean).join(" "))}return{...e,...n}}function Ng(e){var r,o;let t=(r=Object.getOwnPropertyDescriptor(e.props,"ref"))==null?void 0:r.get,n=t&&"isReactWarning"in t&&t.isReactWarning;return n?e.ref:(t=(o=Object.getOwnPropertyDescriptor(e,"ref"))==null?void 0:o.get,n=t&&"isReactWarning"in t&&t.isReactWarning,n?e.props.ref:e.props.ref||e.ref)}function Lg(e){const t=e+"CollectionProvider",[n,r]=kg(t),[o,i]=n(t,{collectionRef:{current:null},itemMap:new Map}),l=g=>{const{scope:w,children:y}=g,x=zt.useRef(null),d=zt.useRef(new Map).current;return R.jsx(o,{scope:w,itemMap:d,collectionRef:x,children:y})};l.displayName=t;const s=e+"CollectionSlot",u=zt.forwardRef((g,w)=>{const{scope:y,children:x}=g,d=i(s,y),c=Nn(w,d.collectionRef);return R.jsx(Ds,{ref:c,children:x})});u.displayName=s;const a=e+"CollectionItemSlot",f="data-radix-collection-item",p=zt.forwardRef((g,w)=>{const{scope:y,children:x,...d}=g,c=zt.useRef(null),h=Nn(w,c),S=i(a,y);return zt.useEffect(()=>(S.itemMap.set(c,{ref:c,...d}),()=>void S.itemMap.delete(c))),R.jsx(Ds,{[f]:"",ref:h,children:x})});p.displayName=a;function v(g){const w=i(e+"CollectionConsumer",g);return zt.useCallback(()=>{const x=w.collectionRef.current;if(!x)return[];const d=Array.from(x.querySelectorAll(`[${f}]`));return Array.from(w.itemMap.values()).sort((S,C)=>d.indexOf(S.ref.current)-d.indexOf(C.ref.current))},[w.collectionRef,w.itemMap])}return[{Provider:l,Slot:u,ItemSlot:p},v,r]}function Og(e,t=[]){let n=[];function r(i,l){const s=m.createContext(l),u=n.length;n=[...n,l];const a=p=>{var d;const{scope:v,children:g,...w}=p,y=((d=v==null?void 0:v[e])==null?void 0:d[u])||s,x=m.useMemo(()=>w,Object.values(w));return R.jsx(y.Provider,{value:x,children:g})};a.displayName=i+"Provider";function f(p,v){var y;const g=((y=v==null?void 0:v[e])==null?void 0:y[u])||s,w=m.useContext(g);if(w)return w;if(l!==void 0)return l;throw new Error(`\`${p}\` must be used within \`${i}\``)}return[a,f]}const o=()=>{const i=n.map(l=>m.createContext(l));return function(s){const u=(s==null?void 0:s[e])||i;return m.useMemo(()=>({[`__scope${e}`]:{...s,[e]:u}}),[s,u])}};return o.scopeName=e,[r,Mg(o,...t)]}function Mg(...e){const t=e[0];if(e.length===1)return t;const n=()=>{const r=e.map(o=>({useScope:o(),scopeName:o.scopeName}));return function(i){const l=r.reduce((s,{useScope:u,scopeName:a})=>{const p=u(i)[`__scope${a}`];return{...s,...p}},{});return m.useMemo(()=>({[`__scope${t.scopeName}`]:l}),[l])}};return n.scopeName=t.scopeName,n}var yp=m.forwardRef((e,t)=>{const{children:n,...r}=e,o=m.Children.toArray(n),i=o.find(Ag);if(i){const l=i.props.children,s=o.map(u=>u===i?m.Children.count(l)>1?m.Children.only(null):m.isValidElement(l)?l.props.children:null:u);return R.jsx(zs,{...r,ref:t,children:m.isValidElement(l)?m.cloneElement(l,void 0,s):null})}return R.jsx(zs,{...r,ref:t,children:n})});yp.displayName="Slot";var zs=m.forwardRef((e,t)=>{const{children:n,...r}=e;if(m.isValidElement(n)){const o=Fg(n);return m.cloneElement(n,{...Dg(r,n.props),ref:t?Wu(t,o):o})}return m.Children.count(n)>1?m.Children.only(null):null});zs.displayName="SlotClone";var Ig=({children:e})=>R.jsx(R.Fragment,{children:e});function Ag(e){return m.isValidElement(e)&&e.type===Ig}function Dg(e,t){const n={...t};for(const r in t){const o=e[r],i=t[r];/^on[A-Z]/.test(r)?o&&i?n[r]=(...s)=>{i(...s),o(...s)}:o&&(n[r]=o):r==="style"?n[r]={...o,...i}:r==="className"&&(n[r]=[o,i].filter(Boolean).join(" "))}return{...e,...n}}function Fg(e){var r,o;let t=(r=Object.getOwnPropertyDescriptor(e.props,"ref"))==null?void 0:r.get,n=t&&"isReactWarning"in t&&t.isReactWarning;return n?e.ref:(t=(o=Object.getOwnPropertyDescriptor(e,"ref"))==null?void 0:o.get,n=t&&"isReactWarning"in t&&t.isReactWarning,n?e.props.ref:e.props.ref||e.ref)}var zg=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","span","svg","ul"],mt=zg.reduce((e,t)=>{const n=m.forwardRef((r,o)=>{const{asChild:i,...l}=r,s=i?yp:t;return typeof window<"u"&&(window[Symbol.for("radix-ui")]=!0),R.jsx(s,{...l,ref:o})});return n.displayName=`Primitive.${t}`,{...e,[t]:n}},{});function gp(e,t){e&&Zi.flushSync(()=>e.dispatchEvent(t))}function Mt(e){const t=m.useRef(e);return m.useEffect(()=>{t.current=e}),m.useMemo(()=>(...n)=>{var r;return(r=t.current)==null?void 0:r.call(t,...n)},[])}function jg(e,t=globalThis==null?void 0:globalThis.document){const n=Mt(e);m.useEffect(()=>{const r=o=>{o.key==="Escape"&&n(o)};return t.addEventListener("keydown",r,{capture:!0}),()=>t.removeEventListener("keydown",r,{capture:!0})},[n,t])}var $g="DismissableLayer",js="dismissableLayer.update",Ug="dismissableLayer.pointerDownOutside",bg="dismissableLayer.focusOutside",yc,wp=m.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),xp=m.forwardRef((e,t)=>{const{disableOutsidePointerEvents:n=!1,onEscapeKeyDown:r,onPointerDownOutside:o,onFocusOutside:i,onInteractOutside:l,onDismiss:s,...u}=e,a=m.useContext(wp),[f,p]=m.useState(null),v=(f==null?void 0:f.ownerDocument)??(globalThis==null?void 0:globalThis.document),[,g]=m.useState({}),w=Nn(t,k=>p(k)),y=Array.from(a.layers),[x]=[...a.layersWithOutsidePointerEventsDisabled].slice(-1),d=y.indexOf(x),c=f?y.indexOf(f):-1,h=a.layersWithOutsidePointerEventsDisabled.size>0,S=c>=d,C=Bg(k=>{const _=k.target,L=[...a.branches].some(M=>M.contains(_));!S||L||(o==null||o(k),l==null||l(k),k.defaultPrevented||s==null||s())},v),T=Wg(k=>{const _=k.target;[...a.branches].some(M=>M.contains(_))||(i==null||i(k),l==null||l(k),k.defaultPrevented||s==null||s())},v);return jg(k=>{c===a.layers.size-1&&(r==null||r(k),!k.defaultPrevented&&s&&(k.preventDefault(),s()))},v),m.useEffect(()=>{if(f)return n&&(a.layersWithOutsidePointerEventsDisabled.size===0&&(yc=v.body.style.pointerEvents,v.body.style.pointerEvents="none"),a.layersWithOutsidePointerEventsDisabled.add(f)),a.layers.add(f),gc(),()=>{n&&a.layersWithOutsidePointerEventsDisabled.size===1&&(v.body.style.pointerEvents=yc)}},[f,v,n,a]),m.useEffect(()=>()=>{f&&(a.layers.delete(f),a.layersWithOutsidePointerEventsDisabled.delete(f),gc())},[f,a]),m.useEffect(()=>{const k=()=>g({});return document.addEventListener(js,k),()=>document.removeEventListener(js,k)},[]),R.jsx(mt.div,{...u,ref:w,style:{pointerEvents:h?S?"auto":"none":void 0,...e.style},onFocusCapture:Ie(e.onFocusCapture,T.onFocusCapture),onBlurCapture:Ie(e.onBlurCapture,T.onBlurCapture),onPointerDownCapture:Ie(e.onPointerDownCapture,C.onPointerDownCapture)})});xp.displayName=$g;var Vg="DismissableLayerBranch",Sp=m.forwardRef((e,t)=>{const n=m.useContext(wp),r=m.useRef(null),o=Nn(t,r);return m.useEffect(()=>{const i=r.current;if(i)return n.branches.add(i),()=>{n.branches.delete(i)}},[n.branches]),R.jsx(mt.div,{...e,ref:o})});Sp.displayName=Vg;function Bg(e,t=globalThis==null?void 0:globalThis.document){const n=Mt(e),r=m.useRef(!1),o=m.useRef(()=>{});return m.useEffect(()=>{const i=s=>{if(s.target&&!r.current){let u=function(){Ep(Ug,n,a,{discrete:!0})};const a={originalEvent:s};s.pointerType==="touch"?(t.removeEventListener("click",o.current),o.current=u,t.addEventListener("click",o.current,{once:!0})):u()}else t.removeEventListener("click",o.current);r.current=!1},l=window.setTimeout(()=>{t.addEventListener("pointerdown",i)},0);return()=>{window.clearTimeout(l),t.removeEventListener("pointerdown",i),t.removeEventListener("click",o.current)}},[t,n]),{onPointerDownCapture:()=>r.current=!0}}function Wg(e,t=globalThis==null?void 0:globalThis.document){const n=Mt(e),r=m.useRef(!1);return m.useEffect(()=>{const o=i=>{i.target&&!r.current&&Ep(bg,n,{originalEvent:i},{discrete:!1})};return t.addEventListener("focusin",o),()=>t.removeEventListener("focusin",o)},[t,n]),{onFocusCapture:()=>r.current=!0,onBlurCapture:()=>r.current=!1}}function gc(){const e=new CustomEvent(js);document.dispatchEvent(e)}function Ep(e,t,n,{discrete:r}){const o=n.originalEvent.target,i=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});t&&o.addEventListener(e,t,{once:!0}),r?gp(o,i):o.dispatchEvent(i)}var Hg=xp,Qg=Sp,Ai=globalThis!=null&&globalThis.document?m.useLayoutEffect:()=>{},Kg="Portal",Cp=m.forwardRef((e,t)=>{var s;const{container:n,...r}=e,[o,i]=m.useState(!1);Ai(()=>i(!0),[]);const l=n||o&&((s=globalThis==null?void 0:globalThis.document)==null?void 0:s.body);return l?Fv.createPortal(R.jsx(mt.div,{...r,ref:t}),l):null});Cp.displayName=Kg;function Gg(e,t){return m.useReducer((n,r)=>t[n][r]??n,e)}var kp=e=>{const{present:t,children:n}=e,r=Yg(t),o=typeof n=="function"?n({present:r.isPresent}):m.Children.only(n),i=Nn(r.ref,Xg(o));return typeof n=="function"||r.isPresent?m.cloneElement(o,{ref:i}):null};kp.displayName="Presence";function Yg(e){const[t,n]=m.useState(),r=m.useRef({}),o=m.useRef(e),i=m.useRef("none"),l=e?"mounted":"unmounted",[s,u]=Gg(l,{mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}});return m.useEffect(()=>{const a=Wo(r.current);i.current=s==="mounted"?a:"none"},[s]),Ai(()=>{const a=r.current,f=o.current;if(f!==e){const v=i.current,g=Wo(a);e?u("MOUNT"):g==="none"||(a==null?void 0:a.display)==="none"?u("UNMOUNT"):u(f&&v!==g?"ANIMATION_OUT":"UNMOUNT"),o.current=e}},[e,u]),Ai(()=>{if(t){let a;const f=t.ownerDocument.defaultView??window,p=g=>{const y=Wo(r.current).includes(g.animationName);if(g.target===t&&y&&(u("ANIMATION_END"),!o.current)){const x=t.style.animationFillMode;t.style.animationFillMode="forwards",a=f.setTimeout(()=>{t.style.animationFillMode==="forwards"&&(t.style.animationFillMode=x)})}},v=g=>{g.target===t&&(i.current=Wo(r.current))};return t.addEventListener("animationstart",v),t.addEventListener("animationcancel",p),t.addEventListener("animationend",p),()=>{f.clearTimeout(a),t.removeEventListener("animationstart",v),t.removeEventListener("animationcancel",p),t.removeEventListener("animationend",p)}}else u("ANIMATION_END")},[t,u]),{isPresent:["mounted","unmountSuspended"].includes(s),ref:m.useCallback(a=>{a&&(r.current=getComputedStyle(a)),n(a)},[])}}function Wo(e){return(e==null?void 0:e.animationName)||"none"}function Xg(e){var r,o;let t=(r=Object.getOwnPropertyDescriptor(e.props,"ref"))==null?void 0:r.get,n=t&&"isReactWarning"in t&&t.isReactWarning;return n?e.ref:(t=(o=Object.getOwnPropertyDescriptor(e,"ref"))==null?void 0:o.get,n=t&&"isReactWarning"in t&&t.isReactWarning,n?e.props.ref:e.props.ref||e.ref)}function qg({prop:e,defaultProp:t,onChange:n=()=>{}}){const[r,o]=Zg({defaultProp:t,onChange:n}),i=e!==void 0,l=i?e:r,s=Mt(n),u=m.useCallback(a=>{if(i){const p=typeof a=="function"?a(e):a;p!==e&&s(p)}else o(a)},[i,e,o,s]);return[l,u]}function Zg({defaultProp:e,onChange:t}){const n=m.useState(e),[r]=n,o=m.useRef(r),i=Mt(t);return m.useEffect(()=>{o.current!==r&&(i(r),o.current=r)},[r,o,i]),n}var Jg="VisuallyHidden",Hu=m.forwardRef((e,t)=>R.jsx(mt.span,{...e,ref:t,style:{position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal",...e.style}}));Hu.displayName=Jg;var Qu="ToastProvider",[Ku,e0,t0]=Lg("Toast"),[Pp,Xw]=Og("Toast",[t0]),[n0,tl]=Pp(Qu),Tp=e=>{const{__scopeToast:t,label:n="Notification",duration:r=5e3,swipeDirection:o="right",swipeThreshold:i=50,children:l}=e,[s,u]=m.useState(null),[a,f]=m.useState(0),p=m.useRef(!1),v=m.useRef(!1);return n.trim()||console.error(`Invalid prop \`label\` supplied to \`${Qu}\`. Expected non-empty \`string\`.`),R.jsx(Ku.Provider,{scope:t,children:R.jsx(n0,{scope:t,label:n,duration:r,swipeDirection:o,swipeThreshold:i,toastCount:a,viewport:s,onViewportChange:u,onToastAdd:m.useCallback(()=>f(g=>g+1),[]),onToastRemove:m.useCallback(()=>f(g=>g-1),[]),isFocusedToastEscapeKeyDownRef:p,isClosePausedRef:v,children:l})})};Tp.displayName=Qu;var Rp="ToastViewport",r0=["F8"],$s="toast.viewportPause",Us="toast.viewportResume",_p=m.forwardRef((e,t)=>{const{__scopeToast:n,hotkey:r=r0,label:o="Notifications ({hotkey})",...i}=e,l=tl(Rp,n),s=e0(n),u=m.useRef(null),a=m.useRef(null),f=m.useRef(null),p=m.useRef(null),v=Nn(t,p,l.onViewportChange),g=r.join("+").replace(/Key/g,"").replace(/Digit/g,""),w=l.toastCount>0;m.useEffect(()=>{const x=d=>{var h;r.length!==0&&r.every(S=>d[S]||d.code===S)&&((h=p.current)==null||h.focus())};return document.addEventListener("keydown",x),()=>document.removeEventListener("keydown",x)},[r]),m.useEffect(()=>{const x=u.current,d=p.current;if(w&&x&&d){const c=()=>{if(!l.isClosePausedRef.current){const T=new CustomEvent($s);d.dispatchEvent(T),l.isClosePausedRef.current=!0}},h=()=>{if(l.isClosePausedRef.current){const T=new CustomEvent(Us);d.dispatchEvent(T),l.isClosePausedRef.current=!1}},S=T=>{!x.contains(T.relatedTarget)&&h()},C=()=>{x.contains(document.activeElement)||h()};return x.addEventListener("focusin",c),x.addEventListener("focusout",S),x.addEventListener("pointermove",c),x.addEventListener("pointerleave",C),window.addEventListener("blur",c),window.addEventListener("focus",h),()=>{x.removeEventListener("focusin",c),x.removeEventListener("focusout",S),x.removeEventListener("pointermove",c),x.removeEventListener("pointerleave",C),window.removeEventListener("blur",c),window.removeEventListener("focus",h)}}},[w,l.isClosePausedRef]);const y=m.useCallback(({tabbingDirection:x})=>{const c=s().map(h=>{const S=h.ref.current,C=[S,...v0(S)];return x==="forwards"?C:C.reverse()});return(x==="forwards"?c.reverse():c).flat()},[s]);return m.useEffect(()=>{const x=p.current;if(x){const d=c=>{var C,T,k;const h=c.altKey||c.ctrlKey||c.metaKey;if(c.key==="Tab"&&!h){const _=document.activeElement,L=c.shiftKey;if(c.target===x&&L){(C=a.current)==null||C.focus();return}const j=y({tabbingDirection:L?"backwards":"forwards"}),Oe=j.findIndex(A=>A===_);zl(j.slice(Oe+1))?c.preventDefault():L?(T=a.current)==null||T.focus():(k=f.current)==null||k.focus()}};return x.addEventListener("keydown",d),()=>x.removeEventListener("keydown",d)}},[s,y]),R.jsxs(Qg,{ref:u,role:"region","aria-label":o.replace("{hotkey}",g),tabIndex:-1,style:{pointerEvents:w?void 0:"none"},children:[w&&R.jsx(bs,{ref:a,onFocusFromOutsideViewport:()=>{const x=y({tabbingDirection:"forwards"});zl(x)}}),R.jsx(Ku.Slot,{scope:n,children:R.jsx(mt.ol,{tabIndex:-1,...i,ref:v})}),w&&R.jsx(bs,{ref:f,onFocusFromOutsideViewport:()=>{const x=y({tabbingDirection:"backwards"});zl(x)}})]})});_p.displayName=Rp;var Np="ToastFocusProxy",bs=m.forwardRef((e,t)=>{const{__scopeToast:n,onFocusFromOutsideViewport:r,...o}=e,i=tl(Np,n);return R.jsx(Hu,{"aria-hidden":!0,tabIndex:0,...o,ref:t,style:{position:"fixed"},onFocus:l=>{var a;const s=l.relatedTarget;!((a=i.viewport)!=null&&a.contains(s))&&r()}})});bs.displayName=Np;var nl="Toast",o0="toast.swipeStart",i0="toast.swipeMove",l0="toast.swipeCancel",s0="toast.swipeEnd",Lp=m.forwardRef((e,t)=>{const{forceMount:n,open:r,defaultOpen:o,onOpenChange:i,...l}=e,[s=!0,u]=qg({prop:r,defaultProp:o,onChange:i});return R.jsx(kp,{present:n||s,children:R.jsx(c0,{open:s,...l,ref:t,onClose:()=>u(!1),onPause:Mt(e.onPause),onResume:Mt(e.onResume),onSwipeStart:Ie(e.onSwipeStart,a=>{a.currentTarget.setAttribute("data-swipe","start")}),onSwipeMove:Ie(e.onSwipeMove,a=>{const{x:f,y:p}=a.detail.delta;a.currentTarget.setAttribute("data-swipe","move"),a.currentTarget.style.setProperty("--radix-toast-swipe-move-x",`${f}px`),a.currentTarget.style.setProperty("--radix-toast-swipe-move-y",`${p}px`)}),onSwipeCancel:Ie(e.onSwipeCancel,a=>{a.currentTarget.setAttribute("data-swipe","cancel"),a.currentTarget.style.removeProperty("--radix-toast-swipe-move-x"),a.currentTarget.style.removeProperty("--radix-toast-swipe-move-y"),a.currentTarget.style.removeProperty("--radix-toast-swipe-end-x"),a.currentTarget.style.removeProperty("--radix-toast-swipe-end-y")}),onSwipeEnd:Ie(e.onSwipeEnd,a=>{const{x:f,y:p}=a.detail.delta;a.currentTarget.setAttribute("data-swipe","end"),a.currentTarget.style.removeProperty("--radix-toast-swipe-move-x"),a.currentTarget.style.removeProperty("--radix-toast-swipe-move-y"),a.currentTarget.style.setProperty("--radix-toast-swipe-end-x",`${f}px`),a.currentTarget.style.setProperty("--radix-toast-swipe-end-y",`${p}px`),u(!1)})})})});Lp.displayName=nl;var[u0,a0]=Pp(nl,{onClose(){}}),c0=m.forwardRef((e,t)=>{const{__scopeToast:n,type:r="foreground",duration:o,open:i,onClose:l,onEscapeKeyDown:s,onPause:u,onResume:a,onSwipeStart:f,onSwipeMove:p,onSwipeCancel:v,onSwipeEnd:g,...w}=e,y=tl(nl,n),[x,d]=m.useState(null),c=Nn(t,A=>d(A)),h=m.useRef(null),S=m.useRef(null),C=o||y.duration,T=m.useRef(0),k=m.useRef(C),_=m.useRef(0),{onToastAdd:L,onToastRemove:M}=y,H=Mt(()=>{var ae;(x==null?void 0:x.contains(document.activeElement))&&((ae=y.viewport)==null||ae.focus()),l()}),j=m.useCallback(A=>{!A||A===1/0||(window.clearTimeout(_.current),T.current=new Date().getTime(),_.current=window.setTimeout(H,A))},[H]);m.useEffect(()=>{const A=y.viewport;if(A){const ae=()=>{j(k.current),a==null||a()},ce=()=>{const $e=new Date().getTime()-T.current;k.current=k.current-$e,window.clearTimeout(_.current),u==null||u()};return A.addEventListener($s,ce),A.addEventListener(Us,ae),()=>{A.removeEventListener($s,ce),A.removeEventListener(Us,ae)}}},[y.viewport,C,u,a,j]),m.useEffect(()=>{i&&!y.isClosePausedRef.current&&j(C)},[i,C,y.isClosePausedRef,j]),m.useEffect(()=>(L(),()=>M()),[L,M]);const Oe=m.useMemo(()=>x?zp(x):null,[x]);return y.viewport?R.jsxs(R.Fragment,{children:[Oe&&R.jsx(d0,{__scopeToast:n,role:"status","aria-live":r==="foreground"?"assertive":"polite","aria-atomic":!0,children:Oe}),R.jsx(u0,{scope:n,onClose:H,children:Zi.createPortal(R.jsx(Ku.ItemSlot,{scope:n,children:R.jsx(Hg,{asChild:!0,onEscapeKeyDown:Ie(s,()=>{y.isFocusedToastEscapeKeyDownRef.current||H(),y.isFocusedToastEscapeKeyDownRef.current=!1}),children:R.jsx(mt.li,{role:"status","aria-live":"off","aria-atomic":!0,tabIndex:0,"data-state":i?"open":"closed","data-swipe-direction":y.swipeDirection,...w,ref:c,style:{userSelect:"none",touchAction:"none",...e.style},onKeyDown:Ie(e.onKeyDown,A=>{A.key==="Escape"&&(s==null||s(A.nativeEvent),A.nativeEvent.defaultPrevented||(y.isFocusedToastEscapeKeyDownRef.current=!0,H()))}),onPointerDown:Ie(e.onPointerDown,A=>{A.button===0&&(h.current={x:A.clientX,y:A.clientY})}),onPointerMove:Ie(e.onPointerMove,A=>{if(!h.current)return;const ae=A.clientX-h.current.x,ce=A.clientY-h.current.y,$e=!!S.current,N=["left","right"].includes(y.swipeDirection),I=["left","up"].includes(y.swipeDirection)?Math.min:Math.max,D=N?I(0,ae):0,U=N?0:I(0,ce),ee=A.pointerType==="touch"?10:2,vt={x:D,y:U},rt={originalEvent:A,delta:vt};$e?(S.current=vt,Ho(i0,p,rt,{discrete:!1})):wc(vt,y.swipeDirection,ee)?(S.current=vt,Ho(o0,f,rt,{discrete:!1}),A.target.setPointerCapture(A.pointerId)):(Math.abs(ae)>ee||Math.abs(ce)>ee)&&(h.current=null)}),onPointerUp:Ie(e.onPointerUp,A=>{const ae=S.current,ce=A.target;if(ce.hasPointerCapture(A.pointerId)&&ce.releasePointerCapture(A.pointerId),S.current=null,h.current=null,ae){const $e=A.currentTarget,N={originalEvent:A,delta:ae};wc(ae,y.swipeDirection,y.swipeThreshold)?Ho(s0,g,N,{discrete:!0}):Ho(l0,v,N,{discrete:!0}),$e.addEventListener("click",I=>I.preventDefault(),{once:!0})}})})})}),y.viewport)})]}):null}),d0=e=>{const{__scopeToast:t,children:n,...r}=e,o=tl(nl,t),[i,l]=m.useState(!1),[s,u]=m.useState(!1);return h0(()=>l(!0)),m.useEffect(()=>{const a=window.setTimeout(()=>u(!0),1e3);return()=>window.clearTimeout(a)},[]),s?null:R.jsx(Cp,{asChild:!0,children:R.jsx(Hu,{...r,children:i&&R.jsxs(R.Fragment,{children:[o.label," ",n]})})})},f0="ToastTitle",Op=m.forwardRef((e,t)=>{const{__scopeToast:n,...r}=e;return R.jsx(mt.div,{...r,ref:t})});Op.displayName=f0;var p0="ToastDescription",Mp=m.forwardRef((e,t)=>{const{__scopeToast:n,...r}=e;return R.jsx(mt.div,{...r,ref:t})});Mp.displayName=p0;var Ip="ToastAction",Ap=m.forwardRef((e,t)=>{const{altText:n,...r}=e;return n.trim()?R.jsx(Fp,{altText:n,asChild:!0,children:R.jsx(Gu,{...r,ref:t})}):(console.error(`Invalid prop \`altText\` supplied to \`${Ip}\`. Expected non-empty \`string\`.`),null)});Ap.displayName=Ip;var Dp="ToastClose",Gu=m.forwardRef((e,t)=>{const{__scopeToast:n,...r}=e,o=a0(Dp,n);return R.jsx(Fp,{asChild:!0,children:R.jsx(mt.button,{type:"button",...r,ref:t,onClick:Ie(e.onClick,o.onClose)})})});Gu.displayName=Dp;var Fp=m.forwardRef((e,t)=>{const{__scopeToast:n,altText:r,...o}=e;return R.jsx(mt.div,{"data-radix-toast-announce-exclude":"","data-radix-toast-announce-alt":r||void 0,...o,ref:t})});function zp(e){const t=[];return Array.from(e.childNodes).forEach(r=>{if(r.nodeType===r.TEXT_NODE&&r.textContent&&t.push(r.textContent),m0(r)){const o=r.ariaHidden||r.hidden||r.style.display==="none",i=r.dataset.radixToastAnnounceExclude==="";if(!o)if(i){const l=r.dataset.radixToastAnnounceAlt;l&&t.push(l)}else t.push(...zp(r))}}),t}function Ho(e,t,n,{discrete:r}){const o=n.originalEvent.currentTarget,i=new CustomEvent(e,{bubbles:!0,cancelable:!0,detail:n});t&&o.addEventListener(e,t,{once:!0}),r?gp(o,i):o.dispatchEvent(i)}var wc=(e,t,n=0)=>{const r=Math.abs(e.x),o=Math.abs(e.y),i=r>o;return t==="left"||t==="right"?i&&r>n:!i&&o>n};function h0(e=()=>{}){const t=Mt(e);Ai(()=>{let n=0,r=0;return n=window.requestAnimationFrame(()=>r=window.requestAnimationFrame(t)),()=>{window.cancelAnimationFrame(n),window.cancelAnimationFrame(r)}},[t])}function m0(e){return e.nodeType===e.ELEMENT_NODE}function v0(e){const t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:r=>{const o=r.tagName==="INPUT"&&r.type==="hidden";return r.disabled||r.hidden||o?NodeFilter.FILTER_SKIP:r.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function zl(e){const t=document.activeElement;return e.some(n=>n===t?!0:(n.focus(),document.activeElement!==t))}var y0=Tp,jp=_p,$p=Lp,Up=Op,bp=Mp,Vp=Ap,Bp=Gu;function Wp(e){var t,n,r="";if(typeof e=="string"||typeof e=="number")r+=e;else if(typeof e=="object")if(Array.isArray(e))for(t=0;t<e.length;t++)e[t]&&(n=Wp(e[t]))&&(r&&(r+=" "),r+=n);else for(t in e)e[t]&&(r&&(r+=" "),r+=t);return r}function g0(){for(var e,t,n=0,r="";n<arguments.length;)(e=arguments[n++])&&(t=Wp(e))&&(r&&(r+=" "),r+=t);return r}const xc=e=>typeof e=="boolean"?"".concat(e):e===0?"0":e,Sc=g0,w0=(e,t)=>n=>{var r;if((t==null?void 0:t.variants)==null)return Sc(e,n==null?void 0:n.class,n==null?void 0:n.className);const{variants:o,defaultVariants:i}=t,l=Object.keys(o).map(a=>{const f=n==null?void 0:n[a],p=i==null?void 0:i[a];if(f===null)return null;const v=xc(f)||xc(p);return o[a][v]}),s=n&&Object.entries(n).reduce((a,f)=>{let[p,v]=f;return v===void 0||(a[p]=v),a},{}),u=t==null||(r=t.compoundVariants)===null||r===void 0?void 0:r.reduce((a,f)=>{let{class:p,className:v,...g}=f;return Object.entries(g).every(w=>{let[y,x]=w;return Array.isArray(x)?x.includes({...i,...s}[y]):{...i,...s}[y]===x})?[...a,p,v]:a},[]);return Sc(e,l,u,n==null?void 0:n.class,n==null?void 0:n.className)};/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const x0=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),Hp=(...e)=>e.filter((t,n,r)=>!!t&&r.indexOf(t)===n).join(" ");/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var S0={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const E0=m.forwardRef(({color:e="currentColor",size:t=24,strokeWidth:n=2,absoluteStrokeWidth:r,className:o="",children:i,iconNode:l,...s},u)=>m.createElement("svg",{ref:u,...S0,width:t,height:t,stroke:e,strokeWidth:r?Number(n)*24/Number(t):n,className:Hp("lucide",o),...s},[...l.map(([a,f])=>m.createElement(a,f)),...Array.isArray(i)?i:[i]]));/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const C0=(e,t)=>{const n=m.forwardRef(({className:r,...o},i)=>m.createElement(E0,{ref:i,iconNode:t,className:Hp(`lucide-${x0(e)}`,r),...o}));return n.displayName=`${e}`,n};/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const k0=C0("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]]);function Qp(e){var t,n,r="";if(typeof e=="string"||typeof e=="number")r+=e;else if(typeof e=="object")if(Array.isArray(e)){var o=e.length;for(t=0;t<o;t++)e[t]&&(n=Qp(e[t]))&&(r&&(r+=" "),r+=n)}else for(n in e)e[n]&&(r&&(r+=" "),r+=n);return r}function P0(){for(var e,t,n=0,r="",o=arguments.length;n<o;n++)(e=arguments[n])&&(t=Qp(e))&&(r&&(r+=" "),r+=t);return r}const Yu="-",T0=e=>{const t=_0(e),{conflictingClassGroups:n,conflictingClassGroupModifiers:r}=e;return{getClassGroupId:l=>{const s=l.split(Yu);return s[0]===""&&s.length!==1&&s.shift(),Kp(s,t)||R0(l)},getConflictingClassGroupIds:(l,s)=>{const u=n[l]||[];return s&&r[l]?[...u,...r[l]]:u}}},Kp=(e,t)=>{var l;if(e.length===0)return t.classGroupId;const n=e[0],r=t.nextPart.get(n),o=r?Kp(e.slice(1),r):void 0;if(o)return o;if(t.validators.length===0)return;const i=e.join(Yu);return(l=t.validators.find(({validator:s})=>s(i)))==null?void 0:l.classGroupId},Ec=/^\[(.+)\]$/,R0=e=>{if(Ec.test(e)){const t=Ec.exec(e)[1],n=t==null?void 0:t.substring(0,t.indexOf(":"));if(n)return"arbitrary.."+n}},_0=e=>{const{theme:t,prefix:n}=e,r={nextPart:new Map,validators:[]};return L0(Object.entries(e.classGroups),n).forEach(([i,l])=>{Vs(l,r,i,t)}),r},Vs=(e,t,n,r)=>{e.forEach(o=>{if(typeof o=="string"){const i=o===""?t:Cc(t,o);i.classGroupId=n;return}if(typeof o=="function"){if(N0(o)){Vs(o(r),t,n,r);return}t.validators.push({validator:o,classGroupId:n});return}Object.entries(o).forEach(([i,l])=>{Vs(l,Cc(t,i),n,r)})})},Cc=(e,t)=>{let n=e;return t.split(Yu).forEach(r=>{n.nextPart.has(r)||n.nextPart.set(r,{nextPart:new Map,validators:[]}),n=n.nextPart.get(r)}),n},N0=e=>e.isThemeGetter,L0=(e,t)=>t?e.map(([n,r])=>{const o=r.map(i=>typeof i=="string"?t+i:typeof i=="object"?Object.fromEntries(Object.entries(i).map(([l,s])=>[t+l,s])):i);return[n,o]}):e,O0=e=>{if(e<1)return{get:()=>{},set:()=>{}};let t=0,n=new Map,r=new Map;const o=(i,l)=>{n.set(i,l),t++,t>e&&(t=0,r=n,n=new Map)};return{get(i){let l=n.get(i);if(l!==void 0)return l;if((l=r.get(i))!==void 0)return o(i,l),l},set(i,l){n.has(i)?n.set(i,l):o(i,l)}}},Gp="!",M0=e=>{const{separator:t,experimentalParseClassName:n}=e,r=t.length===1,o=t[0],i=t.length,l=s=>{const u=[];let a=0,f=0,p;for(let x=0;x<s.length;x++){let d=s[x];if(a===0){if(d===o&&(r||s.slice(x,x+i)===t)){u.push(s.slice(f,x)),f=x+i;continue}if(d==="/"){p=x;continue}}d==="["?a++:d==="]"&&a--}const v=u.length===0?s:s.substring(f),g=v.startsWith(Gp),w=g?v.substring(1):v,y=p&&p>f?p-f:void 0;return{modifiers:u,hasImportantModifier:g,baseClassName:w,maybePostfixModifierPosition:y}};return n?s=>n({className:s,parseClassName:l}):l},I0=e=>{if(e.length<=1)return e;const t=[];let n=[];return e.forEach(r=>{r[0]==="["?(t.push(...n.sort(),r),n=[]):n.push(r)}),t.push(...n.sort()),t},A0=e=>({cache:O0(e.cacheSize),parseClassName:M0(e),...T0(e)}),D0=/\s+/,F0=(e,t)=>{const{parseClassName:n,getClassGroupId:r,getConflictingClassGroupIds:o}=t,i=[],l=e.trim().split(D0);let s="";for(let u=l.length-1;u>=0;u-=1){const a=l[u],{modifiers:f,hasImportantModifier:p,baseClassName:v,maybePostfixModifierPosition:g}=n(a);let w=!!g,y=r(w?v.substring(0,g):v);if(!y){if(!w){s=a+(s.length>0?" "+s:s);continue}if(y=r(v),!y){s=a+(s.length>0?" "+s:s);continue}w=!1}const x=I0(f).join(":"),d=p?x+Gp:x,c=d+y;if(i.includes(c))continue;i.push(c);const h=o(y,w);for(let S=0;S<h.length;++S){const C=h[S];i.push(d+C)}s=a+(s.length>0?" "+s:s)}return s};function z0(){let e=0,t,n,r="";for(;e<arguments.length;)(t=arguments[e++])&&(n=Yp(t))&&(r&&(r+=" "),r+=n);return r}const Yp=e=>{if(typeof e=="string")return e;let t,n="";for(let r=0;r<e.length;r++)e[r]&&(t=Yp(e[r]))&&(n&&(n+=" "),n+=t);return n};function j0(e,...t){let n,r,o,i=l;function l(u){const a=t.reduce((f,p)=>p(f),e());return n=A0(a),r=n.cache.get,o=n.cache.set,i=s,s(u)}function s(u){const a=r(u);if(a)return a;const f=F0(u,n);return o(u,f),f}return function(){return i(z0.apply(null,arguments))}}const Q=e=>{const t=n=>n[e]||[];return t.isThemeGetter=!0,t},Xp=/^\[(?:([a-z-]+):)?(.+)\]$/i,$0=/^\d+\/\d+$/,U0=new Set(["px","full","screen"]),b0=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,V0=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,B0=/^(rgba?|hsla?|hwb|(ok)?(lab|lch))\(.+\)$/,W0=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,H0=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,yt=e=>Zn(e)||U0.has(e)||$0.test(e),Dt=e=>xr(e,"length",J0),Zn=e=>!!e&&!Number.isNaN(Number(e)),jl=e=>xr(e,"number",Zn),Or=e=>!!e&&Number.isInteger(Number(e)),Q0=e=>e.endsWith("%")&&Zn(e.slice(0,-1)),z=e=>Xp.test(e),Ft=e=>b0.test(e),K0=new Set(["length","size","percentage"]),G0=e=>xr(e,K0,qp),Y0=e=>xr(e,"position",qp),X0=new Set(["image","url"]),q0=e=>xr(e,X0,tw),Z0=e=>xr(e,"",ew),Mr=()=>!0,xr=(e,t,n)=>{const r=Xp.exec(e);return r?r[1]?typeof t=="string"?r[1]===t:t.has(r[1]):n(r[2]):!1},J0=e=>V0.test(e)&&!B0.test(e),qp=()=>!1,ew=e=>W0.test(e),tw=e=>H0.test(e),nw=()=>{const e=Q("colors"),t=Q("spacing"),n=Q("blur"),r=Q("brightness"),o=Q("borderColor"),i=Q("borderRadius"),l=Q("borderSpacing"),s=Q("borderWidth"),u=Q("contrast"),a=Q("grayscale"),f=Q("hueRotate"),p=Q("invert"),v=Q("gap"),g=Q("gradientColorStops"),w=Q("gradientColorStopPositions"),y=Q("inset"),x=Q("margin"),d=Q("opacity"),c=Q("padding"),h=Q("saturate"),S=Q("scale"),C=Q("sepia"),T=Q("skew"),k=Q("space"),_=Q("translate"),L=()=>["auto","contain","none"],M=()=>["auto","hidden","clip","visible","scroll"],H=()=>["auto",z,t],j=()=>[z,t],Oe=()=>["",yt,Dt],A=()=>["auto",Zn,z],ae=()=>["bottom","center","left","left-bottom","left-top","right","right-bottom","right-top","top"],ce=()=>["solid","dashed","dotted","double","none"],$e=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],N=()=>["start","end","center","between","around","evenly","stretch"],I=()=>["","0",z],D=()=>["auto","avoid","all","avoid-page","page","left","right","column"],U=()=>[Zn,z];return{cacheSize:500,separator:":",theme:{colors:[Mr],spacing:[yt,Dt],blur:["none","",Ft,z],brightness:U(),borderColor:[e],borderRadius:["none","","full",Ft,z],borderSpacing:j(),borderWidth:Oe(),contrast:U(),grayscale:I(),hueRotate:U(),invert:I(),gap:j(),gradientColorStops:[e],gradientColorStopPositions:[Q0,Dt],inset:H(),margin:H(),opacity:U(),padding:j(),saturate:U(),scale:U(),sepia:I(),skew:U(),space:j(),translate:j()},classGroups:{aspect:[{aspect:["auto","square","video",z]}],container:["container"],columns:[{columns:[Ft]}],"break-after":[{"break-after":D()}],"break-before":[{"break-before":D()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:[...ae(),z]}],overflow:[{overflow:M()}],"overflow-x":[{"overflow-x":M()}],"overflow-y":[{"overflow-y":M()}],overscroll:[{overscroll:L()}],"overscroll-x":[{"overscroll-x":L()}],"overscroll-y":[{"overscroll-y":L()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:[y]}],"inset-x":[{"inset-x":[y]}],"inset-y":[{"inset-y":[y]}],start:[{start:[y]}],end:[{end:[y]}],top:[{top:[y]}],right:[{right:[y]}],bottom:[{bottom:[y]}],left:[{left:[y]}],visibility:["visible","invisible","collapse"],z:[{z:["auto",Or,z]}],basis:[{basis:H()}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["wrap","wrap-reverse","nowrap"]}],flex:[{flex:["1","auto","initial","none",z]}],grow:[{grow:I()}],shrink:[{shrink:I()}],order:[{order:["first","last","none",Or,z]}],"grid-cols":[{"grid-cols":[Mr]}],"col-start-end":[{col:["auto",{span:["full",Or,z]},z]}],"col-start":[{"col-start":A()}],"col-end":[{"col-end":A()}],"grid-rows":[{"grid-rows":[Mr]}],"row-start-end":[{row:["auto",{span:[Or,z]},z]}],"row-start":[{"row-start":A()}],"row-end":[{"row-end":A()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":["auto","min","max","fr",z]}],"auto-rows":[{"auto-rows":["auto","min","max","fr",z]}],gap:[{gap:[v]}],"gap-x":[{"gap-x":[v]}],"gap-y":[{"gap-y":[v]}],"justify-content":[{justify:["normal",...N()]}],"justify-items":[{"justify-items":["start","end","center","stretch"]}],"justify-self":[{"justify-self":["auto","start","end","center","stretch"]}],"align-content":[{content:["normal",...N(),"baseline"]}],"align-items":[{items:["start","end","center","baseline","stretch"]}],"align-self":[{self:["auto","start","end","center","stretch","baseline"]}],"place-content":[{"place-content":[...N(),"baseline"]}],"place-items":[{"place-items":["start","end","center","baseline","stretch"]}],"place-self":[{"place-self":["auto","start","end","center","stretch"]}],p:[{p:[c]}],px:[{px:[c]}],py:[{py:[c]}],ps:[{ps:[c]}],pe:[{pe:[c]}],pt:[{pt:[c]}],pr:[{pr:[c]}],pb:[{pb:[c]}],pl:[{pl:[c]}],m:[{m:[x]}],mx:[{mx:[x]}],my:[{my:[x]}],ms:[{ms:[x]}],me:[{me:[x]}],mt:[{mt:[x]}],mr:[{mr:[x]}],mb:[{mb:[x]}],ml:[{ml:[x]}],"space-x":[{"space-x":[k]}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":[k]}],"space-y-reverse":["space-y-reverse"],w:[{w:["auto","min","max","fit","svw","lvw","dvw",z,t]}],"min-w":[{"min-w":[z,t,"min","max","fit"]}],"max-w":[{"max-w":[z,t,"none","full","min","max","fit","prose",{screen:[Ft]},Ft]}],h:[{h:[z,t,"auto","min","max","fit","svh","lvh","dvh"]}],"min-h":[{"min-h":[z,t,"min","max","fit","svh","lvh","dvh"]}],"max-h":[{"max-h":[z,t,"min","max","fit","svh","lvh","dvh"]}],size:[{size:[z,t,"auto","min","max","fit"]}],"font-size":[{text:["base",Ft,Dt]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:["thin","extralight","light","normal","medium","semibold","bold","extrabold","black",jl]}],"font-family":[{font:[Mr]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractons"],tracking:[{tracking:["tighter","tight","normal","wide","wider","widest",z]}],"line-clamp":[{"line-clamp":["none",Zn,jl]}],leading:[{leading:["none","tight","snug","normal","relaxed","loose",yt,z]}],"list-image":[{"list-image":["none",z]}],"list-style-type":[{list:["none","disc","decimal",z]}],"list-style-position":[{list:["inside","outside"]}],"placeholder-color":[{placeholder:[e]}],"placeholder-opacity":[{"placeholder-opacity":[d]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"text-color":[{text:[e]}],"text-opacity":[{"text-opacity":[d]}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...ce(),"wavy"]}],"text-decoration-thickness":[{decoration:["auto","from-font",yt,Dt]}],"underline-offset":[{"underline-offset":["auto",yt,z]}],"text-decoration-color":[{decoration:[e]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:j()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",z]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",z]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-opacity":[{"bg-opacity":[d]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:[...ae(),Y0]}],"bg-repeat":[{bg:["no-repeat",{repeat:["","x","y","round","space"]}]}],"bg-size":[{bg:["auto","cover","contain",G0]}],"bg-image":[{bg:["none",{"gradient-to":["t","tr","r","br","b","bl","l","tl"]},q0]}],"bg-color":[{bg:[e]}],"gradient-from-pos":[{from:[w]}],"gradient-via-pos":[{via:[w]}],"gradient-to-pos":[{to:[w]}],"gradient-from":[{from:[g]}],"gradient-via":[{via:[g]}],"gradient-to":[{to:[g]}],rounded:[{rounded:[i]}],"rounded-s":[{"rounded-s":[i]}],"rounded-e":[{"rounded-e":[i]}],"rounded-t":[{"rounded-t":[i]}],"rounded-r":[{"rounded-r":[i]}],"rounded-b":[{"rounded-b":[i]}],"rounded-l":[{"rounded-l":[i]}],"rounded-ss":[{"rounded-ss":[i]}],"rounded-se":[{"rounded-se":[i]}],"rounded-ee":[{"rounded-ee":[i]}],"rounded-es":[{"rounded-es":[i]}],"rounded-tl":[{"rounded-tl":[i]}],"rounded-tr":[{"rounded-tr":[i]}],"rounded-br":[{"rounded-br":[i]}],"rounded-bl":[{"rounded-bl":[i]}],"border-w":[{border:[s]}],"border-w-x":[{"border-x":[s]}],"border-w-y":[{"border-y":[s]}],"border-w-s":[{"border-s":[s]}],"border-w-e":[{"border-e":[s]}],"border-w-t":[{"border-t":[s]}],"border-w-r":[{"border-r":[s]}],"border-w-b":[{"border-b":[s]}],"border-w-l":[{"border-l":[s]}],"border-opacity":[{"border-opacity":[d]}],"border-style":[{border:[...ce(),"hidden"]}],"divide-x":[{"divide-x":[s]}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":[s]}],"divide-y-reverse":["divide-y-reverse"],"divide-opacity":[{"divide-opacity":[d]}],"divide-style":[{divide:ce()}],"border-color":[{border:[o]}],"border-color-x":[{"border-x":[o]}],"border-color-y":[{"border-y":[o]}],"border-color-s":[{"border-s":[o]}],"border-color-e":[{"border-e":[o]}],"border-color-t":[{"border-t":[o]}],"border-color-r":[{"border-r":[o]}],"border-color-b":[{"border-b":[o]}],"border-color-l":[{"border-l":[o]}],"divide-color":[{divide:[o]}],"outline-style":[{outline:["",...ce()]}],"outline-offset":[{"outline-offset":[yt,z]}],"outline-w":[{outline:[yt,Dt]}],"outline-color":[{outline:[e]}],"ring-w":[{ring:Oe()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:[e]}],"ring-opacity":[{"ring-opacity":[d]}],"ring-offset-w":[{"ring-offset":[yt,Dt]}],"ring-offset-color":[{"ring-offset":[e]}],shadow:[{shadow:["","inner","none",Ft,Z0]}],"shadow-color":[{shadow:[Mr]}],opacity:[{opacity:[d]}],"mix-blend":[{"mix-blend":[...$e(),"plus-lighter","plus-darker"]}],"bg-blend":[{"bg-blend":$e()}],filter:[{filter:["","none"]}],blur:[{blur:[n]}],brightness:[{brightness:[r]}],contrast:[{contrast:[u]}],"drop-shadow":[{"drop-shadow":["","none",Ft,z]}],grayscale:[{grayscale:[a]}],"hue-rotate":[{"hue-rotate":[f]}],invert:[{invert:[p]}],saturate:[{saturate:[h]}],sepia:[{sepia:[C]}],"backdrop-filter":[{"backdrop-filter":["","none"]}],"backdrop-blur":[{"backdrop-blur":[n]}],"backdrop-brightness":[{"backdrop-brightness":[r]}],"backdrop-contrast":[{"backdrop-contrast":[u]}],"backdrop-grayscale":[{"backdrop-grayscale":[a]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[f]}],"backdrop-invert":[{"backdrop-invert":[p]}],"backdrop-opacity":[{"backdrop-opacity":[d]}],"backdrop-saturate":[{"backdrop-saturate":[h]}],"backdrop-sepia":[{"backdrop-sepia":[C]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":[l]}],"border-spacing-x":[{"border-spacing-x":[l]}],"border-spacing-y":[{"border-spacing-y":[l]}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["none","all","","colors","opacity","shadow","transform",z]}],duration:[{duration:U()}],ease:[{ease:["linear","in","out","in-out",z]}],delay:[{delay:U()}],animate:[{animate:["none","spin","ping","pulse","bounce",z]}],transform:[{transform:["","gpu","none"]}],scale:[{scale:[S]}],"scale-x":[{"scale-x":[S]}],"scale-y":[{"scale-y":[S]}],rotate:[{rotate:[Or,z]}],"translate-x":[{"translate-x":[_]}],"translate-y":[{"translate-y":[_]}],"skew-x":[{"skew-x":[T]}],"skew-y":[{"skew-y":[T]}],"transform-origin":[{origin:["center","top","top-right","right","bottom-right","bottom","bottom-left","left","top-left",z]}],accent:[{accent:["auto",e]}],appearance:[{appearance:["none","auto"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",z]}],"caret-color":[{caret:[e]}],"pointer-events":[{"pointer-events":["none","auto"]}],resize:[{resize:["none","y","x",""]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":j()}],"scroll-mx":[{"scroll-mx":j()}],"scroll-my":[{"scroll-my":j()}],"scroll-ms":[{"scroll-ms":j()}],"scroll-me":[{"scroll-me":j()}],"scroll-mt":[{"scroll-mt":j()}],"scroll-mr":[{"scroll-mr":j()}],"scroll-mb":[{"scroll-mb":j()}],"scroll-ml":[{"scroll-ml":j()}],"scroll-p":[{"scroll-p":j()}],"scroll-px":[{"scroll-px":j()}],"scroll-py":[{"scroll-py":j()}],"scroll-ps":[{"scroll-ps":j()}],"scroll-pe":[{"scroll-pe":j()}],"scroll-pt":[{"scroll-pt":j()}],"scroll-pr":[{"scroll-pr":j()}],"scroll-pb":[{"scroll-pb":j()}],"scroll-pl":[{"scroll-pl":j()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",z]}],fill:[{fill:[e,"none"]}],"stroke-w":[{stroke:[yt,Dt,jl]}],stroke:[{stroke:[e,"none"]}],sr:["sr-only","not-sr-only"],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]}}},rw=j0(nw);function Sr(...e){return rw(P0(e))}const ow=y0,Zp=m.forwardRef(({className:e,...t},n)=>R.jsx(jp,{ref:n,className:Sr("fixed top-0 z-[100] flex max-h-screen w-full flex-col-reverse p-4 sm:bottom-0 sm:right-0 sm:top-auto sm:flex-col md:max-w-[420px]",e),...t}));Zp.displayName=jp.displayName;const iw=w0("group pointer-events-auto relative flex w-full items-center justify-between space-x-4 overflow-hidden rounded-md border p-6 pr-8 shadow-lg transition-all data-[swipe=cancel]:translate-x-0 data-[swipe=end]:translate-x-[var(--radix-toast-swipe-end-x)] data-[swipe=move]:translate-x-[var(--radix-toast-swipe-move-x)] data-[swipe=move]:transition-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[swipe=end]:animate-out data-[state=closed]:fade-out-80 data-[state=closed]:slide-out-to-right-full data-[state=open]:slide-in-from-top-full data-[state=open]:sm:slide-in-from-bottom-full",{variants:{variant:{default:"border bg-background text-foreground",destructive:"destructive group border-destructive bg-destructive text-destructive-foreground"}},defaultVariants:{variant:"default"}}),Jp=m.forwardRef(({className:e,variant:t,...n},r)=>R.jsx($p,{ref:r,className:Sr(iw({variant:t}),e),...n}));Jp.displayName=$p.displayName;const lw=m.forwardRef(({className:e,...t},n)=>R.jsx(Vp,{ref:n,className:Sr("inline-flex h-8 shrink-0 items-center justify-center rounded-md border bg-transparent px-3 text-sm font-medium ring-offset-background transition-colors hover:bg-secondary focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 group-[.destructive]:border-muted/40 group-[.destructive]:hover:border-destructive/30 group-[.destructive]:hover:bg-destructive group-[.destructive]:hover:text-destructive-foreground group-[.destructive]:focus:ring-destructive",e),...t}));lw.displayName=Vp.displayName;const eh=m.forwardRef(({className:e,...t},n)=>R.jsx(Bp,{ref:n,className:Sr("absolute right-2 top-2 rounded-md p-1 text-foreground/50 opacity-0 transition-opacity hover:text-foreground focus:opacity-100 focus:outline-none focus:ring-2 group-hover:opacity-100 group-[.destructive]:text-red-300 group-[.destructive]:hover:text-red-50 group-[.destructive]:focus:ring-red-400 group-[.destructive]:focus:ring-offset-red-600",e),"toast-close":"",...t,children:R.jsx(k0,{className:"h-4 w-4"})}));eh.displayName=Bp.displayName;const th=m.forwardRef(({className:e,...t},n)=>R.jsx(Up,{ref:n,className:Sr("text-sm font-semibold",e),...t}));th.displayName=Up.displayName;const nh=m.forwardRef(({className:e,...t},n)=>R.jsx(bp,{ref:n,className:Sr("text-sm opacity-90",e),...t}));nh.displayName=bp.displayName;function sw(){const{toasts:e}=Eg();return R.jsxs(ow,{children:[e.map(function({id:t,title:n,description:r,action:o,...i}){return R.jsxs(Jp,{...i,children:[R.jsxs("div",{className:"grid gap-1",children:[n&&R.jsx(th,{children:n}),r&&R.jsx(nh,{children:r})]}),o,R.jsx(eh,{})]},t)}),R.jsx(Zp,{})]})}const uw=m.lazy(()=>cn(()=>import("./not-found-DGDzh5xu.js"),[])),aw=m.lazy(()=>cn(()=>import("./Home-BLQIGW27.js"),__vite__mapDeps([0,1,2,3,4]))),cw=m.lazy(()=>cn(()=>import("./SolutionsPage-CqM2IPgi.js"),__vite__mapDeps([5,1,2,6]))),dw=m.lazy(()=>cn(()=>import("./TermsOfUse-w2eVO6tq.js"),__vite__mapDeps([7,1]))),fw=m.lazy(()=>cn(()=>import("./PrivacyPolicy-D6V83Y8R.js"),__vite__mapDeps([8,1]))),pw=m.lazy(()=>cn(()=>import("./FAQ-DbRXZAMc.js"),__vite__mapDeps([9,1]))),hw=m.lazy(()=>cn(()=>import("./Blog-MGj793nO.js"),__vite__mapDeps([10,1,3,11]))),mw=m.lazy(()=>cn(()=>import("./BlogPost-fit_wI4g.js"),__vite__mapDeps([12,1,6,11]))),vw=()=>R.jsx("div",{className:"flex items-center justify-center min-h-screen bg-gray-50",children:R.jsxs("div",{className:"text-center",children:[R.jsx("div",{className:"w-16 h-16 border-4 border-t-[#2ECC71] border-gray-200 rounded-full animate-spin mx-auto mb-4"}),R.jsx("p",{className:"text-gray-600",children:"Carregando..."})]})});function yw(){return R.jsxs(R.Fragment,{children:[R.jsx(m.Suspense,{fallback:R.jsx(vw,{}),children:R.jsxs(jy,{children:[R.jsx(gt,{path:"/",element:R.jsx(aw,{})}),R.jsx(gt,{path:"/solutions",element:R.jsx(cw,{})}),R.jsx(gt,{path:"/terms-of-use",element:R.jsx(dw,{})}),R.jsx(gt,{path:"/privacy-policy",element:R.jsx(fw,{})}),R.jsx(gt,{path:"/faq",element:R.jsx(pw,{})}),R.jsx(gt,{path:"/blog",element:R.jsx(hw,{})}),R.jsx(gt,{path:"/blog/:slug",element:R.jsx(mw,{})}),R.jsx(gt,{path:"*",element:R.jsx(uw,{})})]})}),R.jsx(sw,{})]})}var rl=class{constructor(){this.listeners=new Set,this.subscribe=this.subscribe.bind(this)}subscribe(e){return this.listeners.add(e),this.onSubscribe(),()=>{this.listeners.delete(e),this.onUnsubscribe()}}hasListeners(){return this.listeners.size>0}onSubscribe(){}onUnsubscribe(){}},ol=typeof window>"u"||"Deno"in globalThis;function Ye(){}function gw(e,t){return typeof e=="function"?e(t):e}function ww(e){return typeof e=="number"&&e>=0&&e!==1/0}function xw(e,t){return Math.max(e+(t||0)-Date.now(),0)}function kc(e,t){return typeof e=="function"?e(t):e}function Sw(e,t){return typeof e=="function"?e(t):e}function Pc(e,t){const{type:n="all",exact:r,fetchStatus:o,predicate:i,queryKey:l,stale:s}=e;if(l){if(r){if(t.queryHash!==Xu(l,t.options))return!1}else if(!po(t.queryKey,l))return!1}if(n!=="all"){const u=t.isActive();if(n==="active"&&!u||n==="inactive"&&u)return!1}return!(typeof s=="boolean"&&t.isStale()!==s||o&&o!==t.state.fetchStatus||i&&!i(t))}function Tc(e,t){const{exact:n,status:r,predicate:o,mutationKey:i}=e;if(i){if(!t.options.mutationKey)return!1;if(n){if(fo(t.options.mutationKey)!==fo(i))return!1}else if(!po(t.options.mutationKey,i))return!1}return!(r&&t.state.status!==r||o&&!o(t))}function Xu(e,t){return((t==null?void 0:t.queryKeyHashFn)||fo)(e)}function fo(e){return JSON.stringify(e,(t,n)=>Bs(n)?Object.keys(n).sort().reduce((r,o)=>(r[o]=n[o],r),{}):n)}function po(e,t){return e===t?!0:typeof e!=typeof t?!1:e&&t&&typeof e=="object"&&typeof t=="object"?Object.keys(t).every(n=>po(e[n],t[n])):!1}function rh(e,t){if(e===t)return e;const n=Rc(e)&&Rc(t);if(n||Bs(e)&&Bs(t)){const r=n?e:Object.keys(e),o=r.length,i=n?t:Object.keys(t),l=i.length,s=n?[]:{};let u=0;for(let a=0;a<l;a++){const f=n?a:i[a];(!n&&r.includes(f)||n)&&e[f]===void 0&&t[f]===void 0?(s[f]=void 0,u++):(s[f]=rh(e[f],t[f]),s[f]===e[f]&&e[f]!==void 0&&u++)}return o===l&&u===o?e:s}return t}function Rc(e){return Array.isArray(e)&&e.length===Object.keys(e).length}function Bs(e){if(!_c(e))return!1;const t=e.constructor;if(t===void 0)return!0;const n=t.prototype;return!(!_c(n)||!n.hasOwnProperty("isPrototypeOf")||Object.getPrototypeOf(e)!==Object.prototype)}function _c(e){return Object.prototype.toString.call(e)==="[object Object]"}function Ew(e){return new Promise(t=>{setTimeout(t,e)})}function Cw(e,t,n){return typeof n.structuralSharing=="function"?n.structuralSharing(e,t):n.structuralSharing!==!1?rh(e,t):t}function kw(e,t,n=0){const r=[...e,t];return n&&r.length>n?r.slice(1):r}function Pw(e,t,n=0){const r=[t,...e];return n&&r.length>n?r.slice(0,-1):r}var qu=Symbol();function oh(e,t){return!e.queryFn&&(t!=null&&t.initialPromise)?()=>t.initialPromise:!e.queryFn||e.queryFn===qu?()=>Promise.reject(new Error(`Missing queryFn: '${e.queryHash}'`)):e.queryFn}var yn,Bt,Jn,Oc,Tw=(Oc=class extends rl{constructor(){super();V(this,yn);V(this,Bt);V(this,Jn);F(this,Jn,t=>{if(!ol&&window.addEventListener){const n=()=>t();return window.addEventListener("visibilitychange",n,!1),()=>{window.removeEventListener("visibilitychange",n)}}})}onSubscribe(){E(this,Bt)||this.setEventListener(E(this,Jn))}onUnsubscribe(){var t;this.hasListeners()||((t=E(this,Bt))==null||t.call(this),F(this,Bt,void 0))}setEventListener(t){var n;F(this,Jn,t),(n=E(this,Bt))==null||n.call(this),F(this,Bt,t(r=>{typeof r=="boolean"?this.setFocused(r):this.onFocus()}))}setFocused(t){E(this,yn)!==t&&(F(this,yn,t),this.onFocus())}onFocus(){const t=this.isFocused();this.listeners.forEach(n=>{n(t)})}isFocused(){var t;return typeof E(this,yn)=="boolean"?E(this,yn):((t=globalThis.document)==null?void 0:t.visibilityState)!=="hidden"}},yn=new WeakMap,Bt=new WeakMap,Jn=new WeakMap,Oc),ih=new Tw,er,Wt,tr,Mc,Rw=(Mc=class extends rl{constructor(){super();V(this,er,!0);V(this,Wt);V(this,tr);F(this,tr,t=>{if(!ol&&window.addEventListener){const n=()=>t(!0),r=()=>t(!1);return window.addEventListener("online",n,!1),window.addEventListener("offline",r,!1),()=>{window.removeEventListener("online",n),window.removeEventListener("offline",r)}}})}onSubscribe(){E(this,Wt)||this.setEventListener(E(this,tr))}onUnsubscribe(){var t;this.hasListeners()||((t=E(this,Wt))==null||t.call(this),F(this,Wt,void 0))}setEventListener(t){var n;F(this,tr,t),(n=E(this,Wt))==null||n.call(this),F(this,Wt,t(this.setOnline.bind(this)))}setOnline(t){E(this,er)!==t&&(F(this,er,t),this.listeners.forEach(r=>{r(t)}))}isOnline(){return E(this,er)}},er=new WeakMap,Wt=new WeakMap,tr=new WeakMap,Mc),Di=new Rw;function _w(){let e,t;const n=new Promise((o,i)=>{e=o,t=i});n.status="pending",n.catch(()=>{});function r(o){Object.assign(n,o),delete n.resolve,delete n.reject}return n.resolve=o=>{r({status:"fulfilled",value:o}),e(o)},n.reject=o=>{r({status:"rejected",reason:o}),t(o)},n}function Nw(e){return Math.min(1e3*2**e,3e4)}function lh(e){return(e??"online")==="online"?Di.isOnline():!0}var sh=class extends Error{constructor(e){super("CancelledError"),this.revert=e==null?void 0:e.revert,this.silent=e==null?void 0:e.silent}};function $l(e){return e instanceof sh}function uh(e){let t=!1,n=0,r=!1,o;const i=_w(),l=y=>{var x;r||(v(new sh(y)),(x=e.abort)==null||x.call(e))},s=()=>{t=!0},u=()=>{t=!1},a=()=>ih.isFocused()&&(e.networkMode==="always"||Di.isOnline())&&e.canRun(),f=()=>lh(e.networkMode)&&e.canRun(),p=y=>{var x;r||(r=!0,(x=e.onSuccess)==null||x.call(e,y),o==null||o(),i.resolve(y))},v=y=>{var x;r||(r=!0,(x=e.onError)==null||x.call(e,y),o==null||o(),i.reject(y))},g=()=>new Promise(y=>{var x;o=d=>{(r||a())&&y(d)},(x=e.onPause)==null||x.call(e)}).then(()=>{var y;o=void 0,r||(y=e.onContinue)==null||y.call(e)}),w=()=>{if(r)return;let y;const x=n===0?e.initialPromise:void 0;try{y=x??e.fn()}catch(d){y=Promise.reject(d)}Promise.resolve(y).then(p).catch(d=>{var T;if(r)return;const c=e.retry??(ol?0:3),h=e.retryDelay??Nw,S=typeof h=="function"?h(n,d):h,C=c===!0||typeof c=="number"&&n<c||typeof c=="function"&&c(n,d);if(t||!C){v(d);return}n++,(T=e.onFail)==null||T.call(e,n,d),Ew(S).then(()=>a()?void 0:g()).then(()=>{t?v(d):w()})})};return{promise:i,cancel:l,continue:()=>(o==null||o(),i),cancelRetry:s,continueRetry:u,canStart:f,start:()=>(f()?w():g().then(w),i)}}var Lw=e=>setTimeout(e,0);function Ow(){let e=[],t=0,n=s=>{s()},r=s=>{s()},o=Lw;const i=s=>{t?e.push(s):o(()=>{n(s)})},l=()=>{const s=e;e=[],s.length&&o(()=>{r(()=>{s.forEach(u=>{n(u)})})})};return{batch:s=>{let u;t++;try{u=s()}finally{t--,t||l()}return u},batchCalls:s=>(...u)=>{i(()=>{s(...u)})},schedule:i,setNotifyFunction:s=>{n=s},setBatchNotifyFunction:s=>{r=s},setScheduler:s=>{o=s}}}var Ee=Ow(),gn,Ic,ah=(Ic=class{constructor(){V(this,gn)}destroy(){this.clearGcTimeout()}scheduleGc(){this.clearGcTimeout(),ww(this.gcTime)&&F(this,gn,setTimeout(()=>{this.optionalRemove()},this.gcTime))}updateGcTime(e){this.gcTime=Math.max(this.gcTime||0,e??(ol?1/0:5*60*1e3))}clearGcTimeout(){E(this,gn)&&(clearTimeout(E(this,gn)),F(this,gn,void 0))}},gn=new WeakMap,Ic),nr,rr,Ue,wn,ye,ho,xn,Xe,wt,Ac,Mw=(Ac=class extends ah{constructor(t){super();V(this,Xe);V(this,nr);V(this,rr);V(this,Ue);V(this,wn);V(this,ye);V(this,ho);V(this,xn);F(this,xn,!1),F(this,ho,t.defaultOptions),this.setOptions(t.options),this.observers=[],F(this,wn,t.client),F(this,Ue,E(this,wn).getQueryCache()),this.queryKey=t.queryKey,this.queryHash=t.queryHash,F(this,nr,Aw(this.options)),this.state=t.state??E(this,nr),this.scheduleGc()}get meta(){return this.options.meta}get promise(){var t;return(t=E(this,ye))==null?void 0:t.promise}setOptions(t){this.options={...E(this,ho),...t},this.updateGcTime(this.options.gcTime)}optionalRemove(){!this.observers.length&&this.state.fetchStatus==="idle"&&E(this,Ue).remove(this)}setData(t,n){const r=Cw(this.state.data,t,this.options);return he(this,Xe,wt).call(this,{data:r,type:"success",dataUpdatedAt:n==null?void 0:n.updatedAt,manual:n==null?void 0:n.manual}),r}setState(t,n){he(this,Xe,wt).call(this,{type:"setState",state:t,setStateOptions:n})}cancel(t){var r,o;const n=(r=E(this,ye))==null?void 0:r.promise;return(o=E(this,ye))==null||o.cancel(t),n?n.then(Ye).catch(Ye):Promise.resolve()}destroy(){super.destroy(),this.cancel({silent:!0})}reset(){this.destroy(),this.setState(E(this,nr))}isActive(){return this.observers.some(t=>Sw(t.options.enabled,this)!==!1)}isDisabled(){return this.getObserversCount()>0?!this.isActive():this.options.queryFn===qu||this.state.dataUpdateCount+this.state.errorUpdateCount===0}isStale(){return this.state.isInvalidated?!0:this.getObserversCount()>0?this.observers.some(t=>t.getCurrentResult().isStale):this.state.data===void 0}isStaleByTime(t=0){return this.state.isInvalidated||this.state.data===void 0||!xw(this.state.dataUpdatedAt,t)}onFocus(){var n;const t=this.observers.find(r=>r.shouldFetchOnWindowFocus());t==null||t.refetch({cancelRefetch:!1}),(n=E(this,ye))==null||n.continue()}onOnline(){var n;const t=this.observers.find(r=>r.shouldFetchOnReconnect());t==null||t.refetch({cancelRefetch:!1}),(n=E(this,ye))==null||n.continue()}addObserver(t){this.observers.includes(t)||(this.observers.push(t),this.clearGcTimeout(),E(this,Ue).notify({type:"observerAdded",query:this,observer:t}))}removeObserver(t){this.observers.includes(t)&&(this.observers=this.observers.filter(n=>n!==t),this.observers.length||(E(this,ye)&&(E(this,xn)?E(this,ye).cancel({revert:!0}):E(this,ye).cancelRetry()),this.scheduleGc()),E(this,Ue).notify({type:"observerRemoved",query:this,observer:t}))}getObserversCount(){return this.observers.length}invalidate(){this.state.isInvalidated||he(this,Xe,wt).call(this,{type:"invalidate"})}fetch(t,n){var u,a,f;if(this.state.fetchStatus!=="idle"){if(this.state.data!==void 0&&(n!=null&&n.cancelRefetch))this.cancel({silent:!0});else if(E(this,ye))return E(this,ye).continueRetry(),E(this,ye).promise}if(t&&this.setOptions(t),!this.options.queryFn){const p=this.observers.find(v=>v.options.queryFn);p&&this.setOptions(p.options)}const r=new AbortController,o=p=>{Object.defineProperty(p,"signal",{enumerable:!0,get:()=>(F(this,xn,!0),r.signal)})},i=()=>{const p=oh(this.options,n),v={client:E(this,wn),queryKey:this.queryKey,meta:this.meta};return o(v),F(this,xn,!1),this.options.persister?this.options.persister(p,v,this):p(v)},l={fetchOptions:n,options:this.options,queryKey:this.queryKey,client:E(this,wn),state:this.state,fetchFn:i};o(l),(u=this.options.behavior)==null||u.onFetch(l,this),F(this,rr,this.state),(this.state.fetchStatus==="idle"||this.state.fetchMeta!==((a=l.fetchOptions)==null?void 0:a.meta))&&he(this,Xe,wt).call(this,{type:"fetch",meta:(f=l.fetchOptions)==null?void 0:f.meta});const s=p=>{var v,g,w,y;$l(p)&&p.silent||he(this,Xe,wt).call(this,{type:"error",error:p}),$l(p)||((g=(v=E(this,Ue).config).onError)==null||g.call(v,p,this),(y=(w=E(this,Ue).config).onSettled)==null||y.call(w,this.state.data,p,this)),this.scheduleGc()};return F(this,ye,uh({initialPromise:n==null?void 0:n.initialPromise,fn:l.fetchFn,abort:r.abort.bind(r),onSuccess:p=>{var v,g,w,y;if(p===void 0){s(new Error(`${this.queryHash} data is undefined`));return}try{this.setData(p)}catch(x){s(x);return}(g=(v=E(this,Ue).config).onSuccess)==null||g.call(v,p,this),(y=(w=E(this,Ue).config).onSettled)==null||y.call(w,p,this.state.error,this),this.scheduleGc()},onError:s,onFail:(p,v)=>{he(this,Xe,wt).call(this,{type:"failed",failureCount:p,error:v})},onPause:()=>{he(this,Xe,wt).call(this,{type:"pause"})},onContinue:()=>{he(this,Xe,wt).call(this,{type:"continue"})},retry:l.options.retry,retryDelay:l.options.retryDelay,networkMode:l.options.networkMode,canRun:()=>!0})),E(this,ye).start()}},nr=new WeakMap,rr=new WeakMap,Ue=new WeakMap,wn=new WeakMap,ye=new WeakMap,ho=new WeakMap,xn=new WeakMap,Xe=new WeakSet,wt=function(t){const n=r=>{switch(t.type){case"failed":return{...r,fetchFailureCount:t.failureCount,fetchFailureReason:t.error};case"pause":return{...r,fetchStatus:"paused"};case"continue":return{...r,fetchStatus:"fetching"};case"fetch":return{...r,...Iw(r.data,this.options),fetchMeta:t.meta??null};case"success":return{...r,data:t.data,dataUpdateCount:r.dataUpdateCount+1,dataUpdatedAt:t.dataUpdatedAt??Date.now(),error:null,isInvalidated:!1,status:"success",...!t.manual&&{fetchStatus:"idle",fetchFailureCount:0,fetchFailureReason:null}};case"error":const o=t.error;return $l(o)&&o.revert&&E(this,rr)?{...E(this,rr),fetchStatus:"idle"}:{...r,error:o,errorUpdateCount:r.errorUpdateCount+1,errorUpdatedAt:Date.now(),fetchFailureCount:r.fetchFailureCount+1,fetchFailureReason:o,fetchStatus:"idle",status:"error"};case"invalidate":return{...r,isInvalidated:!0};case"setState":return{...r,...t.state}}};this.state=n(this.state),Ee.batch(()=>{this.observers.forEach(r=>{r.onQueryUpdate()}),E(this,Ue).notify({query:this,type:"updated",action:t})})},Ac);function Iw(e,t){return{fetchFailureCount:0,fetchFailureReason:null,fetchStatus:lh(t.networkMode)?"fetching":"paused",...e===void 0&&{error:null,status:"pending"}}}function Aw(e){const t=typeof e.initialData=="function"?e.initialData():e.initialData,n=t!==void 0,r=n?typeof e.initialDataUpdatedAt=="function"?e.initialDataUpdatedAt():e.initialDataUpdatedAt:0;return{data:t,dataUpdateCount:0,dataUpdatedAt:n?r??Date.now():0,error:null,errorUpdateCount:0,errorUpdatedAt:0,fetchFailureCount:0,fetchFailureReason:null,fetchMeta:null,isInvalidated:!1,status:n?"success":"pending",fetchStatus:"idle"}}var lt,Dc,Dw=(Dc=class extends rl{constructor(t={}){super();V(this,lt);this.config=t,F(this,lt,new Map)}build(t,n,r){const o=n.queryKey,i=n.queryHash??Xu(o,n);let l=this.get(i);return l||(l=new Mw({client:t,queryKey:o,queryHash:i,options:t.defaultQueryOptions(n),state:r,defaultOptions:t.getQueryDefaults(o)}),this.add(l)),l}add(t){E(this,lt).has(t.queryHash)||(E(this,lt).set(t.queryHash,t),this.notify({type:"added",query:t}))}remove(t){const n=E(this,lt).get(t.queryHash);n&&(t.destroy(),n===t&&E(this,lt).delete(t.queryHash),this.notify({type:"removed",query:t}))}clear(){Ee.batch(()=>{this.getAll().forEach(t=>{this.remove(t)})})}get(t){return E(this,lt).get(t)}getAll(){return[...E(this,lt).values()]}find(t){const n={exact:!0,...t};return this.getAll().find(r=>Pc(n,r))}findAll(t={}){const n=this.getAll();return Object.keys(t).length>0?n.filter(r=>Pc(t,r)):n}notify(t){Ee.batch(()=>{this.listeners.forEach(n=>{n(t)})})}onFocus(){Ee.batch(()=>{this.getAll().forEach(t=>{t.onFocus()})})}onOnline(){Ee.batch(()=>{this.getAll().forEach(t=>{t.onOnline()})})}},lt=new WeakMap,Dc),st,xe,Sn,ut,jt,Fc,Fw=(Fc=class extends ah{constructor(t){super();V(this,ut);V(this,st);V(this,xe);V(this,Sn);this.mutationId=t.mutationId,F(this,xe,t.mutationCache),F(this,st,[]),this.state=t.state||zw(),this.setOptions(t.options),this.scheduleGc()}setOptions(t){this.options=t,this.updateGcTime(this.options.gcTime)}get meta(){return this.options.meta}addObserver(t){E(this,st).includes(t)||(E(this,st).push(t),this.clearGcTimeout(),E(this,xe).notify({type:"observerAdded",mutation:this,observer:t}))}removeObserver(t){F(this,st,E(this,st).filter(n=>n!==t)),this.scheduleGc(),E(this,xe).notify({type:"observerRemoved",mutation:this,observer:t})}optionalRemove(){E(this,st).length||(this.state.status==="pending"?this.scheduleGc():E(this,xe).remove(this))}continue(){var t;return((t=E(this,Sn))==null?void 0:t.continue())??this.execute(this.state.variables)}async execute(t){var i,l,s,u,a,f,p,v,g,w,y,x,d,c,h,S,C,T,k,_;const n=()=>{he(this,ut,jt).call(this,{type:"continue"})};F(this,Sn,uh({fn:()=>this.options.mutationFn?this.options.mutationFn(t):Promise.reject(new Error("No mutationFn found")),onFail:(L,M)=>{he(this,ut,jt).call(this,{type:"failed",failureCount:L,error:M})},onPause:()=>{he(this,ut,jt).call(this,{type:"pause"})},onContinue:n,retry:this.options.retry??0,retryDelay:this.options.retryDelay,networkMode:this.options.networkMode,canRun:()=>E(this,xe).canRun(this)}));const r=this.state.status==="pending",o=!E(this,Sn).canStart();try{if(r)n();else{he(this,ut,jt).call(this,{type:"pending",variables:t,isPaused:o}),await((l=(i=E(this,xe).config).onMutate)==null?void 0:l.call(i,t,this));const M=await((u=(s=this.options).onMutate)==null?void 0:u.call(s,t));M!==this.state.context&&he(this,ut,jt).call(this,{type:"pending",context:M,variables:t,isPaused:o})}const L=await E(this,Sn).start();return await((f=(a=E(this,xe).config).onSuccess)==null?void 0:f.call(a,L,t,this.state.context,this)),await((v=(p=this.options).onSuccess)==null?void 0:v.call(p,L,t,this.state.context)),await((w=(g=E(this,xe).config).onSettled)==null?void 0:w.call(g,L,null,this.state.variables,this.state.context,this)),await((x=(y=this.options).onSettled)==null?void 0:x.call(y,L,null,t,this.state.context)),he(this,ut,jt).call(this,{type:"success",data:L}),L}catch(L){try{throw await((c=(d=E(this,xe).config).onError)==null?void 0:c.call(d,L,t,this.state.context,this)),await((S=(h=this.options).onError)==null?void 0:S.call(h,L,t,this.state.context)),await((T=(C=E(this,xe).config).onSettled)==null?void 0:T.call(C,void 0,L,this.state.variables,this.state.context,this)),await((_=(k=this.options).onSettled)==null?void 0:_.call(k,void 0,L,t,this.state.context)),L}finally{he(this,ut,jt).call(this,{type:"error",error:L})}}finally{E(this,xe).runNext(this)}}},st=new WeakMap,xe=new WeakMap,Sn=new WeakMap,ut=new WeakSet,jt=function(t){const n=r=>{switch(t.type){case"failed":return{...r,failureCount:t.failureCount,failureReason:t.error};case"pause":return{...r,isPaused:!0};case"continue":return{...r,isPaused:!1};case"pending":return{...r,context:t.context,data:void 0,failureCount:0,failureReason:null,error:null,isPaused:t.isPaused,status:"pending",variables:t.variables,submittedAt:Date.now()};case"success":return{...r,data:t.data,failureCount:0,failureReason:null,error:null,status:"success",isPaused:!1};case"error":return{...r,data:void 0,error:t.error,failureCount:r.failureCount+1,failureReason:t.error,isPaused:!1,status:"error"}}};this.state=n(this.state),Ee.batch(()=>{E(this,st).forEach(r=>{r.onMutationUpdate(t)}),E(this,xe).notify({mutation:this,type:"updated",action:t})})},Fc);function zw(){return{context:void 0,data:void 0,error:null,failureCount:0,failureReason:null,isPaused:!1,status:"idle",variables:void 0,submittedAt:0}}var Et,qe,mo,zc,jw=(zc=class extends rl{constructor(t={}){super();V(this,Et);V(this,qe);V(this,mo);this.config=t,F(this,Et,new Set),F(this,qe,new Map),F(this,mo,0)}build(t,n,r){const o=new Fw({mutationCache:this,mutationId:++Po(this,mo)._,options:t.defaultMutationOptions(n),state:r});return this.add(o),o}add(t){E(this,Et).add(t);const n=Qo(t);if(typeof n=="string"){const r=E(this,qe).get(n);r?r.push(t):E(this,qe).set(n,[t])}this.notify({type:"added",mutation:t})}remove(t){if(E(this,Et).delete(t)){const n=Qo(t);if(typeof n=="string"){const r=E(this,qe).get(n);if(r)if(r.length>1){const o=r.indexOf(t);o!==-1&&r.splice(o,1)}else r[0]===t&&E(this,qe).delete(n)}}this.notify({type:"removed",mutation:t})}canRun(t){const n=Qo(t);if(typeof n=="string"){const r=E(this,qe).get(n),o=r==null?void 0:r.find(i=>i.state.status==="pending");return!o||o===t}else return!0}runNext(t){var r;const n=Qo(t);if(typeof n=="string"){const o=(r=E(this,qe).get(n))==null?void 0:r.find(i=>i!==t&&i.state.isPaused);return(o==null?void 0:o.continue())??Promise.resolve()}else return Promise.resolve()}clear(){Ee.batch(()=>{E(this,Et).forEach(t=>{this.notify({type:"removed",mutation:t})}),E(this,Et).clear(),E(this,qe).clear()})}getAll(){return Array.from(E(this,Et))}find(t){const n={exact:!0,...t};return this.getAll().find(r=>Tc(n,r))}findAll(t={}){return this.getAll().filter(n=>Tc(t,n))}notify(t){Ee.batch(()=>{this.listeners.forEach(n=>{n(t)})})}resumePausedMutations(){const t=this.getAll().filter(n=>n.state.isPaused);return Ee.batch(()=>Promise.all(t.map(n=>n.continue().catch(Ye))))}},Et=new WeakMap,qe=new WeakMap,mo=new WeakMap,zc);function Qo(e){var t;return(t=e.options.scope)==null?void 0:t.id}function Nc(e){return{onFetch:(t,n)=>{var f,p,v,g,w;const r=t.options,o=(v=(p=(f=t.fetchOptions)==null?void 0:f.meta)==null?void 0:p.fetchMore)==null?void 0:v.direction,i=((g=t.state.data)==null?void 0:g.pages)||[],l=((w=t.state.data)==null?void 0:w.pageParams)||[];let s={pages:[],pageParams:[]},u=0;const a=async()=>{let y=!1;const x=h=>{Object.defineProperty(h,"signal",{enumerable:!0,get:()=>(t.signal.aborted?y=!0:t.signal.addEventListener("abort",()=>{y=!0}),t.signal)})},d=oh(t.options,t.fetchOptions),c=async(h,S,C)=>{if(y)return Promise.reject();if(S==null&&h.pages.length)return Promise.resolve(h);const T={client:t.client,queryKey:t.queryKey,pageParam:S,direction:C?"backward":"forward",meta:t.options.meta};x(T);const k=await d(T),{maxPages:_}=t.options,L=C?Pw:kw;return{pages:L(h.pages,k,_),pageParams:L(h.pageParams,S,_)}};if(o&&i.length){const h=o==="backward",S=h?$w:Lc,C={pages:i,pageParams:l},T=S(r,C);s=await c(C,T,h)}else{const h=e??i.length;do{const S=u===0?l[0]??r.initialPageParam:Lc(r,s);if(u>0&&S==null)break;s=await c(s,S),u++}while(u<h)}return s};t.options.persister?t.fetchFn=()=>{var y,x;return(x=(y=t.options).persister)==null?void 0:x.call(y,a,{client:t.client,queryKey:t.queryKey,meta:t.options.meta,signal:t.signal},n)}:t.fetchFn=a}}}function Lc(e,{pages:t,pageParams:n}){const r=t.length-1;return t.length>0?e.getNextPageParam(t[r],t,n[r],n):void 0}function $w(e,{pages:t,pageParams:n}){var r;return t.length>0?(r=e.getPreviousPageParam)==null?void 0:r.call(e,t[0],t,n[0],n):void 0}var te,Ht,Qt,or,ir,Kt,lr,sr,jc,Uw=(jc=class{constructor(e={}){V(this,te);V(this,Ht);V(this,Qt);V(this,or);V(this,ir);V(this,Kt);V(this,lr);V(this,sr);F(this,te,e.queryCache||new Dw),F(this,Ht,e.mutationCache||new jw),F(this,Qt,e.defaultOptions||{}),F(this,or,new Map),F(this,ir,new Map),F(this,Kt,0)}mount(){Po(this,Kt)._++,E(this,Kt)===1&&(F(this,lr,ih.subscribe(async e=>{e&&(await this.resumePausedMutations(),E(this,te).onFocus())})),F(this,sr,Di.subscribe(async e=>{e&&(await this.resumePausedMutations(),E(this,te).onOnline())})))}unmount(){var e,t;Po(this,Kt)._--,E(this,Kt)===0&&((e=E(this,lr))==null||e.call(this),F(this,lr,void 0),(t=E(this,sr))==null||t.call(this),F(this,sr,void 0))}isFetching(e){return E(this,te).findAll({...e,fetchStatus:"fetching"}).length}isMutating(e){return E(this,Ht).findAll({...e,status:"pending"}).length}getQueryData(e){var n;const t=this.defaultQueryOptions({queryKey:e});return(n=E(this,te).get(t.queryHash))==null?void 0:n.state.data}ensureQueryData(e){const t=this.defaultQueryOptions(e),n=E(this,te).build(this,t),r=n.state.data;return r===void 0?this.fetchQuery(e):(e.revalidateIfStale&&n.isStaleByTime(kc(t.staleTime,n))&&this.prefetchQuery(t),Promise.resolve(r))}getQueriesData(e){return E(this,te).findAll(e).map(({queryKey:t,state:n})=>{const r=n.data;return[t,r]})}setQueryData(e,t,n){const r=this.defaultQueryOptions({queryKey:e}),o=E(this,te).get(r.queryHash),i=o==null?void 0:o.state.data,l=gw(t,i);if(l!==void 0)return E(this,te).build(this,r).setData(l,{...n,manual:!0})}setQueriesData(e,t,n){return Ee.batch(()=>E(this,te).findAll(e).map(({queryKey:r})=>[r,this.setQueryData(r,t,n)]))}getQueryState(e){var n;const t=this.defaultQueryOptions({queryKey:e});return(n=E(this,te).get(t.queryHash))==null?void 0:n.state}removeQueries(e){const t=E(this,te);Ee.batch(()=>{t.findAll(e).forEach(n=>{t.remove(n)})})}resetQueries(e,t){const n=E(this,te);return Ee.batch(()=>(n.findAll(e).forEach(r=>{r.reset()}),this.refetchQueries({type:"active",...e},t)))}cancelQueries(e,t={}){const n={revert:!0,...t},r=Ee.batch(()=>E(this,te).findAll(e).map(o=>o.cancel(n)));return Promise.all(r).then(Ye).catch(Ye)}invalidateQueries(e,t={}){return Ee.batch(()=>(E(this,te).findAll(e).forEach(n=>{n.invalidate()}),(e==null?void 0:e.refetchType)==="none"?Promise.resolve():this.refetchQueries({...e,type:(e==null?void 0:e.refetchType)??(e==null?void 0:e.type)??"active"},t)))}refetchQueries(e,t={}){const n={...t,cancelRefetch:t.cancelRefetch??!0},r=Ee.batch(()=>E(this,te).findAll(e).filter(o=>!o.isDisabled()).map(o=>{let i=o.fetch(void 0,n);return n.throwOnError||(i=i.catch(Ye)),o.state.fetchStatus==="paused"?Promise.resolve():i}));return Promise.all(r).then(Ye)}fetchQuery(e){const t=this.defaultQueryOptions(e);t.retry===void 0&&(t.retry=!1);const n=E(this,te).build(this,t);return n.isStaleByTime(kc(t.staleTime,n))?n.fetch(t):Promise.resolve(n.state.data)}prefetchQuery(e){return this.fetchQuery(e).then(Ye).catch(Ye)}fetchInfiniteQuery(e){return e.behavior=Nc(e.pages),this.fetchQuery(e)}prefetchInfiniteQuery(e){return this.fetchInfiniteQuery(e).then(Ye).catch(Ye)}ensureInfiniteQueryData(e){return e.behavior=Nc(e.pages),this.ensureQueryData(e)}resumePausedMutations(){return Di.isOnline()?E(this,Ht).resumePausedMutations():Promise.resolve()}getQueryCache(){return E(this,te)}getMutationCache(){return E(this,Ht)}getDefaultOptions(){return E(this,Qt)}setDefaultOptions(e){F(this,Qt,e)}setQueryDefaults(e,t){E(this,or).set(fo(e),{queryKey:e,defaultOptions:t})}getQueryDefaults(e){const t=[...E(this,or).values()],n={};return t.forEach(r=>{po(e,r.queryKey)&&Object.assign(n,r.defaultOptions)}),n}setMutationDefaults(e,t){E(this,ir).set(fo(e),{mutationKey:e,defaultOptions:t})}getMutationDefaults(e){const t=[...E(this,ir).values()],n={};return t.forEach(r=>{po(e,r.mutationKey)&&Object.assign(n,r.defaultOptions)}),n}defaultQueryOptions(e){if(e._defaulted)return e;const t={...E(this,Qt).queries,...this.getQueryDefaults(e.queryKey),...e,_defaulted:!0};return t.queryHash||(t.queryHash=Xu(t.queryKey,t)),t.refetchOnReconnect===void 0&&(t.refetchOnReconnect=t.networkMode!=="always"),t.throwOnError===void 0&&(t.throwOnError=!!t.suspense),!t.networkMode&&t.persister&&(t.networkMode="offlineFirst"),t.queryFn===qu&&(t.enabled=!1),t}defaultMutationOptions(e){return e!=null&&e._defaulted?e:{...E(this,Qt).mutations,...(e==null?void 0:e.mutationKey)&&this.getMutationDefaults(e.mutationKey),...e,_defaulted:!0}}clear(){E(this,te).clear(),E(this,Ht).clear()}},te=new WeakMap,Ht=new WeakMap,Qt=new WeakMap,or=new WeakMap,ir=new WeakMap,Kt=new WeakMap,lr=new WeakMap,sr=new WeakMap,jc),bw=m.createContext(void 0),Vw=({client:e,children:t})=>(m.useEffect(()=>(e.mount(),()=>{e.unmount()}),[e]),R.jsx(bw.Provider,{value:e,children:t}));async function Bw(e){if(!e.ok){const t=await e.text()||e.statusText;throw new Error(`${e.status}: ${t}`)}}const Ww=({on401:e})=>async({queryKey:t})=>{const n=t[0],r=(n.startsWith("http"),n),o=await fetch(r,{credentials:"include"});return e==="returnNull"&&o.status===401?null:(await Bw(o),await o.json())},Hw=new Uw({defaultOptions:{queries:{retry:!1,staleTime:1/0,queryFn:Ww({on401:"throw"}),refetchInterval:!1,refetchOnWindowFocus:!1},mutations:{retry:!1}}});function Ul(){document.querySelectorAll('iframe[src*="youtube.com/embed"]').forEach(t=>{const n=t.getBoundingClientRect();if(n.top>=0&&n.left>=0&&n.bottom<=(window.innerHeight||document.documentElement.clientHeight)&&n.right<=(window.innerWidth||document.documentElement.clientWidth)){const o=t.src;t.src=o}})}function Qw(){window.addEventListener("load",Ul),window.addEventListener("resize",Ul),window.addEventListener("scroll",Ul)}Qw();Jf(document.getElementById("root")).render(R.jsx(Vw,{client:Hw,children:R.jsx(sg,{children:R.jsx(yw,{})})}));export{mp as L,mt as P,Gw as R,k0 as X,Sr as a,w0 as b,C0 as c,Lg as d,Og as e,qg as f,Ie as g,Nn as h,Mt as i,R as j,kp as k,Yw as l,m as r,Ai as u};
