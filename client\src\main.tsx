import { createRoot } from "react-dom/client";
import App from "./App";
import "./index.css";
import "./styles/animations.css"; // Importação do CSS de animações consistentes
import "./styles/mobile-fixes.css"; // Importação do CSS de correções para mobile
import { QueryClientProvider } from "@tanstack/react-query";
import { queryClient } from "./lib/queryClient";
import { BrowserRouter } from "react-router-dom";
import { ensureYouTubeVideos } from "./utils/youtube-helper";

// Garantir que os vídeos do YouTube sejam carregados corretamente
ensureYouTubeVideos();

createRoot(document.getElementById("root")!).render(
  <QueryClientProvider client={queryClient}>
    <BrowserRouter>
      <App />
    </BrowserRouter>
  </QueryClientProvider>
);
