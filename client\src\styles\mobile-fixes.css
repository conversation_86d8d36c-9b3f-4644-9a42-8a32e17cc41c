/**
 * Correções específicas para dispositivos móveis
 * Este arquivo contém ajustes para melhorar a experiência em telas pequenas
 */

/* Ajustes gerais para mobile */
@media (max-width: 768px) {
  /* Reduzir espaçamentos */
  .container {
    padding-left: 1rem !important;
    padding-right: 1rem !important;
  }
  
  /* Ajustar tamanhos de texto */
  h1, .h1 {
    font-size: 2rem !important;
    line-height: 1.2 !important;
  }
  
  h2, .h2 {
    font-size: 1.75rem !important;
    line-height: 1.2 !important;
  }
  
  /* Melhorar espaçamento de seções */
  section {
    padding-top: 3rem !important;
    padding-bottom: 3rem !important;
  }
  
  /* Corrigir tamanho de imagens */
  img, .img, [class*="image"] {
    max-width: 100% !important;
    height: auto !important;
    border: none !important;
    outline: none !important;
  }
  
  /* Melhorar cards */
  .card, .case-card, .solution-card {
    margin-bottom: 1.5rem !important;
  }
  
  /* Ajustar botões */
  button, .btn, a[href], [role="button"] {
    padding: 0.75rem 1.25rem !important;
    font-size: 1rem !important;
  }
  
  /* Corrigir problemas de overflow */
  .overflow-hidden {
    overflow: hidden !important;
  }
  
  /* Melhorar carregamento de imagens */
  .lazy-image {
    opacity: 1 !important;
    transition: opacity 0.3s ease !important;
  }
  
  /* Remover efeitos de hover desnecessários */
  *:hover {
    transform: none !important;
  }
  
  /* Exceções para elementos interativos */
  a:hover, button:hover, .btn:hover {
    transform: translateY(-2px) !important;
  }
}

/* Ajustes específicos para telas muito pequenas */
@media (max-width: 480px) {
  /* Reduzir ainda mais espaçamentos */
  .container {
    padding-left: 0.75rem !important;
    padding-right: 0.75rem !important;
  }

  /* Ajustar tamanhos de texto */
  h1, .h1 {
    font-size: 1.75rem !important;
    line-height: 1.2 !important;
  }

  h2, .h2 {
    font-size: 1.5rem !important;
    line-height: 1.2 !important;
  }

  /* Melhorar espaçamento de seções */
  section {
    padding-top: 2.5rem !important;
    padding-bottom: 2.5rem !important;
  }

  /* Ajustar botões para telas muito pequenas */
  button, .btn, a[href], [role="button"] {
    padding: 0.5rem 1rem !important;
    font-size: 0.875rem !important;
  }
}

/* ===== MELHORIAS CONSERVADORAS MOBILE ===== */

/* Touch targets otimizados */
@media (max-width: 768px) {
  button, .btn, a[href], [role="button"], input, select, textarea {
    min-height: 44px !important;
    min-width: 44px !important;
  }

  /* Melhorar espaçamento entre elementos tocáveis */
  button + button, .btn + .btn, a + a {
    margin-left: 0.5rem !important;
  }

  /* Otimizar formulários para mobile */
  input, textarea, select {
    font-size: 16px !important; /* Previne zoom no iOS */
    padding: 0.75rem !important;
    border-radius: 0.5rem !important;
  }

  /* Melhorar scroll behavior */
  body {
    -webkit-overflow-scrolling: touch;
    overscroll-behavior: contain;
  }

  /* Otimizar viewport */
  html {
    -webkit-text-size-adjust: 100%;
    -ms-text-size-adjust: 100%;
  }

  /* Melhorar performance de scroll */
  * {
    -webkit-transform: translateZ(0);
    transform: translateZ(0);
  }

  /* Otimizar imagens para mobile */
  img {
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
  }

  /* Melhorar legibilidade */
  p, span, div {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  /* Otimizar vídeos para mobile */
  video, iframe {
    max-width: 100% !important;
    height: auto !important;
  }

  /* Melhorar navegação mobile */
  nav a, .nav-link {
    padding: 0.75rem 1rem !important;
    display: block !important;
  }

  /* Otimizar cards para mobile */
  .card, .case-card, .solution-card {
    margin-bottom: 1rem !important;
    padding: 1rem !important;
  }

  /* Melhorar modais em mobile */
  .modal, .dialog {
    margin: 1rem !important;
    max-height: calc(100vh - 2rem) !important;
    overflow-y: auto !important;
  }
}

/* Otimizações específicas para iOS */
@supports (-webkit-touch-callout: none) {
  /* Remove bounce scroll */
  body {
    -webkit-overflow-scrolling: touch;
    overscroll-behavior: none;
  }

  /* Otimizar inputs no iOS */
  input, textarea, select {
    -webkit-appearance: none;
    border-radius: 0.5rem;
  }

  /* Melhorar performance no iOS */
  * {
    -webkit-backface-visibility: hidden;
    backface-visibility: hidden;
  }
}

/* Otimizações para Android */
@media screen and (-webkit-min-device-pixel-ratio: 1) {
  /* Melhorar renderização no Android */
  * {
    -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
  }

  /* Otimizar scroll no Android */
  body {
    overscroll-behavior: contain;
  }
}
